# Flujo de Autenticación OAuth 2.0 - GA4 Insights

## 🔐 Diagrama de Flujo de Autenticación

```mermaid
flowchart TD
    A[Usuario hace clic en extensión] --> B{¿Token válido en storage?}
    B -->|Sí| C[Verificar token con Google]
    B -->|No| D[Mostrar pantalla de login]
    
    C --> E{¿Token válido?}
    E -->|Sí| F[Verificar con backend]
    E -->|No| D
    
    F --> G{¿Usuario existe en backend?}
    G -->|Sí| H[Cargar datos del usuario]
    G -->|No| I[Crear usuario en backend]
    
    D --> J[Usuario hace clic en 'Conectar con Google']
    J --> K[chrome.identity.getAuthToken]
    K --> L{¿Autorización exitosa?}
    
    L -->|Sí| M[Recibir access_token]
    L -->|No| N[Mostrar error de autenticación]
    
    M --> O[Enviar token al backend]
    O --> P[Backend verifica con Google]
    P --> Q{¿Token válido?}
    
    Q -->|Sí| R[Obtener datos del usuario de Google]
    Q -->|No| S[Error: Token inválido]
    
    R --> T[Crear/actualizar usuario en BD]
    T --> U[Generar JWT token]
    U --> V[Devolver datos del usuario + JWT]
    V --> W[Guardar datos en chrome.storage]
    W --> X[Mostrar interfaz principal]
    
    I --> H
    H --> X
    N --> Y[Permitir reintentar]
    S --> Y
    Y --> D
```

## 🎨 Mockups de UI para Autenticación

### 1. Estado Inicial - Sin Autenticación

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [📊 Logo GA4]              │
│                                         │
│         ¡Bienvenido a GA4 Insights!     │
│                                         │
│    Analiza el rendimiento de tu sitio   │
│         web en tiempo real desde        │
│           cualquier página              │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │  [🔵 G] Conectar con Google        │ │
│  └─────────────────────────────────────┘ │
│                                         │
│         🔒 Conexión segura OAuth 2.0    │
│                                         │
│    ✅ Solo lectura de Analytics         │
│    ✅ No almacenamos tu contraseña      │
│    ✅ Puedes revocar acceso en cualquier│
│       momento                           │
│                                         │
│    [?] ¿Qué permisos necesitamos?       │
│                                         │
└─────────────────────────────────────────┘
```

### 2. Estado de Carga - Autenticando

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [🔄 Spinner]               │
│                                         │
│           Conectando con Google...      │
│                                         │
│         ████████████████                │
│                                         │
│    • Verificando credenciales...        │
│    • Obteniendo propiedades GA4...      │
│    • Configurando tu cuenta...          │
│                                         │
│                                         │
│         Por favor, espera un momento    │
│                                         │
│                                         │
│                                         │
│                                         │
│                                         │
│                                         │
└─────────────────────────────────────────┘
```

### 3. Error de Autenticación

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [⚠️ Error]                 │
│                                         │
│         Error de Autenticación          │
│                                         │
│    No se pudo conectar con Google       │
│                                         │
│    Posibles causas:                     │
│    • Permisos denegados                 │
│    • Conexión a internet               │
│    • Bloqueador de popups activo       │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           Intentar de nuevo         │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │         Obtener ayuda               │ │
│  └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

### 4. Selección de Cuenta Google (si múltiples)

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│        Selecciona tu cuenta Google      │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ [👤] <EMAIL>               │ │
│  │      Cuenta empresarial             │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ [👤] <EMAIL>        │ │
│  │      Cuenta personal                │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ [➕] Usar otra cuenta               │ │
│  └─────────────────────────────────────┘ │
│                                         │
│    ℹ️ Selecciona la cuenta que tiene    │
│       acceso a Google Analytics        │
│                                         │
└─────────────────────────────────────────┘
```

### 5. Permisos de Google Analytics

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│           Permisos Requeridos           │
│                                         │
│    GA4 Insights necesita acceso a:      │
│                                         │
│    ✅ Ver datos de Google Analytics     │
│       (Solo lectura)                    │
│                                         │
│    ✅ Información básica del perfil     │
│       (Nombre y email)                  │
│                                         │
│    ❌ NO accedemos a:                   │
│    • Otros datos de Google              │
│    • Configuración de Analytics        │
│    • Datos de otros sitios web         │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           Autorizar                 │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  [Cancelar]                             │
│                                         │
└─────────────────────────────────────────┘
```

### 6. Éxito - Primera Configuración

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [✅ Éxito]                 │
│                                         │
│         ¡Conexión exitosa!              │
│                                         │
│    Hola, Juan 👋                        │
│    <EMAIL>                     │
│                                         │
│    Hemos encontrado 3 propiedades       │
│         de Google Analytics:            │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ 🔽 Selecciona tu propiedad GA4     ▼│ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           Comenzar                  │ │
│  └─────────────────────────────────────┘ │
│                                         │
│    [⚙️] Configurar preferencias         │
│                                         │
└─────────────────────────────────────────┘
```

## 🔧 Implementación Técnica del Flujo

### 1. Verificación de Token Existente
```javascript
// popup.js - Al abrir la extensión
async function checkExistingAuth() {
    Logger.info('Checking existing authentication');
    
    try {
        // Verificar si hay token en storage
        const result = await chrome.storage.local.get(['authToken', 'tokenExpiry']);
        
        if (result.authToken && result.tokenExpiry) {
            const now = Date.now();
            if (now < result.tokenExpiry) {
                Logger.debug('Valid token found in storage');
                return await validateTokenWithGoogle(result.authToken);
            } else {
                Logger.info('Token expired, removing from storage');
                await chrome.storage.local.remove(['authToken', 'tokenExpiry']);
            }
        }
        
        return false;
    } catch (error) {
        Logger.error('Error checking existing auth', { error: error.message });
        return false;
    }
}
```

### 2. Inicio de Autenticación OAuth
```javascript
// auth.js - Iniciar flujo OAuth
async function initiateGoogleAuth() {
    Logger.info('Initiating Google OAuth flow');
    
    try {
        UI.showLoading('Conectando con Google...');
        
        const token = await chrome.identity.getAuthToken({
            interactive: true,
            scopes: [
                'https://www.googleapis.com/auth/analytics.readonly',
                'email',
                'profile'
            ]
        });
        
        if (token) {
            Logger.debug('OAuth token received');
            return await processAuthToken(token);
        } else {
            throw new Error('No token received from Google');
        }
        
    } catch (error) {
        Logger.error('OAuth authentication failed', { 
            error: error.message,
            stack: error.stack 
        });
        
        UI.hideLoading();
        UI.showError('Error de autenticación. Por favor, intenta de nuevo.');
        throw error;
    }
}
```

### 3. Procesamiento del Token
```javascript
// auth.js - Procesar token recibido
async function processAuthToken(googleToken) {
    Logger.info('Processing auth token with backend');
    
    try {
        // Enviar token al backend para verificación
        const response = await fetch(`${CONFIG.BACKEND_URL}/auth/verify-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Request-ID': generateRequestId()
            },
            body: JSON.stringify({
                googleAccessToken: googleToken,
                clientInfo: {
                    extensionVersion: chrome.runtime.getManifest().version,
                    userAgent: navigator.userAgent
                }
            })
        });
        
        if (!response.ok) {
            throw new Error(`Backend verification failed: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            Logger.info('User authenticated successfully', { 
                userId: data.data.userId 
            });
            
            // Guardar datos en storage
            await saveAuthData(data.data);
            
            // Actualizar estado global
            State.setUser(data.data);
            
            return data.data;
        } else {
            throw new Error(data.error.message || 'Backend verification failed');
        }
        
    } catch (error) {
        Logger.error('Token processing failed', { 
            error: error.message 
        });
        throw error;
    }
}
```

### 4. Almacenamiento Seguro
```javascript
// auth.js - Guardar datos de autenticación
async function saveAuthData(userData) {
    Logger.debug('Saving auth data to storage');
    
    try {
        const authData = {
            authToken: userData.jwtToken,
            tokenExpiry: new Date(userData.tokenExpiry).getTime(),
            userId: userData.userId,
            email: userData.email,
            plan: userData.plan,
            lastLogin: Date.now()
        };
        
        await chrome.storage.local.set(authData);
        
        Logger.info('Auth data saved successfully');
    } catch (error) {
        Logger.error('Failed to save auth data', { 
            error: error.message 
        });
        throw error;
    }
}
```

### 5. Manejo de Errores Específicos
```javascript
// auth.js - Manejo de errores de autenticación
function handleAuthError(error) {
    Logger.error('Authentication error occurred', { 
        error: error.message,
        type: error.name 
    });
    
    let userMessage = 'Error de autenticación desconocido';
    let actionButton = 'Reintentar';
    
    if (error.message.includes('User did not approve')) {
        userMessage = 'Permisos denegados. Necesitamos acceso a Google Analytics para funcionar.';
        actionButton = 'Autorizar de nuevo';
    } else if (error.message.includes('network')) {
        userMessage = 'Error de conexión. Verifica tu conexión a internet.';
    } else if (error.message.includes('popup')) {
        userMessage = 'Popup bloqueado. Permite popups para esta extensión.';
    }
    
    UI.showAuthError(userMessage, actionButton);
}
```

## 🔄 Estados de Sesión

### Gestión de Estados:
1. **No autenticado**: Mostrar pantalla de login
2. **Autenticando**: Mostrar loading con progreso
3. **Autenticado**: Mostrar interfaz principal
4. **Token expirado**: Renovar automáticamente o re-autenticar
5. **Error**: Mostrar mensaje específico con opciones

### Persistencia:
- **chrome.storage.local**: Para datos de sesión
- **Expiración automática**: Tokens con TTL
- **Limpieza**: Remover datos al cerrar sesión

Este flujo garantiza una experiencia de autenticación segura, clara y robusta para los usuarios.
