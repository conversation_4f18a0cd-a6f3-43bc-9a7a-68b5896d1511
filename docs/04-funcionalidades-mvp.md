# Funcionalidades MVP - Extensión GA4 Insights

## 🎯 Objetivo del MVP
Crear una extensión de Chrome que permita a los usuarios ver rápidamente las métricas clave de Google Analytics 4 para la página actual y su sitio web completo, con un sistema de planes freemium.

## 🔧 Funcionalidades Principales

### 1. Autenticación y Configuración
- **OAuth 2.0 con Google**: Autenticación segura usando Google Identity
- **Selección de Propiedad GA4**: Lista desplegable con todas las propiedades GA4 accesibles
- **Recordar Configuración**: Guardar última propiedad seleccionada
- **Estado de Conexión**: Indicador visual del estado de autenticación

### 2. Vista "Esta Página" (Página Actual)
- **Detección Automática de URL**: Identificar automáticamente la página activa
- **Métricas Clave de Página**:
  - Usuarios Activos (últimos 7 días)
  - Vistas de Página (últimos 7 días)
  - Tasa de Interacción
  - Duración Promedio de Sesión
  - Tasa de Rebote
- **Fuentes de Tráfico**: Top 5 fuentes que llevan tráfico a esta página
- **Análisis de Dispositivos**: Distribución desktop/mobile/tablet
- **Tendencia Temporal**: Gráfico simple de los últimos 7 días

### 3. Vista "Sitio Completo" (Propiedad GA4)
- **Métricas Generales del Sitio**:
  - Usuarios Activos (últimos 28 días)
  - Usuarios Nuevos
  - Sesiones Totales
  - Tasa de Interacción General
  - Conversiones (si están configuradas)
- **Top Páginas**: Las 10 páginas más visitadas
- **Top Fuentes de Tráfico**: Las 10 principales fuentes
- **Distribución Geográfica**: Top 5 países
- **Tendencia Temporal**: Gráfico de los últimos 28 días

### 4. Interfaz de Usuario
- **Popup Compacto**: Diseño optimizado para el popup de extensión (400x600px aprox.)
- **Navegación por Pestañas**: Alternar fácilmente entre "Esta Página" y "Sitio Completo"
- **Indicadores Visuales**: 
  - Estados de carga
  - Mensajes de error claros
  - Indicadores de datos sin información
- **Gráficos Simples**: Usar Chart.js para visualizaciones básicas
- **Responsive**: Adaptable a diferentes tamaños de popup

### 5. Sistema de Planes (Freemium)
#### Plan Gratuito:
- **Límite de Consultas**: 50 consultas a la API de GA4 por mes
- **Funcionalidades Básicas**:
  - Vista "Esta Página" con métricas básicas
  - Vista "Sitio Completo" con datos limitados
  - Períodos de tiempo fijos (7 días para página, 28 días para sitio)
  - Sin comparaciones de períodos
- **Restricciones**:
  - Máximo 30 días de datos históricos
  - Sin exportación de datos
  - Sin filtros avanzados

#### Plan Premium (Futuro):
- **Consultas Ilimitadas** a la API de GA4
- **Funcionalidades Avanzadas**:
  - Comparación de períodos (período anterior, año anterior)
  - Rangos de fechas personalizados (hasta 1 año)
  - Filtros avanzados por segmentos
  - Exportación de datos (CSV/PDF)
  - Alertas y notificaciones
  - Análisis de eventos personalizados

### 6. Gestión de Datos y Cache
- **Cache Local**: Almacenar datos en `chrome.storage.local` para reducir llamadas a la API
- **Duración de Cache**:
  - Datos recientes: 15 minutos
  - Datos históricos: 1 hora
  - Resúmenes de cuenta: 1 hora
- **Invalidación Inteligente**: Limpiar cache al cambiar de propiedad o período
- **Gestión de Cuotas**: Tracking del uso de API para planes gratuitos

### 7. Manejo de Errores y Estados
- **Estados de Carga**: Spinners y mensajes de "Cargando datos..."
- **Errores de Autenticación**: Guías claras para reautenticarse
- **Errores de API**: Mensajes informativos sobre límites de cuota
- **Sin Datos**: Mensajes explicativos cuando no hay datos disponibles
- **Conectividad**: Manejo de errores de red

## 🚫 Funcionalidades NO Incluidas en MVP

### Excluidas para Simplificar:
- **Alertas y Notificaciones**: Demasiado complejo para MVP
- **Múltiples Propiedades Simultáneas**: Una propiedad a la vez
- **Análisis de Cohortes**: Muy avanzado para usuarios iniciales
- **Integración con Otras Herramientas**: Enfoque solo en GA4
- **Análisis de Embudos**: Requiere configuración compleja
- **Datos en Tiempo Real**: GA4 tiene latencia natural de 24-48h
- **Configuración Avanzada de Eventos**: Solo eventos estándar
- **Análisis de Atribución**: Muy complejo para MVP

### Reservadas para Versiones Futuras:
- **Dashboard Personalizable**: Permitir reorganizar widgets
- **Reportes Programados**: Envío automático de reportes
- **Análisis de Competencia**: Comparar con benchmarks de industria
- **Integración con Google Ads**: Datos de campañas publicitarias
- **API Propia**: Para que terceros integren con la extensión

## 📊 Métricas de Éxito del MVP

### Métricas de Adopción:
- **Instalaciones**: Meta de 1,000 instalaciones en los primeros 3 meses
- **Usuarios Activos**: 70% de usuarios que usan la extensión al menos 1 vez por semana
- **Retención**: 50% de usuarios siguen activos después de 30 días

### Métricas de Uso:
- **Consultas por Usuario**: Promedio de 20 consultas por usuario por mes (plan gratuito)
- **Conversión a Premium**: 5% de usuarios gratuitos se convierten a premium
- **Tiempo de Uso**: Promedio de 2 minutos por sesión en la extensión

### Métricas de Calidad:
- **Rating en Chrome Web Store**: Mantener 4+ estrellas
- **Errores**: Menos del 5% de consultas resultan en error
- **Tiempo de Carga**: Datos mostrados en menos de 3 segundos

## 🔄 Flujo de Usuario Típico

1. **Primera Vez**:
   - Instalar extensión desde Chrome Web Store
   - Hacer clic en icono de extensión
   - Autenticarse con Google
   - Seleccionar propiedad GA4
   - Ver datos de la página actual

2. **Uso Diario**:
   - Navegar a una página de interés
   - Hacer clic en extensión
   - Ver métricas de "Esta Página"
   - Alternar a "Sitio Completo" si es necesario
   - Cerrar popup

3. **Análisis Semanal**:
   - Abrir extensión en página principal del sitio
   - Revisar vista "Sitio Completo"
   - Analizar tendencias y fuentes de tráfico
   - Identificar páginas de mejor rendimiento

## 🎨 Principios de Diseño

- **Simplicidad**: Información clara y accionable sin abrumar
- **Velocidad**: Datos disponibles en segundos, no minutos
- **Contextualidad**: Relevante para lo que el usuario está viendo
- **Confiabilidad**: Funciona consistentemente sin errores
- **Accesibilidad**: Usable por personas con diferentes habilidades técnicas
