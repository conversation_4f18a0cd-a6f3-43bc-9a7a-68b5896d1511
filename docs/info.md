* **PROMPT: Generar Plantilla `manifest.json`**
    * **INSTRUCCIÓN:** Genera una plantilla base para `manifest.json` incluyendo:
        * `manifest_version: 3`
        * `name: "GA4 Insights"` (o similar, permite personalización)
        * `version: "0.1.0"`
        * `description: "Extensión para analizar datos de Google Analytics 4."`
        * `permissions: ["identity", "storage", "activeTab", "scripting", "alarms"]` (considerar `alarms` para tareas periódicas como refresco de token o caché)
        * `host_permissions: ["https://*.google.com/", "https://*.googleapis.com/", "<URL_DEL_BACKEND_PROPIO>"]`
        * `action: { "default_popup": "popup.html", "default_icon": { "16": "icons/icon16.png", ... } }`
        * `icons: { "16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png" }`
        * `oauth2: { "client_id": "TU_CLIENT_ID_DE_GOOGLE_CLOUD.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/analytics.readonly", "email", "profile"] }`
        * (Opcional) `background: { "service_worker": "background.js" }` (si se necesita lógica de fondo persistente).
    * `OUTPUT_FORMAT: JSON (contenido del manifest.json)`

* **PROMPT: Generar Estructura Base de Archivos JS**
    * **INSTRUCCIÓN:** Para cada archivo JS principal (`popup.js`, `auth.js`, `api.js`, `ui.js`, `features.js`, `state.js`, `utils.js`, `config.js`), genera una estructura base con comentarios indicando el propósito del archivo y las funciones principales que contendrá.
    * Ejemplo para `api.js`:
        ```javascript
        // api.js - Funciones para interactuar con la API de GA4 y el backend propio

        // Constante para la URL base del backend (desde config.js o state.js)
        // const BACKEND_URL = ...;

        /**
         * Obtiene el token de acceso de Google (puede ser desde auth.js o state.js)
         * async function getGoogleAuthToken() { ... }
         */

        /**
         * Realiza una llamada genérica a la API de Google protegida con OAuth.
         * @param {string} url - El endpoint de la API.
         * @param {string} accessToken - El token de acceso.
         * @param {object} options - Opciones para fetch (method, headers, body).
         * @returns {Promise<object>} - La respuesta JSON de la API.
         */
        // async function fetchGoogleAPI(url, accessToken, options) { ... }

        /**
         * Obtiene los resúmenes de cuentas de GA4 para el usuario.
         * @param {string} accessToken
         * @returns {Promise<Array>}
         */
        // async function fetchGA4AccountSummaries(accessToken) { ... }

        /**
         * Ejecuta un informe en la API de Datos de GA4.
         * @param {string} accessToken
         * @param {string} propertyId - Ej. "properties/12345"
         * @param {object} reportRequest - Cuerpo de la solicitud del informe.
         * @returns {Promise<object>}
         */
        // async function runGA4Report(accessToken, propertyId, reportRequest) { ... }

        /**
         * Verifica el token de Google con el backend propio.
         * @param {string} googleAccessToken
         * @returns {Promise<object>} - Datos del usuario y plan desde el backend.
         */
        // async function verifyTokenWithBackend(googleAccessToken) { ... }

        /**
         * Notifica al backend sobre el uso de una funcionalidad (para planes gratuitos).
         * @param {string} userApiToken - Token propio del backend para identificar al usuario.
         * @returns {Promise<object>} - Nuevo estado de uso.
         */
        // async function confirmUsageWithBackend(userApiToken) { ... }
        ```
    * `OUTPUT_FORMAT: Múltiples bloques de código JavaScript con la estructura solicitada.`

* **PROMPT: Diseñar Esquema de Base de Datos (Backend)**
    * **INSTRUCCIÓN:** Define el esquema para una base de datos relacional (ej. PostgreSQL) o NoSQL (ej. Firestore) para gestionar usuarios y sus planes.
    * **Tabla/Colección `users`:**
        * `userId` (String, PK)
        * `googleId` (String, Indexado, Único)
        * `email` (String, Indexado)
        * `planId` (String, FK a `plans`/Referencia) - ej: "free", "premium_monthly"
        * `stripeCustomerId` (String, Opcional)
        * `subscriptionId` (String, Opcional)
        * `subscriptionStatus` (String, Opcional) - ej: "active", "canceled", "past_due"
        * `usageCount` (Number, Default: 0) - Para el plan actual
        * `currentPeriodStart` (Timestamp)
        * `currentPeriodEnd` (Timestamp)
        * `createdAt` (Timestamp)
        * `updatedAt` (Timestamp)
    * **Tabla/Colección `plans`:**
        * `planId` (String, PK) - ej: "free", "premium_monthly"
        * `name` (String) - ej: "Plan Gratuito", "Plan Premium Mensual"
        * `price` (Number, Opcional)
        * `currency` (String, Opcional) - ej: "USD"
        * `apiCallLimit` (Number, Nullable) - Límite de llamadas a la API de GA4 por periodo. `null` para ilimitado.
        * `features` (Object/JSON) - ej: `{"canCompareDates": true, "advancedFilters": false, "maxDateRangeDays": 28}`
    * `OUTPUT_FORMAT: SQL DDL para relacional, o estructura JSON para NoSQL (Firestore).`

* **PROMPT: Definir Estructura de Configuración (`config.js` o similar)**
    * **INSTRUCCIÓN:** Define las constantes de configuración que se necesitarán en el frontend.
        ```javascript
        // config.js
        // const GOOGLE_CLIENT_ID = "TU_CLIENT_ID_DE_GOOGLE_CLOUD.apps.googleusercontent.com"; // Reemplazar en producción
        // const GA4_ADMIN_API_BASE_URL = "[https://analyticsadmin.googleapis.com/v1beta](https://analyticsadmin.googleapis.com/v1beta)";
        // const GA4_DATA_API_BASE_URL = "[https://analyticsdata.googleapis.com/v1beta](https://analyticsdata.googleapis.com/v1beta)";
        // const BACKEND_API_BASE_URL = "[https://tu-backend.com/api](https://tu-backend.com/api)"; // URL de tu backend
        // const CACHE_DURATION_GA4_DATA_MS = 15 * 60 * 1000; // 15 minutos para datos de GA4
        // const CACHE_DURATION_ACCOUNT_SUMMARIES_MS = 60 * 60 * 1000; // 1 hora para resúmenes de cuenta
        ```
    * `OUTPUT_FORMAT: Código JavaScript.`
