# Almacenamiento Local y Estrategia de Cache - GA4 Insights

## 🗄️ Estructura de chrome.storage.local

### Principios de Almacenamiento:
- **Seguridad**: No almacenar datos sensibles sin cifrar
- **Eficiencia**: <PERSON><PERSON><PERSON> el tamaño de datos almacenados
- **TTL**: Todos los datos con tiempo de vida definido
- **Limpieza**: Purga automática de datos expirados

## 📊 Datos Almacenados en chrome.storage.local

### 1. Autenticación y Usuario
```javascript
// Estructura de datos de autenticación
const authData = {
  // Token de autenticación (JWT del backend)
  "ga4_auth_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "ga4_token_expiry": *************, // timestamp
  
  // Información del usuario
  "ga4_user_data": {
    userId: "user_123456",
    email: "<EMAIL>",
    name: "<PERSON>",
    avatarUrl: "https://lh3.googleusercontent.com/...",
    plan: {
      planId: "free",
      name: "Plan Gratuito",
      apiCallLimit: 50,
      features: {
        canCompareDates: false,
        advancedFilters: false,
        maxDateRangeDays: 30,
        exportData: false
      }
    },
    usage: {
      usageCount: 15,
      usageLimit: 50,
      resetDate: "2024-02-01T00:00:00Z"
    },
    lastLogin: *************
  },
  "ga4_user_data_expiry": ************* // 24 horas
};
```

### 2. Configuración de Propiedades GA4
```javascript
// Propiedades GA4 del usuario
const ga4Properties = {
  "ga4_properties_list": [
    {
      propertyId: "properties/*********",
      propertyName: "Mi Sitio Web",
      accountId: "accounts/12345",
      accountName: "Mi Empresa",
      websiteUrl: "https://ejemplo.com",
      accessLevel: "read",
      isDefault: true,
      lastAccessed: *************
    },
    {
      propertyId: "properties/*********",
      propertyName: "Blog Personal",
      accountId: "accounts/54321",
      accountName: "Personal",
      websiteUrl: "https://blog.ejemplo.com",
      accessLevel: "read",
      isDefault: false,
      lastAccessed: *************
    }
  ],
  "ga4_properties_expiry": *************, // 24 horas
  
  // Propiedad actualmente seleccionada
  "ga4_selected_property": {
    propertyId: "properties/*********",
    propertyName: "Mi Sitio Web",
    selectedAt: *************
  }
};
```

### 3. Preferencias del Usuario
```javascript
// Configuraciones personalizadas
const userPreferences = {
  "ga4_user_preferences": {
    // Configuración de dimensiones
    defaultDimension: "pagePath", // pagePath, landingPage, exitPage
    
    // Configuración de períodos
    defaultDateRangePageView: "7daysAgo", // Para vista de página
    defaultDateRangeSiteView: "28daysAgo", // Para vista de sitio
    
    // Configuración de UI
    language: "es",
    theme: "light", // light, dark, auto
    compactMode: false,
    
    // Configuración de notificaciones
    showUsageWarnings: true,
    showTrendIndicators: true,
    
    // Configuración avanzada
    cacheEnabled: true,
    debugMode: false,
    
    // Última actualización
    updatedAt: *************
  },
  "ga4_preferences_expiry": 2592000000 // 30 días
};
```

### 4. Cache de Datos GA4
```javascript
// Cache de consultas a la API de GA4
const ga4Cache = {
  // Cache de métricas de página
  "ga4_cache_page_metrics_hash123": {
    data: {
      metrics: [
        { name: "activeUsers", value: 142, change: 12 },
        { name: "screenPageViews", value: 89, change: -5 },
        { name: "engagementRate", value: 68, change: 3 }
      ],
      dateRange: { start: "7daysAgo", end: "yesterday" },
      pagePath: "/productos/categoria-a",
      dimension: "pagePath"
    },
    metadata: {
      propertyId: "properties/*********",
      queryHash: "hash123",
      requestId: "req_789012",
      cachedAt: *************,
      expiresAt: 1704068100000 // 15 minutos después
    }
  },
  
  // Cache de fuentes de tráfico
  "ga4_cache_traffic_sources_hash456": {
    data: {
      sources: [
        { source: "google/organic", users: 64, percentage: 45 },
        { source: "email/newsletter", users: 33, percentage: 23 },
        { source: "(direct)/(none)", users: 26, percentage: 18 }
      ],
      totalUsers: 142
    },
    metadata: {
      propertyId: "properties/*********",
      queryHash: "hash456",
      cachedAt: *************,
      expiresAt: 1704068100000
    }
  },
  
  // Cache de resumen del sitio
  "ga4_cache_site_overview_hash789": {
    data: {
      summary: {
        activeUsers: 2847,
        newUsers: 1203,
        sessions: 4156,
        engagementRate: 72,
        conversions: 89
      },
      trends: [
        { date: "2024-01-08", users: 98 },
        { date: "2024-01-09", users: 112 },
        { date: "2024-01-10", users: 134 }
      ]
    },
    metadata: {
      propertyId: "properties/*********",
      queryHash: "hash789",
      cachedAt: *************,
      expiresAt: 1704070800000 // 1 hora después
    }
  }
};
```

### 5. Estado de la Aplicación
```javascript
// Estado temporal de la aplicación
const appState = {
  "ga4_app_state": {
    // Estado actual de la UI
    currentView: "page_analysis", // page_analysis, site_overview
    currentUrl: "https://ejemplo.com/productos/categoria-a",
    currentPagePath: "/productos/categoria-a",
    
    // Estado de carga
    isLoading: false,
    lastError: null,
    lastRefresh: *************,
    
    // Configuración temporal
    selectedDimension: "pagePath",
    selectedDateRange: {
      start: "7daysAgo",
      end: "yesterday",
      label: "Últimos 7 días"
    },
    
    // Debug info
    debugLogs: [], // Solo en modo debug
    requestCount: 15,
    
    // Timestamp
    stateUpdatedAt: *************
  },
  "ga4_app_state_expiry": 86400000 // 24 horas
};
```

## 🔧 Implementación de Cache

### Cache Manager
```javascript
// cache.js - Gestor de cache
class CacheManager {
    constructor() {
        this.prefix = 'ga4_cache_';
        this.maxCacheSize = 5 * 1024 * 1024; // 5MB límite de Chrome
        
        Logger.debug('CacheManager initialized');
    }
    
    // Generar clave de cache
    generateCacheKey(query, propertyId) {
        const queryString = JSON.stringify(query);
        const hash = this.simpleHash(queryString + propertyId);
        const key = `${this.prefix}${hash}`;
        
        Logger.debug('Cache key generated', { 
            key: key.substring(0, 30) + '...',
            propertyId 
        });
        
        return key;
    }
    
    // Almacenar en cache
    async set(key, data, ttlMs = 900000) { // 15 minutos por defecto
        try {
            const cacheEntry = {
                data,
                metadata: {
                    cachedAt: Date.now(),
                    expiresAt: Date.now() + ttlMs,
                    size: JSON.stringify(data).length
                }
            };
            
            await chrome.storage.local.set({ [key]: cacheEntry });
            
            Logger.debug('Data cached successfully', { 
                key: key.substring(0, 30) + '...',
                size: cacheEntry.metadata.size,
                ttl: ttlMs 
            });
            
            // Limpiar cache si es necesario
            await this.cleanupIfNeeded();
            
        } catch (error) {
            Logger.error('Failed to cache data', { 
                key, 
                error: error.message 
            });
        }
    }
    
    // Obtener del cache
    async get(key) {
        try {
            const result = await chrome.storage.local.get(key);
            const cacheEntry = result[key];
            
            if (!cacheEntry) {
                Logger.debug('Cache miss', { key: key.substring(0, 30) + '...' });
                return null;
            }
            
            // Verificar expiración
            if (Date.now() > cacheEntry.metadata.expiresAt) {
                Logger.debug('Cache expired, removing', { key });
                await chrome.storage.local.remove(key);
                return null;
            }
            
            Logger.debug('Cache hit', { 
                key: key.substring(0, 30) + '...',
                age: Date.now() - cacheEntry.metadata.cachedAt 
            });
            
            return cacheEntry.data;
            
        } catch (error) {
            Logger.error('Failed to get cached data', { 
                key, 
                error: error.message 
            });
            return null;
        }
    }
    
    // Limpiar cache expirado
    async cleanup() {
        Logger.info('Starting cache cleanup');
        
        try {
            const allData = await chrome.storage.local.get(null);
            const now = Date.now();
            const keysToRemove = [];
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const entry = allData[key];
                    if (entry.metadata && now > entry.metadata.expiresAt) {
                        keysToRemove.push(key);
                    }
                }
            });
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                Logger.info('Cache cleanup completed', { 
                    removedKeys: keysToRemove.length 
                });
            }
            
        } catch (error) {
            Logger.error('Cache cleanup failed', { error: error.message });
        }
    }
    
    // Limpiar si el cache está muy lleno
    async cleanupIfNeeded() {
        const usage = await this.getCacheUsage();
        
        if (usage.totalSize > this.maxCacheSize * 0.8) { // 80% del límite
            Logger.warn('Cache size approaching limit, cleaning up', { 
                currentSize: usage.totalSize,
                limit: this.maxCacheSize 
            });
            
            await this.cleanup();
            await this.removeOldestEntries(10); // Remover 10 entradas más antiguas
        }
    }
    
    // Obtener estadísticas de uso del cache
    async getCacheUsage() {
        try {
            const allData = await chrome.storage.local.get(null);
            let totalSize = 0;
            let cacheEntries = 0;
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const entry = allData[key];
                    if (entry.metadata) {
                        totalSize += entry.metadata.size || 0;
                        cacheEntries++;
                    }
                }
            });
            
            return { totalSize, cacheEntries };
            
        } catch (error) {
            Logger.error('Failed to get cache usage', { error: error.message });
            return { totalSize: 0, cacheEntries: 0 };
        }
    }
    
    // Hash simple para generar claves
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convertir a 32bit integer
        }
        return Math.abs(hash).toString(36);
    }
}

// Instancia global
const Cache = new CacheManager();
```

## ⏰ Estrategia de TTL (Time To Live)

### Duración por Tipo de Dato:
```javascript
const TTL_CONFIG = {
    // Autenticación
    AUTH_TOKEN: 24 * 60 * 60 * 1000,        // 24 horas
    USER_DATA: 24 * 60 * 60 * 1000,         // 24 horas
    
    // Configuración
    PROPERTIES_LIST: 24 * 60 * 60 * 1000,   // 24 horas
    USER_PREFERENCES: 30 * 24 * 60 * 60 * 1000, // 30 días
    
    // Cache de datos GA4
    RECENT_DATA: 15 * 60 * 1000,            // 15 minutos (datos recientes)
    HISTORICAL_DATA: 60 * 60 * 1000,        // 1 hora (datos históricos)
    ACCOUNT_SUMMARIES: 60 * 60 * 1000,      // 1 hora
    
    // Estado de aplicación
    APP_STATE: 24 * 60 * 60 * 1000,         // 24 horas
    DEBUG_LOGS: 60 * 60 * 1000              // 1 hora
};
```

### Invalidación de Cache:
```javascript
// cache.js - Estrategias de invalidación
class CacheInvalidation {
    // Invalidar al cambiar de propiedad
    static async onPropertyChange(newPropertyId) {
        Logger.info('Invalidating cache due to property change', { newPropertyId });
        
        const allData = await chrome.storage.local.get(null);
        const keysToRemove = [];
        
        Object.keys(allData).forEach(key => {
            if (key.startsWith('ga4_cache_')) {
                const entry = allData[key];
                if (entry.metadata && entry.metadata.propertyId !== newPropertyId) {
                    keysToRemove.push(key);
                }
            }
        });
        
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            Logger.debug('Cache invalidated', { removedKeys: keysToRemove.length });
        }
    }
    
    // Invalidar al cambiar período de tiempo
    static async onDateRangeChange() {
        Logger.info('Invalidating cache due to date range change');
        
        // Solo invalidar cache de datos, no configuración
        const pattern = /^ga4_cache_(page_metrics|traffic_sources|site_overview)/;
        await this.invalidateByPattern(pattern);
    }
    
    // Invalidar por patrón
    static async invalidateByPattern(pattern) {
        const allData = await chrome.storage.local.get(null);
        const keysToRemove = Object.keys(allData).filter(key => pattern.test(key));
        
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            Logger.debug('Pattern-based cache invalidation', { 
                pattern: pattern.toString(),
                removedKeys: keysToRemove.length 
            });
        }
    }
}
```

## 🧹 Limpieza y Mantenimiento

### Limpieza Automática:
- **Al iniciar**: Limpiar datos expirados
- **Cada hora**: Verificar tamaño del cache
- **Al cerrar**: Opcional, limpiar datos temporales

### Monitoreo:
- **Tamaño total**: Alertar si supera 4MB
- **Entradas**: Máximo 100 entradas de cache
- **Errores**: Log de fallos de cache

Esta estrategia asegura un almacenamiento eficiente y seguro de datos en la extensión.
