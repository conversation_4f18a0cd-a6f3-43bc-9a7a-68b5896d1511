# Arquitectura del Sistema - GA4 Insights Extension

## 🏗️ Visión General de la Arquitectura

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Chrome Tab    │    │   Extension      │    │   Backend API   │
│                 │    │   (Frontend)     │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │   Website   │ │    │ │    Popup     │ │    │ │   Node.js   │ │
│ │             │ │    │ │   (popup.html)│ │    │ │   Express   │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Chrome APIs     │    │ Google APIs      │    │   Database      │
│ - tabs.query    │    │ - GA4 Data API   │    │   (Firestore)   │
│ - storage.local │    │ - GA4 Admin API  │    │                 │
│ - identity      │    │ - OAuth 2.0      │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔄 Flujo de Comunicación Detallado

### 1. Inicialización de la Extensión

```mermaid
sequenceDiagram
    participant U as Usuario
    participant P as Popup
    participant A as Auth.js
    participant S as State.js
    participant B as Backend
    participant G as Google APIs

    U->>P: Hace clic en extensión
    P->>S: initializeExtension()
    S->>A: checkAuthStatus()
    
    alt Usuario no autenticado
        A->>G: chrome.identity.getAuthToken()
        G-->>A: accessToken
        A->>B: /verify-token
        B-->>A: userData + planInfo
        A->>S: updateAuthState()
    else Usuario ya autenticado
        S->>S: loadCachedData()
    end
    
    S->>P: updateUI()
    P->>U: Mostrar interfaz
```

### 2. Análisis de Página Actual

```mermaid
sequenceDiagram
    participant P as Popup
    participant U as Utils.js
    participant A as API.js
    participant G as GA4 APIs
    participant C as Cache
    participant L as Logger

    P->>U: getCurrentTabUrl()
    U-->>P: currentUrl
    P->>L: log('Analyzing page:', currentUrl)
    
    P->>A: getPageAnalytics(url, dimension, metrics)
    A->>L: log('API request:', {url, dimension, metrics})
    A->>C: getCachedData(cacheKey)
    
    alt Datos en cache válidos
        C-->>A: cachedData
        A->>L: log('Using cached data')
    else Cache expirado o no existe
        A->>G: runReport(reportRequest)
        G-->>A: reportResponse
        A->>L: log('API response:', reportResponse)
        A->>C: setCachedData(cacheKey, data)
    end
    
    A-->>P: processedData
    P->>P: renderPageAnalytics()
```

### 3. Gestión de Planes y Límites

```mermaid
sequenceDiagram
    participant A as API.js
    participant B as Backend
    participant D as Database
    participant L as Logger

    A->>L: log('Checking usage limits')
    A->>B: /confirm-usage
    B->>D: getUserUsage(userId)
    D-->>B: currentUsage
    
    alt Límite no alcanzado
        B->>D: incrementUsage(userId)
        B-->>A: {allowed: true, remaining: X}
        A->>L: log('Usage confirmed, remaining:', X)
    else Límite alcanzado
        B-->>A: {allowed: false, limitReached: true}
        A->>L: log('Usage limit reached')
        A->>A: showUpgradePrompt()
    end
```

## 📁 Estructura de Archivos y Carpetas

```
ga4-insights-extension/
├── manifest.json                 # Configuración de la extensión
├── popup.html                   # Interfaz principal
├── popup.css                    # Estilos del popup
├── js/
│   ├── popup.js                 # Lógica principal del popup
│   ├── auth.js                  # Gestión de autenticación OAuth
│   ├── api.js                   # Comunicación con APIs (GA4 + Backend)
│   ├── ui.js                    # Manipulación de DOM y UI
│   ├── features.js              # Procesamiento y visualización de datos
│   ├── state.js                 # Gestión de estado global
│   ├── utils.js                 # Funciones utilitarias
│   ├── config.js                # Configuración y constantes
│   ├── logger.js                # Sistema de logging y debug
│   └── i18n.js                  # Internacionalización
├── css/
│   ├── popup.css                # Estilos principales
│   ├── components.css           # Estilos de componentes
│   └── themes.css               # Temas y variables CSS
├── icons/
│   ├── icon16.png              # Icono 16x16
│   ├── icon48.png              # Icono 48x48
│   └── icon128.png             # Icono 128x128
├── locales/
│   ├── en.json                 # Textos en inglés
│   └── es.json                 # Textos en español
├── lib/
│   └── chart.min.js            # Biblioteca Chart.js
└── background.js               # Service worker (si es necesario)
```

## 🔧 Arquitectura del Backend

### Stack Tecnológico:
- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Base de Datos**: Firebase Firestore
- **Autenticación**: Google OAuth 2.0
- **Hosting**: Google Cloud Run
- **Logging**: Winston + Google Cloud Logging

### Estructura del Backend:
```
backend/
├── src/
│   ├── controllers/
│   │   ├── authController.js    # Manejo de autenticación
│   │   ├── userController.js    # Gestión de usuarios
│   │   └── usageController.js   # Control de límites de uso
│   ├── middleware/
│   │   ├── auth.js              # Verificación de tokens
│   │   ├── rateLimiter.js       # Control de rate limiting
│   │   ├── logger.js            # Middleware de logging
│   │   └── errorHandler.js      # Manejo centralizado de errores
│   ├── services/
│   │   ├── googleAuth.js        # Integración con Google OAuth
│   │   ├── firestore.js         # Operaciones de base de datos
│   │   └── planManager.js       # Lógica de planes y límites
│   ├── models/
│   │   ├── User.js              # Modelo de usuario
│   │   └── Plan.js              # Modelo de plan
│   ├── routes/
│   │   ├── auth.js              # Rutas de autenticación
│   │   ├── users.js             # Rutas de usuarios
│   │   └── usage.js             # Rutas de control de uso
│   ├── utils/
│   │   ├── logger.js            # Configuración de Winston
│   │   ├── validation.js        # Validaciones
│   │   └── constants.js         # Constantes del sistema
│   └── app.js                   # Configuración principal de Express
├── config/
│   ├── database.js              # Configuración de Firestore
│   ├── oauth.js                 # Configuración OAuth
│   └── environment.js           # Variables de entorno
├── tests/
│   ├── unit/                    # Pruebas unitarias
│   └── integration/             # Pruebas de integración
├── package.json
└── Dockerfile                   # Para despliegue en Cloud Run
```

## 🔍 Sistema de Logging y Debug

### Niveles de Log:
```javascript
// logger.js - Configuración de niveles
const LOG_LEVELS = {
  ERROR: 0,    // Errores críticos
  WARN: 1,     // Advertencias
  INFO: 2,     // Información general
  DEBUG: 3,    // Información de debug
  TRACE: 4     // Trazas detalladas
};
```

### Implementación en Frontend:
```javascript
// Ejemplo de uso en api.js
Logger.info('Starting GA4 API request', {
  propertyId: propertyId,
  dimension: selectedDimension,
  dateRange: dateRange,
  userId: currentUser.id
});

try {
  const response = await fetch(apiUrl, requestOptions);
  Logger.debug('GA4 API response received', {
    status: response.status,
    responseSize: response.headers.get('content-length')
  });
} catch (error) {
  Logger.error('GA4 API request failed', {
    error: error.message,
    stack: error.stack,
    requestUrl: apiUrl
  });
}
```

## 🎛️ Configuración de Dimensiones de Página (Nueva Funcionalidad)

### Opciones de Dimensión para Análisis de Página:
```javascript
// config.js - Configuración de dimensiones
const PAGE_DIMENSION_OPTIONS = {
  pagePath: {
    name: 'Ruta de Página',
    description: 'Analiza esta página específica por su URL',
    apiName: 'pagePath',
    filterType: 'exact_match'
  },
  landingPage: {
    name: 'Página de Destino',
    description: 'Analiza sesiones que comenzaron en esta página',
    apiName: 'landingPage', 
    filterType: 'exact_match'
  },
  exitPage: {
    name: 'Página de Salida',
    description: 'Analiza sesiones que terminaron en esta página',
    apiName: 'exitPage',
    filterType: 'exact_match'
  },
  pagePathPlusQueryString: {
    name: 'Página + Parámetros',
    description: 'Incluye parámetros de URL en el análisis',
    apiName: 'pagePathPlusQueryString',
    filterType: 'exact_match'
  }
};
```

### UI para Selección de Dimensión:
```html
<!-- En popup.html -->
<div class="dimension-selector">
  <label for="page-dimension">Analizar página como:</label>
  <select id="page-dimension" class="form-select">
    <option value="pagePath">Ruta de Página</option>
    <option value="landingPage">Página de Destino</option>
    <option value="exitPage">Página de Salida</option>
    <option value="pagePathPlusQueryString">Página + Parámetros</option>
  </select>
  <small class="help-text" id="dimension-help">
    Analiza esta página específica por su URL
  </small>
</div>
```

## 🔐 Consideraciones de Seguridad

### Frontend:
- **Tokens**: Nunca almacenar tokens de larga duración en localStorage
- **Validación**: Validar todas las entradas del usuario
- **CSP**: Content Security Policy estricta en manifest.json
- **Permisos**: Solicitar solo permisos mínimos necesarios

### Backend:
- **Autenticación**: Verificar tokens de Google en cada request
- **Rate Limiting**: Implementar límites por IP y por usuario
- **Validación**: Validar y sanitizar todas las entradas
- **Logs**: No loggear información sensible (tokens, emails completos)

## 📊 Monitoreo y Métricas

### Métricas de Rendimiento:
- Tiempo de respuesta de APIs
- Tasa de errores por endpoint
- Uso de cache (hit/miss ratio)
- Tiempo de carga del popup

### Métricas de Negocio:
- Consultas por usuario por día
- Conversión de gratuito a premium
- Páginas más analizadas
- Errores más comunes

Esta arquitectura proporciona una base sólida, escalable y mantenible para la extensión, con especial atención al logging, debug y flexibilidad en el análisis de páginas.
