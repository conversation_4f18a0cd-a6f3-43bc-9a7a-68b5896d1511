# Flujo de Selección de Propiedades GA4 - GA4 Insights

## 🔄 Diagrama de Flujo de Selección

```mermaid
flowchart TD
    A[Usuario autenticado exitosamente] --> B[Llamar GA4 Admin API]
    B --> C[Obtener Account Summaries]
    C --> D{¿Hay propiedades GA4?}
    
    D -->|Sí| E{¿Cuántas propiedades?}
    D -->|No| F[Mostrar mensaje: Sin propiedades GA4]
    
    E -->|1 propiedad| G[Seleccionar automáticamente]
    E -->|Múltiples| H[Mostrar selector de propiedades]
    
    F --> I[Ofrecer ayuda para configurar GA4]
    
    H --> J[Usuario selecciona propiedad]
    J --> K[Validar acceso a la propiedad]
    K --> L{¿Acceso válido?}
    
    L -->|Sí| M[Guardar selección en storage]
    L -->|No| N[Mostrar error de permisos]
    
    G --> M
    M --> O[Obtener datos básicos de la propiedad]
    O --> P[Mostrar interfaz principal]
    
    N --> Q[Sugerir contactar administrador]
    I --> R[Enlace a documentación GA4]
```

## 🎨 Mockups de UI para Selección de Propiedades

### 1. Cargando Propiedades GA4

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [🔄 Spinner]               │
│                                         │
│        Obteniendo tus propiedades       │
│           de Google Analytics...        │
│                                         │
│         ████████████████                │
│                                         │
│    • Conectando con GA4 Admin API...    │
│    • Verificando permisos...            │
│    • Cargando propiedades...            │
│                                         │
│                                         │
│         Esto puede tomar unos           │
│             segundos...                 │
│                                         │
│                                         │
│                                         │
│                                         │
└─────────────────────────────────────────┘
```

### 2. Selección de Propiedad (Múltiples Opciones)

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│        Selecciona tu propiedad GA4      │
│                                         │
│    Hemos encontrado 3 propiedades       │
│         con acceso de lectura:          │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ 🏢 Mi Sitio Web Corporativo        │ │
│  │    GA4-123456789                    │ │
│  │    📊 Datos disponibles             │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ 📝 Blog Personal                    │ │
│  │    GA4-987654321                    │ │
│  │    📊 Datos disponibles             │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ 🛒 Tienda Online                    │ │
│  │    GA4-456789123                    │ │
│  │    ⚠️ Acceso limitado               │ │
│  └─────────────────────────────────────┘ │
│                                         │
│    [🔄] Actualizar lista                │
│                                         │
└─────────────────────────────────────────┘
```

### 3. Detalles de Propiedad Seleccionada

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [← Cambiar propiedad]                   │
│                                         │
│         🏢 Mi Sitio Web Corporativo     │
│            GA4-123456789                │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           INFORMACIÓN               │ │
│  ├─────────────────────────────────────┤ │
│  │ 🌐 Sitio web: ejemplo.com           │ │
│  │ 📅 Creado: 15 Ene 2023              │ │
│  │ 🕐 Zona horaria: Europe/Madrid      │ │
│  │ 💰 Moneda: EUR                      │ │
│  │ 📊 Flujos de datos: 1               │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │         PERMISOS DE ACCESO          │ │
│  ├─────────────────────────────────────┤ │
│  │ ✅ Leer datos de audiencia          │ │
│  │ ✅ Leer informes                    │ │
│  │ ✅ Ver configuración                │ │
│  │ ❌ Editar configuración             │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           Continuar                 │ │
│  └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

### 4. Sin Propiedades GA4 Disponibles

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [📊 Icono]                 │
│                                         │
│       No se encontraron propiedades     │
│           de Google Analytics 4         │
│                                         │
│    Posibles causas:                     │
│    • No tienes propiedades GA4          │
│    • Sin permisos de acceso             │
│    • Propiedades en otra cuenta         │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │    📚 Cómo crear una propiedad GA4 │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │    🔄 Actualizar lista              │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │    🔄 Cambiar cuenta Google         │ │
│  └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

### 5. Error de Permisos

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [🔒 Candado]               │
│                                         │
│         Acceso Denegado                 │
│                                         │
│    No tienes permisos suficientes       │
│      para acceder a esta propiedad      │
│                                         │
│    Para usar GA4 Insights necesitas:    │
│    ✅ Rol de "Lector" o superior        │
│    ✅ Acceso a informes                 │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │   📧 Contactar administrador        │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │   🔄 Probar otra propiedad          │ │
│  └─────────────────────────────────────┘ │
│                                         │
│    [?] Más información sobre permisos   │
│                                         │
└─────────────────────────────────────────┘
```

### 6. Configuración Inicial Completada

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [✅ Check]                 │
│                                         │
│         ¡Configuración completa!        │
│                                         │
│    Tu extensión está lista para usar    │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │         CONFIGURACIÓN ACTUAL        │ │
│  ├─────────────────────────────────────┤ │
│  │ 👤 Usuario: <EMAIL>        │ │
│  │ 🏢 Propiedad: Mi Sitio Web          │ │
│  │ 📊 Plan: Gratuito (50/50 consultas) │ │
│  │ 🌐 Idioma: Español                  │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │        Comenzar a analizar          │ │
│  └─────────────────────────────────────┘ │
│                                         │
│    [⚙️] Cambiar configuración           │
│                                         │
└─────────────────────────────────────────┘
```

## 🔧 Implementación Técnica

### 1. Obtener Account Summaries
```javascript
// api.js - Obtener propiedades GA4
async function fetchGA4AccountSummaries(accessToken) {
    Logger.info('Fetching GA4 account summaries');
    
    try {
        const response = await fetch(
            `${CONFIG.GA4_ADMIN_API_BASE_URL}/accountSummaries`,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        if (!response.ok) {
            throw new Error(`GA4 Admin API error: ${response.status}`);
        }
        
        const data = await response.json();
        
        Logger.debug('Account summaries received', { 
            accountCount: data.accountSummaries?.length || 0 
        });
        
        return processAccountSummaries(data.accountSummaries || []);
        
    } catch (error) {
        Logger.error('Failed to fetch account summaries', { 
            error: error.message 
        });
        throw error;
    }
}
```

### 2. Procesar y Filtrar Propiedades
```javascript
// api.js - Procesar account summaries
function processAccountSummaries(accountSummaries) {
    Logger.debug('Processing account summaries');
    
    const ga4Properties = [];
    
    accountSummaries.forEach(account => {
        if (account.propertySummaries) {
            account.propertySummaries.forEach(property => {
                // Solo propiedades GA4 (no Universal Analytics)
                if (property.propertyType === 'PROPERTY_TYPE_ORDINARY') {
                    ga4Properties.push({
                        accountId: account.account.split('/')[1],
                        accountName: account.displayName,
                        propertyId: property.property,
                        propertyName: property.displayName,
                        websiteUrl: property.websiteUrl || null,
                        createTime: property.createTime || null
                    });
                }
            });
        }
    });
    
    Logger.info('GA4 properties found', { 
        count: ga4Properties.length 
    });
    
    return ga4Properties;
}
```

### 3. Validar Acceso a Propiedad
```javascript
// api.js - Validar acceso a propiedad específica
async function validatePropertyAccess(accessToken, propertyId) {
    Logger.info('Validating property access', { propertyId });
    
    try {
        // Intentar hacer una consulta básica para verificar acceso
        const testRequest = {
            property: propertyId,
            dateRanges: [{ startDate: '7daysAgo', endDate: 'yesterday' }],
            metrics: [{ name: 'activeUsers' }],
            limit: 1
        };
        
        const response = await fetch(
            `${CONFIG.GA4_DATA_API_BASE_URL}/${propertyId}:runReport`,
            {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testRequest)
            }
        );
        
        if (response.ok) {
            Logger.debug('Property access validated successfully');
            return { hasAccess: true, permissions: ['read'] };
        } else if (response.status === 403) {
            Logger.warn('Property access denied', { 
                propertyId, 
                status: response.status 
            });
            return { hasAccess: false, reason: 'insufficient_permissions' };
        } else {
            throw new Error(`Validation failed: ${response.status}`);
        }
        
    } catch (error) {
        Logger.error('Property access validation failed', { 
            propertyId, 
            error: error.message 
        });
        return { hasAccess: false, reason: 'validation_error' };
    }
}
```

### 4. Guardar Selección de Propiedad
```javascript
// state.js - Guardar propiedad seleccionada
async function saveSelectedProperty(propertyData) {
    Logger.info('Saving selected property', { 
        propertyId: propertyData.propertyId 
    });
    
    try {
        const propertyConfig = {
            selectedProperty: {
                propertyId: propertyData.propertyId,
                propertyName: propertyData.propertyName,
                accountName: propertyData.accountName,
                websiteUrl: propertyData.websiteUrl,
                selectedAt: Date.now()
            }
        };
        
        await chrome.storage.local.set(propertyConfig);
        
        // Actualizar estado global
        State.selectedProperty = propertyConfig.selectedProperty;
        
        Logger.debug('Property selection saved successfully');
        
        return true;
        
    } catch (error) {
        Logger.error('Failed to save property selection', { 
            error: error.message 
        });
        throw error;
    }
}
```

### 5. UI para Mostrar Lista de Propiedades
```javascript
// ui.js - Renderizar lista de propiedades
function renderPropertySelector(properties) {
    Logger.debug('Rendering property selector', { 
        propertyCount: properties.length 
    });
    
    const container = document.getElementById('property-selector');
    
    if (properties.length === 0) {
        container.innerHTML = `
            <div class="no-properties">
                <div class="icon">📊</div>
                <h3>No se encontraron propiedades GA4</h3>
                <p>No tienes acceso a ninguna propiedad de Google Analytics 4</p>
                <button onclick="showGA4Help()" class="btn btn-primary">
                    📚 Cómo crear una propiedad GA4
                </button>
                <button onclick="refreshProperties()" class="btn btn-secondary">
                    🔄 Actualizar lista
                </button>
            </div>
        `;
        return;
    }
    
    if (properties.length === 1) {
        // Auto-seleccionar si solo hay una propiedad
        Logger.info('Auto-selecting single property');
        selectProperty(properties[0]);
        return;
    }
    
    // Mostrar selector para múltiples propiedades
    const propertyList = properties.map(property => `
        <div class="property-item" onclick="selectProperty('${property.propertyId}')">
            <div class="property-header">
                <h4>${escapeHtml(property.propertyName)}</h4>
                <span class="property-id">${property.propertyId}</span>
            </div>
            <div class="property-details">
                <span class="account">📁 ${escapeHtml(property.accountName)}</span>
                ${property.websiteUrl ? `<span class="url">🌐 ${escapeHtml(property.websiteUrl)}</span>` : ''}
            </div>
            <div class="property-status">
                <span class="status-badge status-available">📊 Datos disponibles</span>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = `
        <div class="property-selector">
            <h3>Selecciona tu propiedad GA4</h3>
            <p>Hemos encontrado ${properties.length} propiedades con acceso de lectura:</p>
            <div class="property-list">
                ${propertyList}
            </div>
            <button onclick="refreshProperties()" class="btn btn-link">
                🔄 Actualizar lista
            </button>
        </div>
    `;
}
```

## 📊 Manejo de Estados Especiales

### Estados de Error:
1. **Sin propiedades**: Guía para crear GA4
2. **Sin permisos**: Contactar administrador
3. **Error de red**: Reintentar conexión
4. **Token expirado**: Re-autenticar

### Optimizaciones:
- **Cache de propiedades**: 1 hora de duración
- **Validación lazy**: Solo al seleccionar
- **Retry automático**: Para errores temporales
- **Feedback visual**: Estados de carga claros

Este flujo asegura que los usuarios puedan configurar fácilmente su propiedad GA4 y entender cualquier limitación de acceso.
