# Wireframes del Popup - GA4 Insights Extension

## 📐 Especificaciones de Diseño

### Dimensiones del Popup:
- **Ancho**: 420px (óptimo para extensiones de Chrome)
- **Alto**: 600px (permite scroll si es necesario)
- **Mínimo**: 380px x 500px
- **Máximo**: 800px x 700px

### Principios de Diseño:
- **Minimalista**: Información clara sin saturar
- **Escaneable**: Jerarquía visual clara
- **Accionable**: CTAs evidentes
- **Responsive**: Adaptable a diferentes tamaños

## 🎨 Wireframe 1: Estado de Carga Inicial

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│           [🔄 Cargando...]              │
│                                         │
│     Conectando con Google Analytics     │
│                                         │
│         ████████████████                │
│           Cargando datos...             │
│                                         │
│                                         │
│                                         │
│                                         │
│                                         │
│                                         │
│                                         │
│                                         │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 2: Prompt de Autenticación

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [🔐 Logo]                  │
│                                         │
│         ¡Bienvenido a GA4 Insights!     │
│                                         │
│    Analiza el rendimiento de tu sitio   │
│         web directamente desde          │
│           la página actual              │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │  🔵 Conectar con Google Analytics  │ │
│  └─────────────────────────────────────┘ │
│                                         │
│         🔒 Conexión segura con          │
│            Google OAuth 2.0             │
│                                         │
│    [?] ¿Qué datos accedemos?            │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 3: Selección de Propiedad GA4

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│        Selecciona tu propiedad GA4      │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │ 🔽 Mi Sitio Web (GA4-123456789)   ▼│ │
│  └─────────────────────────────────────┘ │
│                                         │
│  Propiedades disponibles:               │
│  • Mi Sitio Web (GA4-123456789)        │
│  • Blog Personal (GA4-987654321)       │
│  • Tienda Online (GA4-456789123)       │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           Continuar                 │ │
│  └─────────────────────────────────────┘ │
│                                         │
│         [🔄] Actualizar lista           │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 4: Vista Principal - "Esta Página"

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│                                         │
│ 📍 /productos/categoria-a               │
│                                         │
│ Analizar como: [🔽 Ruta de Página    ▼] │
│ Período: [🔽 Últimos 7 días         ▼] │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │           MÉTRICAS CLAVE            │ │
│ ├─────────────────────────────────────┤ │
│ │ 👥 Usuarios Activos        142      │ │
│ │ 👁️  Vistas de Página       89       │ │
│ │ ⚡ Tasa de Interacción     68%      │ │
│ │ ⏱️  Duración Promedio      2:34     │ │
│ │ 📉 Tasa de Rebote         32%      │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │        FUENTES DE TRÁFICO           │ │
│ ├─────────────────────────────────────┤ │
│ │ 🔍 google/organic         45%       │ │
│ │ 📧 email/newsletter       23%       │ │
│ │ 🔗 direct/(none)          18%       │ │
│ │ 📱 facebook/social        14%       │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [📊 Ver tendencia] [🔄 Actualizar]      │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 5: Vista Principal - "Sitio Completo"

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│                                         │
│ 🏠 Mi Sitio Web (GA4-123456789)        │
│ Período: [🔽 Últimos 28 días        ▼] │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │         RESUMEN GENERAL             │ │
│ ├─────────────────────────────────────┤ │
│ │ 👥 Usuarios Activos      2,847      │ │
│ │ 🆕 Usuarios Nuevos       1,203      │ │
│ │ 📊 Sesiones Totales      4,156      │ │
│ │ ⚡ Tasa de Interacción   72%        │ │
│ │ 🎯 Conversiones          89         │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │         TOP PÁGINAS                 │ │
│ ├─────────────────────────────────────┤ │
│ │ /                        1,234      │ │
│ │ /productos               567        │ │
│ │ /blog/articulo-1         234        │ │
│ │ /contacto                123        │ │
│ │ [Ver todas...]                      │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [📈 Ver gráficos] [📤 Exportar]         │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 6: Vista de Gráfico/Tendencias

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│ [← Volver]                              │
│                                         │
│ 📈 Tendencia de Usuarios Activos        │
│ Últimos 7 días                          │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 200 ┤                               │ │
│ │     │     ●                         │ │
│ │ 150 ┤   ●   ●                       │ │
│ │     │ ●       ●                     │ │
│ │ 100 ┤           ●   ●               │ │
│ │     │             ●   ●             │ │
│ │  50 ┤                 ●             │ │
│ │     └─┬─┬─┬─┬─┬─┬─┬─                │ │
│ │      L M M J V S D                  │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ 📊 Métricas del período:                │
│ • Promedio diario: 142 usuarios        │
│ • Día pico: Viernes (189 usuarios)     │
│ • Crecimiento: +12% vs período anterior│
│                                         │
│ [📤 Exportar datos] [🔄 Actualizar]     │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 7: Estado de Error

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [⚠️ Error]                 │
│                                         │
│         No se pudieron cargar           │
│            los datos de GA4             │
│                                         │
│    Posibles causas:                     │
│    • Sin conexión a internet           │
│    • Límite de API alcanzado            │
│    • Sesión expirada                    │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │           Reintentar                │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │         Cerrar sesión               │ │
│  └─────────────────────────────────────┘ │
│                                         │
│    [?] Obtener ayuda                    │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 8: Límite de Plan Alcanzado

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│                                         │
│              [🚫 Límite]                │
│                                         │
│       Has alcanzado el límite de        │
│          tu plan gratuito               │
│                                         │
│    📊 Uso actual: 50/50 consultas       │
│    🔄 Se renueva: 1 Feb 2024            │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │  ⭐ Actualizar a Premium - $9.99   │ │
│  └─────────────────────────────────────┘ │
│                                         │
│    ✅ Consultas ilimitadas              │
│    ✅ Comparación de períodos           │
│    ✅ Exportación de datos              │
│    ✅ Filtros avanzados                 │
│                                         │
│    [ℹ️] Más información                 │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Wireframe 9: Configuración/Ajustes

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [← Volver]                              │
│                                         │
│              ⚙️ Configuración           │
│                                         │
│ 🏠 Propiedad GA4:                       │
│ [🔽 Mi Sitio Web (GA4-123456789)    ▼] │
│                                         │
│ 🌐 Idioma:                              │
│ [🔽 Español                         ▼] │
│                                         │
│ 📊 Dimensión por defecto:               │
│ [🔽 Ruta de Página                  ▼] │
│                                         │
│ 📅 Período por defecto:                 │
│ [🔽 Últimos 7 días                  ▼] │
│                                         │
│ 🔔 Notificaciones:                      │
│ [☑️] Alertas de límite de uso           │
│ [☐] Reportes semanales                  │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │           Guardar cambios           │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [🚪 Cerrar sesión] [❓ Ayuda]           │
│                                         │
└─────────────────────────────────────────┘
```

## 📱 Consideraciones de Responsive Design

### Popup Pequeño (380px ancho):
- Ocultar texto descriptivo largo
- Iconos más pequeños
- Métricas en 2 columnas en lugar de lista
- Botones más compactos

### Popup Grande (800px ancho):
- Mostrar más métricas simultáneamente
- Gráficos más detallados
- Sidebar con navegación adicional
- Vista de tabla expandida

## 🎯 Elementos Interactivos

### Botones Principales:
- **Primario**: Azul (#007bff) - Acciones principales
- **Secundario**: Gris (#6c757d) - Acciones secundarias
- **Éxito**: Verde (#28a745) - Confirmaciones
- **Peligro**: Rojo (#dc3545) - Acciones destructivas
- **Advertencia**: Amarillo (#ffc107) - Alertas

### Estados de Hover:
- Cambio de color sutil
- Sombra ligera
- Transición suave (0.2s)

### Estados de Loading:
- Skeleton screens para métricas
- Spinners para acciones
- Barras de progreso para cargas largas

Estos wireframes proporcionan una base sólida para el desarrollo de la interfaz, priorizando la usabilidad y la claridad de la información.
