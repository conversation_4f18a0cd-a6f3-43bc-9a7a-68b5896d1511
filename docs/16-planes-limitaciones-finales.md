# Planes y Limitaciones - GA4 Insights

## 📋 Plan Gratuito

### Límites y Restricciones:
- **Consultas API**: 50 por mes
- **Período de datos**: Máximo 30 días históricos
- **Comparaciones**: No disponibles
- **Exportación**: No disponible
- **Dimensiones**: Solo básicas (pagePath, landingPage)
- **Soporte**: Documentación online

### Funcionalidades Incluidas:
- ✅ Vista "Esta Página" con métricas básicas
- ✅ Vista "Sitio Completo" limitada
- ✅ Gráficos simples de tendencias
- ✅ Top 5 fuentes de tráfico
- ✅ Análisis de dispositivos básico
- ✅ Cache local para optimizar uso

## 💎 Plan Premium ($9.99/mes)

### Sin Límites:
- **Consultas API**: Ilimitadas
- **Período de datos**: Hasta 1 año histórico
- **Comparaciones**: Período anterior y año anterior
- **Exportación**: CSV, PDF, PNG
- **Dimensiones**: Todas disponibles + personalizadas
- **Soporte**: Email prioritario

### Funcionalidades Avanzadas:
- ✅ Todas las funcionalidades gratuitas
- ✅ Comparación de períodos
- ✅ Filtros avanzados por segmentos
- ✅ Análisis de eventos personalizados
- ✅ Reportes programados por email
- ✅ Dashboard personalizable
- ✅ Análisis de cohortes
- ✅ Métricas de conversión avanzadas

## 🔧 Lógica de Control en Backend

### Verificación de Límites:
```php
// app/Services/UsageTrackingService.php
class UsageTrackingService {
    public function canMakeApiCall(User $user): array {
        $plan = $user->plan;
        
        // Plan premium = sin límites
        if ($plan->api_call_limit === null) {
            return ['allowed' => true, 'unlimited' => true];
        }
        
        // Verificar uso actual
        $currentUsage = $user->usage_count;
        $limit = $plan->api_call_limit;
        
        if ($currentUsage >= $limit) {
            Log::warning('Usage limit exceeded', [
                'user_id' => $user->id,
                'current_usage' => $currentUsage,
                'limit' => $limit
            ]);
            
            return [
                'allowed' => false,
                'reason' => 'limit_exceeded',
                'current_usage' => $currentUsage,
                'limit' => $limit,
                'reset_date' => $user->usage_reset_at
            ];
        }
        
        return [
            'allowed' => true,
            'remaining' => $limit - $currentUsage,
            'current_usage' => $currentUsage,
            'limit' => $limit
        ];
    }
    
    public function incrementUsage(User $user): void {
        $user->increment('usage_count');
        $user->touch('updated_at');
        
        // Log del uso
        UsageLog::create([
            'user_id' => $user->id,
            'action_type' => 'ga4_api_call',
            'success' => true,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }
}
```

### Reset Mensual:
```php
// app/Console/Commands/ResetUsageCounters.php
class ResetUsageCounters extends Command {
    public function handle() {
        $users = User::where('usage_reset_at', '<=', now())->get();
        
        foreach ($users as $user) {
            $user->update([
                'usage_count' => 0,
                'usage_reset_at' => now()->addMonth()
            ]);
            
            Log::info('Usage reset for user', ['user_id' => $user->id]);
        }
    }
}
```

## 🎯 Mensajes de Límites en Frontend

### JavaScript para Manejo de Límites:
```javascript
// features.js - Verificar límites antes de consulta
async function checkUsageLimits() {
    try {
        const response = await API.checkUsageStatus();
        
        if (!response.allowed) {
            if (response.reason === 'limit_exceeded') {
                UI.showUpgradePrompt({
                    title: 'Límite Alcanzado',
                    message: `Has usado ${response.current_usage}/${response.limit} consultas este mes.`,
                    resetDate: response.reset_date,
                    upgradeUrl: CONFIG.UPGRADE_URL
                });
                return false;
            }
        }
        
        // Mostrar advertencia si está cerca del límite
        if (response.remaining <= 5) {
            UI.showUsageWarning(response.remaining);
        }
        
        return true;
    } catch (error) {
        Logger.error('Failed to check usage limits', { error: error.message });
        return true; // Permitir en caso de error
    }
}
```

---

## 🚀 **PROPUESTA: Pasar a Desarrollo Real**

Ahora que tenemos toda la planificación completa, te propongo pasar directamente a crear los archivos reales de la extensión:

### **Próximos pasos sugeridos:**

1. **Crear estructura de archivos** de la extensión
2. **Generar `manifest.json`** funcional
3. **Desarrollar `popup.html`** y CSS
4. **Implementar JavaScript** core (`popup.js`, `auth.js`, `api.js`)
5. **Configurar autenticación** OAuth real
6. **Conectar con APIs** de GA4

### **¿Empezamos con el código real?**

¿Te parece bien que ahora creemos los archivos funcionales de la extensión? Podemos empezar con:

1. **`manifest.json`** - Configuración base de la extensión
2. **`popup.html`** - Interfaz principal
3. **`popup.css`** - Estilos
4. **`js/popup.js`** - Lógica principal

¿Por cuál prefieres que empecemos?
