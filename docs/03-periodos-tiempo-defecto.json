{"periodos_tiempo_defecto": {"vista_pagina_actual": {"periodo_principal": {"id": "last_7_days", "nombre": "Últimos 7 días", "descripcion": "Período óptimo para análisis de página específica - suficientes datos sin ruido", "start_date": "7daysAgo", "end_date": "yesterday", "justificacion": "Balance entre volumen de datos y relevancia temporal para páginas individuales"}, "periodos_alternativos": [{"id": "yesterday", "nombre": "Ayer", "start_date": "yesterday", "end_date": "yesterday", "uso": "Análisis de rendimiento inmediato"}, {"id": "last_30_days", "nombre": "Últimos 30 días", "start_date": "30daysAgo", "end_date": "yesterday", "uso": "Tendencias a largo plazo para páginas con tráfico constante"}]}, "vista_sitio_completo": {"periodo_principal": {"id": "last_28_days", "nombre": "Últimos 28 días", "descripcion": "Período estándar para análisis de sitio completo - elimina variaciones semanales", "start_date": "28daysAgo", "end_date": "yesterday", "justificacion": "4 semanas completas proporcionan datos estadísticamente significativos y eliminan sesgos de días de la semana"}, "periodos_alternativos": [{"id": "last_7_days", "nombre": "Últimos 7 días", "start_date": "7daysAgo", "end_date": "yesterday", "uso": "Análisis de tendencias recientes"}, {"id": "last_90_days", "nombre": "Últimos 90 días", "start_date": "90daysAgo", "end_date": "yesterday", "uso": "Análisis trimestral y tendencias estacionales"}]}}, "periodos_comparacion": {"habilitado_en_planes": ["premium"], "opciones": [{"id": "previous_period", "nombre": "<PERSON><PERSON><PERSON>", "descripcion": "Compara con el período inmediatamente anterior del mismo tamaño", "ejemplo": "Si seleccionas 'Últimos 7 días', compara con los 7 días anteriores a esos"}, {"id": "year_over_year", "nombre": "<PERSON><PERSON>", "descripcion": "Compara con el mismo período del año anterior", "ejemplo": "Si seleccionas 'Últimos 28 días', compara con los mismos 28 días del año pasado"}]}, "configuracion_avanzada": {"rangos_personalizados": {"habilitado_en_planes": ["premium"], "limitaciones": {"plan_gratuito": {"max_dias": 30, "mensaje": "El plan gratuito está limitado a máximo 30 días de datos"}, "plan_premium": {"max_dias": 365, "mensaje": "Puedes consultar hasta 1 año de datos históricos"}}}, "frecuencia_actualizacion": {"tiempo_real": false, "nota": "Los datos de GA4 tienen una latencia de 24-48 horas", "cache_duracion": {"datos_recientes": "15 minutos", "datos_historicos": "1 hora"}}}, "formatos_fecha": {"api_ga4": {"formato": "YYYY-MM-DD", "ejemplos": {"hoy": "2024-01-15", "ayer": "2024-01-14", "hace_7_dias": "2024-01-08"}}, "interfaz_usuario": {"formato_corto": "DD/MM", "formato_largo": "DD MMM YYYY", "ejemplos": {"corto": "15/01", "largo": "15 Ene 2024"}}}, "logica_seleccion_automatica": {"reglas": [{"condicion": "Primera vez que se abre la extensión", "accion": "Usar período principal según la vista (7 días para página, 28 días para sitio)"}, {"condicion": "Usuario ha seleccionado un período previamente", "accion": "Recordar última selección usando chrome.storage.local"}, {"condicion": "Cambio entre vistas (página ↔ sitio)", "accion": "Aplicar período principal de la nueva vista"}, {"condicion": "Página con poco tráfico (< 10 vistas en 7 días)", "accion": "Sugerir automáticamente período más largo (30 días)"}]}, "mensajes_usuario": {"datos_insuficientes": {"titulo": "Pocos datos disponibles", "mensaje": "Esta página tiene poco tráfico en el período seleccionado. ¿Quieres ampliar a {periodo_sugerido}?", "acciones": ["Ampliar per<PERSON>", "<PERSON><PERSON><PERSON>"]}, "sin_datos": {"titulo": "Sin datos", "mensaje": "No hay datos disponibles para esta página en el período seleccionado.", "acciones": ["Ver sitio completo", "Cam<PERSON><PERSON>"]}, "datos_antiguos": {"titulo": "Datos no actualizados", "mensaje": "Los datos más recientes son de hace {horas} horas debido a la latencia normal de GA4.", "mostrar_cuando": "datos más recientes > 48 horas"}}, "implementacion_tecnica": {"storage_keys": {"ultimo_periodo_pagina": "ga4_last_period_page", "ultimo_periodo_sitio": "ga4_last_period_site", "preferencia_comparacion": "ga4_comparison_preference"}, "validaciones": ["Verificar que start_date <= end_date", "Verificar que el rango no exceda límites del plan", "Verificar que las fechas no sean futuras", "Verificar disponibilidad de datos en GA4"]}}