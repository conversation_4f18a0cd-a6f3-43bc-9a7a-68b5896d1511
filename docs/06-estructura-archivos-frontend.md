# Estructura de Archivos Frontend - GA4 Insights Extension

## 📁 Árbol de Directorios Completo

```
ga4-insights-extension/
│
├── 📄 manifest.json                    # Configuración principal de la extensión
├── 📄 popup.html                       # Interfaz principal del popup
├── 📄 background.js                    # Service worker (opcional)
│
├── 📁 js/                              # Scripts JavaScript
│   ├── 📄 popup.js                     # Controlador principal del popup
│   ├── 📄 auth.js                      # Gestión de autenticación OAuth 2.0
│   ├── 📄 api.js                       # Comunicación con APIs (GA4 + Backend)
│   ├── 📄 ui.js                        # Manipulación de DOM y componentes UI
│   ├── 📄 features.js                  # Procesamiento y visualización de datos
│   ├── 📄 state.js                     # Gestión de estado global de la aplicación
│   ├── 📄 utils.js                     # Funciones utilitarias y helpers
│   ├── 📄 config.js                    # Configuración y constantes
│   ├── 📄 logger.js                    # Sistema de logging y debug
│   ├── 📄 i18n.js                      # Internacionalización y traducciones
│   ├── 📄 cache.js                     # Gestión de cache local
│   └── 📄 dimensions.js                # Configuración de dimensiones de página
│
├── 📁 css/                             # Hojas de estilo
│   ├── 📄 popup.css                    # Estilos principales del popup
│   ├── 📄 components.css               # Estilos de componentes reutilizables
│   ├── 📄 themes.css                   # Variables CSS y temas
│   ├── 📄 animations.css               # Animaciones y transiciones
│   └── 📄 responsive.css               # Estilos responsive
│
├── 📁 icons/                           # Iconos de la extensión
│   ├── 🖼️ icon16.png                   # Icono 16x16 (toolbar)
│   ├── 🖼️ icon48.png                   # Icono 48x48 (gestión de extensiones)
│   ├── 🖼️ icon128.png                  # Icono 128x128 (Chrome Web Store)
│   └── 🖼️ icon.svg                     # Icono vectorial (fuente)
│
├── 📁 images/                          # Imágenes adicionales
│   ├── 🖼️ logo.png                     # Logo de la aplicación
│   ├── 🖼️ google-signin.png            # Botón de Google Sign-in
│   ├── 🖼️ no-data.svg                  # Ilustración para "sin datos"
│   └── 🖼️ loading.gif                  # Animación de carga
│
├── 📁 locales/                         # Archivos de traducción
│   ├── 📄 en.json                      # Textos en inglés
│   ├── 📄 es.json                      # Textos en español
│   └── 📄 pt.json                      # Textos en portugués
│
├── 📁 lib/                             # Bibliotecas externas
│   ├── 📄 chart.min.js                 # Chart.js para gráficos
│   └── 📄 date-fns.min.js              # Biblioteca para manejo de fechas
│
├── 📁 templates/                       # Plantillas HTML
│   ├── 📄 page-analysis.html           # Template para análisis de página
│   ├── 📄 site-overview.html           # Template para vista general del sitio
│   ├── 📄 auth-prompt.html             # Template para prompt de autenticación
│   └── 📄 error-states.html            # Templates para estados de error
│
├── 📁 data/                            # Datos estáticos y configuración
│   ├── 📄 ga4-metrics.json             # Definiciones de métricas GA4
│   ├── 📄 ga4-dimensions.json          # Definiciones de dimensiones GA4
│   └── 📄 default-config.json          # Configuración por defecto
│
└── 📁 tests/                           # Archivos de prueba (desarrollo)
    ├── 📄 popup.test.js                # Pruebas del popup
    ├── 📄 api.test.js                  # Pruebas de API
    └── 📄 utils.test.js                # Pruebas de utilidades
```

## 📋 Descripción Detallada de Archivos

### 🔧 Archivos de Configuración Principal

#### `manifest.json`
```json
{
  "manifest_version": 3,
  "name": "GA4 Insights",
  "version": "0.1.0",
  "description": "Análisis rápido de Google Analytics 4 para la página actual",
  "permissions": ["identity", "storage", "activeTab", "scripting"],
  "host_permissions": [
    "https://*.google.com/",
    "https://*.googleapis.com/",
    "https://your-backend.com/"
  ],
  "action": {
    "default_popup": "popup.html",
    "default_icon": {
      "16": "icons/icon16.png",
      "48": "icons/icon48.png",
      "128": "icons/icon128.png"
    }
  },
  "oauth2": {
    "client_id": "YOUR_CLIENT_ID.apps.googleusercontent.com",
    "scopes": [
      "https://www.googleapis.com/auth/analytics.readonly",
      "email",
      "profile"
    ]
  },
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'"
  }
}
```

#### `popup.html`
```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GA4 Insights</title>
  <link rel="stylesheet" href="css/popup.css">
  <link rel="stylesheet" href="css/components.css">
</head>
<body>
  <div id="app" class="popup-container">
    <!-- El contenido se carga dinámicamente -->
  </div>
  
  <!-- Scripts -->
  <script src="lib/chart.min.js"></script>
  <script src="js/logger.js"></script>
  <script src="js/config.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/i18n.js"></script>
  <script src="js/cache.js"></script>
  <script src="js/state.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/api.js"></script>
  <script src="js/dimensions.js"></script>
  <script src="js/features.js"></script>
  <script src="js/ui.js"></script>
  <script src="js/popup.js"></script>
</body>
</html>
```

### 🎯 Scripts JavaScript Principales

#### `js/popup.js` - Controlador Principal
```javascript
/**
 * popup.js - Controlador principal del popup de la extensión
 * 
 * Responsabilidades:
 * - Inicialización de la extensión
 * - Coordinación entre módulos
 * - Manejo de eventos principales
 * - Control del flujo de la aplicación
 */

// Funciones principales que contendrá:
// - initializeExtension()
// - handleTabChange()
// - handleDimensionChange()
// - handleDateRangeChange()
// - handleRefreshData()
```

#### `js/auth.js` - Gestión de Autenticación
```javascript
/**
 * auth.js - Gestión de autenticación OAuth 2.0 con Google
 * 
 * Responsabilidades:
 * - Autenticación con Google Identity
 * - Gestión de tokens de acceso
 * - Renovación automática de tokens
 * - Estado de autenticación
 */

// Funciones principales:
// - getAuthToken(interactive)
// - refreshToken()
// - revokeToken()
// - checkAuthStatus()
// - handleAuthError()
```

#### `js/api.js` - Comunicación con APIs
```javascript
/**
 * api.js - Comunicación con APIs de GA4 y backend propio
 * 
 * Responsabilidades:
 * - Llamadas a GA4 Data API
 * - Llamadas a GA4 Admin API
 * - Comunicación con backend propio
 * - Manejo de errores de API
 * - Gestión de rate limiting
 */

// Funciones principales:
// - fetchGA4AccountSummaries(accessToken)
// - runGA4Report(accessToken, propertyId, reportRequest)
// - verifyTokenWithBackend(googleAccessToken)
// - confirmUsageWithBackend(userApiToken)
// - handleApiError(error, context)
```

#### `js/ui.js` - Manipulación de UI
```javascript
/**
 * ui.js - Manipulación de DOM y componentes de interfaz
 * 
 * Responsabilidades:
 * - Renderizado de componentes
 * - Actualización de DOM
 * - Manejo de estados visuales
 * - Animaciones y transiciones
 */

// Funciones principales:
// - showLoading(message)
// - hideLoading()
// - showError(message, type)
// - renderPageAnalytics(data)
// - renderSiteOverview(data)
// - updateDimensionSelector(options)
```

#### `js/features.js` - Procesamiento de Datos
```javascript
/**
 * features.js - Procesamiento y visualización de datos de GA4
 * 
 * Responsabilidades:
 * - Transformación de datos de API
 * - Cálculos y agregaciones
 * - Preparación de datos para gráficos
 * - Formateo para visualización
 */

// Funciones principales:
// - processGA4DataForDisplay(apiResponse)
// - calculateTrends(data, previousData)
// - prepareChartData(data, chartType)
// - formatMetricValue(value, metricType)
// - aggregateDataByDimension(data, dimension)
```

#### `js/state.js` - Gestión de Estado
```javascript
/**
 * state.js - Gestión de estado global de la aplicación
 * 
 * Responsabilidades:
 * - Estado de autenticación
 * - Configuración del usuario
 * - Datos cacheados
 * - Preferencias de UI
 */

// Variables de estado:
// - currentUser
// - selectedProperty
// - selectedDimension
// - dateRange
// - cachedData
// - uiState
```

#### `js/logger.js` - Sistema de Logging
```javascript
/**
 * logger.js - Sistema de logging y debug
 * 
 * Responsabilidades:
 * - Logging estructurado
 * - Niveles de log configurables
 * - Almacenamiento de logs
 * - Reportes de error
 */

// Funciones principales:
// - log(level, message, context)
// - error(message, error, context)
// - warn(message, context)
// - info(message, context)
// - debug(message, context)
// - exportLogs()
```

#### `js/dimensions.js` - Configuración de Dimensiones
```javascript
/**
 * dimensions.js - Configuración y manejo de dimensiones de página
 * 
 * Responsabilidades:
 * - Definición de opciones de dimensión
 * - Lógica de filtrado por dimensión
 * - Validación de dimensiones
 * - Ayuda contextual
 */

// Funciones principales:
// - getAvailableDimensions()
// - buildDimensionFilter(dimension, value)
// - validateDimensionValue(dimension, value)
// - getDimensionDescription(dimension)
```

### 🎨 Archivos de Estilo

#### `css/popup.css` - Estilos Principales
```css
/* Estilos base del popup */
/* Layout principal */
/* Tipografía */
/* Colores principales */
```

#### `css/components.css` - Componentes Reutilizables
```css
/* Botones */
/* Formularios */
/* Tablas */
/* Gráficos */
/* Modales */
/* Tooltips */
```

#### `css/themes.css` - Variables y Temas
```css
/* Variables CSS */
/* Tema claro */
/* Tema oscuro (futuro) */
/* Colores de marca */
```

### 🌐 Archivos de Localización

#### `locales/es.json`
```json
{
  "extension_name": "GA4 Insights",
  "page_analysis": "Análisis de Página",
  "site_overview": "Vista General",
  "dimension_selector": {
    "page_path": "Ruta de Página",
    "landing_page": "Página de Destino",
    "exit_page": "Página de Salida"
  },
  "metrics": {
    "active_users": "Usuarios Activos",
    "page_views": "Vistas de Página",
    "engagement_rate": "Tasa de Interacción"
  }
}
```

## 🔄 Flujo de Carga de Archivos

1. **`popup.html`** se carga cuando el usuario hace clic en la extensión
2. **CSS** se carga primero para evitar FOUC (Flash of Unstyled Content)
3. **Bibliotecas externas** (`chart.js`) se cargan antes que nuestros scripts
4. **Scripts base** (`logger.js`, `config.js`, `utils.js`) se cargan primero
5. **Scripts de funcionalidad** se cargan en orden de dependencia
6. **`popup.js`** se carga al final e inicia la aplicación

## 📦 Consideraciones de Empaquetado

### Archivos Incluidos en Distribución:
- Todos los archivos excepto `tests/`
- Versiones minificadas de CSS y JS en producción
- Solo los locales necesarios según configuración

### Archivos Excluidos:
- `tests/` - Solo para desarrollo
- `*.map` - Source maps solo en desarrollo
- `README.md`, `package.json` - Archivos de desarrollo

Esta estructura proporciona una organización clara, mantenible y escalable para el frontend de la extensión.
