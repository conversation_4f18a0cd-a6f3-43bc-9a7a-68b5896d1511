{"metricas_principales": {"usuarios_y_sesiones": {"activeUsers": {"nombre": "Usuarios Activos", "descripcion": "Número de usuarios únicos que han tenido al menos una sesión en el período especificado", "tipo": "metric", "categoria": "audiencia"}, "newUsers": {"nombre": "Usuarios Nuevos", "descripcion": "Número de usuarios que han iniciado su primera sesión en el período especificado", "tipo": "metric", "categoria": "audiencia"}, "sessions": {"nombre": "Sesiones", "descripcion": "Número total de sesiones iniciadas por los usuarios", "tipo": "metric", "categoria": "audiencia"}, "sessionsPerUser": {"nombre": "Sesiones por Usuario", "descripcion": "Promedio de sesiones por usuario", "tipo": "metric", "categoria": "audiencia"}}, "interaccion": {"engagementRate": {"nombre": "Tasa de Interacción", "descripcion": "Porcentaje de sesiones que fueron interactivas (duraron más de 10 segundos, tuvieron un evento de conversión o tuvieron 2+ vistas de pantalla)", "tipo": "metric", "categoria": "interaccion"}, "averageSessionDuration": {"nombre": "Duración Promedio de Sesión", "descripcion": "Tiempo promedio que duran las sesiones", "tipo": "metric", "categoria": "interaccion"}, "bounceRate": {"nombre": "<PERSON><PERSON>", "descripcion": "Porcentaje de sesiones que no fueron interactivas", "tipo": "metric", "categoria": "interaccion"}}, "contenido": {"screenPageViews": {"nombre": "Vistas de Página/Pantalla", "descripcion": "Número total de vistas de página (web) o pantalla (app)", "tipo": "metric", "categoria": "contenido"}, "screenPageViewsPerSession": {"nombre": "Páginas por Sesión", "descripcion": "Promedio de páginas vistas por sesión", "tipo": "metric", "categoria": "contenido"}}, "conversiones": {"conversions": {"nombre": "Conversiones", "descripcion": "Número total de eventos de conversión", "tipo": "metric", "categoria": "conversiones"}, "totalRevenue": {"nombre": "Ingresos Totales", "descripcion": "Suma de todos los ingresos de comercio electrónico", "tipo": "metric", "categoria": "conversiones"}, "purchaseRevenue": {"nombre": "Ingresos por Compras", "descripcion": "Ingresos generados por eventos de compra", "tipo": "metric", "categoria": "conversiones"}}, "eventos": {"eventCount": {"nombre": "Recuento de Eventos", "descripcion": "Número total de eventos registrados", "tipo": "metric", "categoria": "eventos"}, "eventsPerSession": {"nombre": "Eventos por Sesión", "descripcion": "Promedio de eventos por sesión", "tipo": "metric", "categoria": "eventos"}}}, "dimensiones_principales": {"tiempo": {"date": {"nombre": "<PERSON><PERSON>", "descripcion": "Fecha en formato YYYYMMDD", "tipo": "dimension", "categoria": "tiempo"}, "dateHour": {"nombre": "<PERSON><PERSON> y <PERSON>", "descripcion": "<PERSON>cha y hora en formato YYYYMMDDHH", "tipo": "dimension", "categoria": "tiempo"}, "dayOfWeek": {"nombre": "Día de la Semana", "descripcion": "Día de la semana (0=Domingo, 6=Sábado)", "tipo": "dimension", "categoria": "tiempo"}}, "fuentes_trafico": {"source": {"nombre": "Fuente", "descripcion": "Fuente del tráfico (ej: google, facebook, direct)", "tipo": "dimension", "categoria": "adquisicion"}, "medium": {"nombre": "Medio", "descripcion": "Medio del tráfico (ej: organic, cpc, referral, email)", "tipo": "dimension", "categoria": "adquisicion"}, "sourceMedium": {"nombre": "Fuente/Medio", "descripcion": "Combinación de fuente y medio", "tipo": "dimension", "categoria": "adquisicion"}, "campaign": {"nombre": "Campaña", "descripcion": "Nombre de la campaña de marketing", "tipo": "dimension", "categoria": "adquisicion"}}, "contenido": {"pagePath": {"nombre": "<PERSON><PERSON>", "descripcion": "<PERSON><PERSON> de la página (ej: /productos/categoria)", "tipo": "dimension", "categoria": "contenido"}, "pageTitle": {"nombre": "<PERSON><PERSON><PERSON><PERSON>", "descripcion": "Tí<PERSON>lo de la página web", "tipo": "dimension", "categoria": "contenido"}, "landingPage": {"nombre": "Página de Destino", "descripcion": "Primera página vista en una sesión", "tipo": "dimension", "categoria": "contenido"}}, "tecnologia": {"deviceCategory": {"nombre": "Categoría de Dispositivo", "descripcion": "Tipo de dispositivo (desktop, mobile, tablet)", "tipo": "dimension", "categoria": "tecnologia"}, "browser": {"nombre": "<PERSON><PERSON><PERSON><PERSON>", "descripcion": "Navegador utilizado", "tipo": "dimension", "categoria": "tecnologia"}, "operatingSystem": {"nombre": "Sistema Operativo", "descripcion": "Sistema operativo del dispositivo", "tipo": "dimension", "categoria": "tecnologia"}}, "geografia": {"country": {"nombre": "<PERSON><PERSON>", "descripcion": "País del usuario", "tipo": "dimension", "categoria": "geografia"}, "city": {"nombre": "Ciudad", "descripcion": "Ciudad del usuario", "tipo": "dimension", "categoria": "geografia"}}, "eventos": {"eventName": {"nombre": "Nombre del Evento", "descripcion": "Nombre del evento personalizado o automático", "tipo": "dimension", "categoria": "eventos"}}}, "combinaciones_recomendadas_mvp": [{"nombre": "Vista General de Audiencia", "metricas": ["activeUsers", "newUsers", "sessions", "engagementRate"], "dimensiones": ["date"], "proposito": "Resumen rápido del rendimiento de la audiencia"}, {"nombre": "Análisis de Fuentes de Tráfico", "metricas": ["sessions", "activeUsers", "engagementRate"], "dimensiones": ["sourceMedium"], "proposito": "Identificar las mejores fuentes de tráfico"}, {"nombre": "Rendimiento de Contenido", "metricas": ["screenPageViews", "activeUsers", "averageSessionDuration"], "dimensiones": ["pagePath"], "proposito": "<PERSON><PERSON><PERSON> qué páginas funcionan mejor"}, {"nombre": "Análisis de Dispositivos", "metricas": ["sessions", "engagementRate", "bounceRate"], "dimensiones": ["deviceCategory"], "proposito": "Optimización para diferentes dispositivos"}, {"nombre": "Tendencias Temporales", "metricas": ["activeUsers", "sessions", "conversions"], "dimensiones": ["date", "dayOfWeek"], "proposito": "Identificar patrones temporales"}]}