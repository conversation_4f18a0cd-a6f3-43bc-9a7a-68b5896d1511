# Esquema de Base de Datos Detallado - GA4 Insights

## 🗄️ Diseño de Base de Datos SQL

### Principios de Diseño:
- **Normalización**: Evitar redundancia de datos
- **Escalabilidad**: Preparado para crecimiento
- **Rendimiento**: Índices optimizados para consultas frecuentes
- **Integridad**: Constraints y foreign keys apropiados
- **Auditabilidad**: Timestamps y logs de cambios

## 📊 Esquema Completo

### 1. Tabla `plans`
```sql
-- Tabla de planes de suscripción
CREATE TABLE plans (
    plan_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    billing_period ENUM('monthly', 'yearly', 'lifetime') DEFAULT 'monthly',
    api_call_limit INT UNSIGNED NULL COMMENT 'NULL = ilimitado',
    features JSON NOT NULL COMMENT 'Características del plan en formato JSON',
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    sort_order INT UNSIGNED DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Datos iniciales
INSERT INTO plans (plan_id, name, description, price, api_call_limit, features, sort_order) VALUES
('free', 'Plan Gratuito', 'Plan básico con funcionalidades limitadas', 0.00, 50, 
 JSON_OBJECT(
   'canCompareDates', false,
   'advancedFilters', false,
   'maxDateRangeDays', 30,
   'exportData', false,
   'customDimensions', false,
   'emailReports', false
 ), 1),
('premium_monthly', 'Plan Premium Mensual', 'Acceso completo con consultas ilimitadas', 9.99, NULL,
 JSON_OBJECT(
   'canCompareDates', true,
   'advancedFilters', true,
   'maxDateRangeDays', 365,
   'exportData', true,
   'customDimensions', true,
   'emailReports', true
 ), 2),
('premium_yearly', 'Plan Premium Anual', 'Plan anual con descuento', 99.99, NULL,
 JSON_OBJECT(
   'canCompareDates', true,
   'advancedFilters', true,
   'maxDateRangeDays', 365,
   'exportData', true,
   'customDimensions', true,
   'emailReports', true
 ), 3);
```

### 2. Tabla `users`
```sql
-- Tabla principal de usuarios
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    google_id VARCHAR(255) NOT NULL UNIQUE COMMENT 'ID único de Google',
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    avatar_url VARCHAR(500),
    plan_id VARCHAR(50) NOT NULL DEFAULT 'free',
    
    -- Información de suscripción
    stripe_customer_id VARCHAR(255) NULL,
    subscription_id VARCHAR(255) NULL,
    subscription_status ENUM('active', 'canceled', 'past_due', 'trialing', 'incomplete') NULL,
    subscription_current_period_start TIMESTAMP NULL,
    subscription_current_period_end TIMESTAMP NULL,
    
    -- Control de uso
    usage_count INT UNSIGNED NOT NULL DEFAULT 0,
    usage_reset_at TIMESTAMP NULL COMMENT 'Cuándo se resetea el contador',
    
    -- Preferencias del usuario
    preferences JSON NULL COMMENT 'Configuraciones personalizadas',
    
    -- Configuración de GA4
    selected_property_id VARCHAR(255) NULL COMMENT 'Última propiedad GA4 seleccionada',
    selected_property_name VARCHAR(255) NULL,
    
    -- Timestamps
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL COMMENT 'Soft delete',
    
    -- Constraints
    FOREIGN KEY (plan_id) REFERENCES plans(plan_id) ON UPDATE CASCADE,
    
    -- Índices
    INDEX idx_google_id (google_id),
    INDEX idx_email (email),
    INDEX idx_plan_id (plan_id),
    INDEX idx_subscription_status (subscription_status),
    INDEX idx_created_at (created_at),
    INDEX idx_last_login (last_login_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 3. Tabla `usage_logs`
```sql
-- Log detallado de uso de la API
CREATE TABLE usage_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    
    -- Información de la consulta
    action_type ENUM('ga4_api_call', 'export_data', 'email_report') NOT NULL,
    property_id VARCHAR(255) NULL COMMENT 'Propiedad GA4 consultada',
    
    -- Detalles de la consulta
    query_details JSON NULL COMMENT 'Detalles de la consulta realizada',
    
    -- Metadatos
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    request_id VARCHAR(100) NULL COMMENT 'ID único de la request',
    
    -- Resultado
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT NULL,
    response_time_ms INT UNSIGNED NULL COMMENT 'Tiempo de respuesta en ms',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    INDEX idx_success (success),
    INDEX idx_user_date (user_id, created_at),
    INDEX idx_request_id (request_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 4. Tabla `user_properties`
```sql
-- Propiedades GA4 asociadas a usuarios
CREATE TABLE user_properties (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    
    -- Información de la propiedad GA4
    property_id VARCHAR(255) NOT NULL COMMENT 'ID de la propiedad GA4',
    property_name VARCHAR(255) NOT NULL,
    account_id VARCHAR(255) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    website_url VARCHAR(500) NULL,
    
    -- Configuración específica
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    access_level ENUM('read', 'edit', 'admin') DEFAULT 'read',
    
    -- Metadatos
    last_accessed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_property (user_id, property_id),
    
    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_property_id (property_id),
    INDEX idx_is_default (is_default),
    INDEX idx_last_accessed (last_accessed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 5. Tabla `api_tokens`
```sql
-- Tokens de API para autenticación
CREATE TABLE api_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    
    -- Token information
    token_hash VARCHAR(255) NOT NULL COMMENT 'Hash del token para seguridad',
    token_name VARCHAR(100) DEFAULT 'Extension Token',
    
    -- Capabilities
    abilities JSON NULL COMMENT 'Permisos específicos del token',
    
    -- Usage tracking
    last_used_at TIMESTAMP NULL,
    usage_count INT UNSIGNED DEFAULT 0,
    
    -- Timestamps
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Índices
    INDEX idx_user_id (user_id),
    INDEX idx_token_hash (token_hash),
    INDEX idx_expires_at (expires_at),
    INDEX idx_last_used (last_used_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 6. Tabla `cache_entries`
```sql
-- Cache de consultas GA4 para optimizar rendimiento
CREATE TABLE cache_entries (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Cache key
    cache_key VARCHAR(255) NOT NULL UNIQUE COMMENT 'Hash de la consulta',
    
    -- Data
    data LONGTEXT NOT NULL COMMENT 'Datos cacheados en JSON',
    
    -- Metadata
    user_id BIGINT UNSIGNED NULL,
    property_id VARCHAR(255) NULL,
    query_hash VARCHAR(255) NOT NULL COMMENT 'Hash de la consulta original',
    
    -- TTL
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Índices
    INDEX idx_cache_key (cache_key),
    INDEX idx_expires_at (expires_at),
    INDEX idx_user_property (user_id, property_id),
    INDEX idx_query_hash (query_hash)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 7. Tabla `system_logs`
```sql
-- Logs del sistema para debugging y monitoreo
CREATE TABLE system_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    
    -- Log level and message
    level ENUM('emergency', 'alert', 'critical', 'error', 'warning', 'notice', 'info', 'debug') NOT NULL,
    message TEXT NOT NULL,
    
    -- Context
    context JSON NULL COMMENT 'Contexto adicional del log',
    
    -- Request information
    user_id BIGINT UNSIGNED NULL,
    request_id VARCHAR(100) NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Índices
    INDEX idx_level (level),
    INDEX idx_created_at (created_at),
    INDEX idx_user_id (user_id),
    INDEX idx_request_id (request_id),
    INDEX idx_level_date (level, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 🔧 Migraciones Laravel

### Migration: Create Plans Table
```php
<?php
// database/migrations/2024_01_01_000001_create_plans_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->string('plan_id', 50)->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2)->default(0.00);
            $table->string('currency', 3)->default('USD');
            $table->enum('billing_period', ['monthly', 'yearly', 'lifetime'])->default('monthly');
            $table->unsignedInteger('api_call_limit')->nullable()->comment('NULL = unlimited');
            $table->json('features')->comment('Plan features in JSON format');
            $table->boolean('is_active')->default(true);
            $table->unsignedInteger('sort_order')->default(0);
            $table->timestamps();
            
            $table->index('is_active');
            $table->index('sort_order');
        });
    }

    public function down()
    {
        Schema::dropIfExists('plans');
    }
};
```

### Migration: Create Users Table
```php
<?php
// database/migrations/2024_01_01_000002_create_users_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('google_id')->unique()->comment('Google unique ID');
            $table->string('email');
            $table->string('name')->nullable();
            $table->string('avatar_url', 500)->nullable();
            $table->string('plan_id', 50)->default('free');
            
            // Subscription info
            $table->string('stripe_customer_id')->nullable();
            $table->string('subscription_id')->nullable();
            $table->enum('subscription_status', ['active', 'canceled', 'past_due', 'trialing', 'incomplete'])->nullable();
            $table->timestamp('subscription_current_period_start')->nullable();
            $table->timestamp('subscription_current_period_end')->nullable();
            
            // Usage control
            $table->unsignedInteger('usage_count')->default(0);
            $table->timestamp('usage_reset_at')->nullable();
            
            // User preferences
            $table->json('preferences')->nullable();
            
            // GA4 configuration
            $table->string('selected_property_id')->nullable();
            $table->string('selected_property_name')->nullable();
            
            // Timestamps
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamps();
            $table->timestamp('last_login_at')->nullable();
            $table->softDeletes();
            
            // Foreign keys
            $table->foreign('plan_id')->references('plan_id')->on('plans')->onUpdate('cascade');
            
            // Indexes
            $table->index('google_id');
            $table->index('email');
            $table->index('plan_id');
            $table->index('subscription_status');
            $table->index('created_at');
            $table->index('last_login_at');
        });
    }

    public function down()
    {
        Schema::dropIfExists('users');
    }
};
```

## 📊 Consultas Optimizadas

### Consultas Frecuentes:
```sql
-- Obtener usuario con su plan
SELECT u.*, p.name as plan_name, p.features, p.api_call_limit
FROM users u
JOIN plans p ON u.plan_id = p.plan_id
WHERE u.google_id = ?;

-- Verificar límite de uso
SELECT usage_count, 
       CASE 
         WHEN p.api_call_limit IS NULL THEN 999999 
         ELSE p.api_call_limit 
       END as usage_limit,
       usage_reset_at
FROM users u
JOIN plans p ON u.plan_id = p.plan_id
WHERE u.id = ?;

-- Log de uso por usuario en período
SELECT COUNT(*) as total_calls,
       COUNT(CASE WHEN success = 1 THEN 1 END) as successful_calls,
       AVG(response_time_ms) as avg_response_time
FROM usage_logs
WHERE user_id = ? 
  AND created_at >= ?
  AND created_at <= ?;
```

### Índices de Rendimiento:
- **Compuestos**: Para consultas frecuentes multi-columna
- **Parciales**: Para soft deletes y estados específicos
- **Covering**: Para consultas que solo necesitan índice

## 🔒 Consideraciones de Seguridad

### Datos Sensibles:
- **Tokens**: Hasheados, nunca en texto plano
- **Emails**: Indexados pero no expuestos en logs
- **IPs**: Anonimizadas después de 30 días
- **Queries**: Sin datos personales en cache

### Retención de Datos:
- **Logs de uso**: 1 año
- **Cache**: TTL automático
- **Logs del sistema**: 6 meses
- **Usuarios eliminados**: Soft delete por 30 días

Este esquema proporciona una base sólida, escalable y segura para la aplicación.
