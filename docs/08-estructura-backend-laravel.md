# Estructura del Backend Laravel - GA4 Insights

## 📁 Estructura de Directorios Laravel

```
ga4-insights-backend/
│
├── 📁 app/
│   ├── 📁 Http/
│   │   ├── 📁 Controllers/
│   │   │   ├── 📄 AuthController.php          # Autenticación OAuth Google
│   │   │   ├── 📄 UserController.php          # Gestión de usuarios
│   │   │   ├── 📄 UsageController.php         # Control de límites de uso
│   │   │   └── 📄 PlanController.php          # Gestión de planes
│   │   ├── 📁 Middleware/
│   │   │   ├── 📄 AuthenticateJWT.php         # Verificación JWT
│   │   │   ├── 📄 RateLimitMiddleware.php     # Rate limiting
│   │   │   ├── 📄 RequestLogger.php           # Logging de requests
│   │   │   └── 📄 CorsMiddleware.php          # CORS para extensión
│   │   ├── 📁 Requests/
│   │   │   ├── 📄 VerifyTokenRequest.php      # Validación verify-token
│   │   │   ├── 📄 ConfirmUsageRequest.php     # Validación confirm-usage
│   │   │   └── 📄 UpdatePreferencesRequest.php # Validación preferencias
│   │   └── 📁 Resources/
│   │       ├── 📄 UserResource.php            # Serialización de usuario
│   │       ├── 📄 PlanResource.php            # Serialización de plan
│   │       └── 📄 UsageResource.php           # Serialización de uso
│   ├── 📁 Models/
│   │   ├── 📄 User.php                        # Modelo de usuario
│   │   ├── 📄 Plan.php                        # Modelo de plan
│   │   └── 📄 UsageLog.php                    # Log de uso (opcional)
│   ├── 📁 Services/
│   │   ├── 📄 GoogleAuthService.php           # Integración Google OAuth
│   │   ├── 📄 UsageTrackingService.php        # Tracking de uso
│   │   ├── 📄 PlanManagerService.php          # Lógica de planes
│   │   └── 📄 GA4ApiService.php               # Wrapper para GA4 APIs
│   ├── 📁 Exceptions/
│   │   ├── 📄 UsageLimitExceededException.php # Excepción límite uso
│   │   ├── 📄 InvalidTokenException.php       # Excepción token inválido
│   │   └── 📄 PlanNotFoundException.php       # Excepción plan no encontrado
│   └── 📁 Providers/
│       ├── 📄 AppServiceProvider.php          # Configuración servicios
│       └── 📄 RouteServiceProvider.php        # Configuración rutas
│
├── 📁 config/
│   ├── 📄 cors.php                            # Configuración CORS
│   ├── 📄 logging.php                         # Configuración logging
│   ├── 📄 sanctum.php                         # Configuración Sanctum
│   └── 📄 ga4insights.php                     # Configuración personalizada
│
├── 📁 database/
│   ├── 📁 migrations/
│   │   ├── 📄 2024_01_01_000001_create_plans_table.php
│   │   ├── 📄 2024_01_01_000002_create_users_table.php
│   │   └── 📄 2024_01_01_000003_create_usage_logs_table.php
│   ├── 📁 seeders/
│   │   ├── 📄 PlanSeeder.php                  # Datos iniciales de planes
│   │   └── 📄 DatabaseSeeder.php              # Seeder principal
│   └── 📁 factories/
│       ├── 📄 UserFactory.php                 # Factory para testing
│       └── 📄 PlanFactory.php                 # Factory para testing
│
├── 📁 routes/
│   ├── 📄 api.php                             # Rutas de API
│   └── 📄 web.php                             # Rutas web (si necesario)
│
├── 📁 tests/
│   ├── 📁 Feature/
│   │   ├── 📄 AuthTest.php                    # Tests de autenticación
│   │   ├── 📄 UsageTrackingTest.php           # Tests de uso
│   │   └── 📄 PlanManagementTest.php          # Tests de planes
│   └── 📁 Unit/
│       ├── 📄 GoogleAuthServiceTest.php       # Tests unitarios
│       └── 📄 UsageTrackingServiceTest.php    # Tests unitarios
│
├── 📁 storage/
│   └── 📁 logs/
│       ├── 📄 laravel.log                     # Logs generales
│       └── 📄 ga4-insights.log                # Logs específicos
│
├── 📄 .env                                    # Variables de entorno
├── 📄 .env.example                            # Ejemplo de variables
├── 📄 composer.json                           # Dependencias PHP
├── 📄 artisan                                 # CLI de Laravel
└── 📄 README.md                               # Documentación
```

## 🔧 Configuración Específica

### Variables de Entorno (.env):
```bash
# Aplicación
APP_NAME="GA4 Insights API"
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://pietramundodigital.com

# Base de datos
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ga4_insights
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# JWT/Sanctum
SANCTUM_STATEFUL_DOMAINS=pietramundodigital.com
SESSION_DRIVER=database

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=info

# Cache (para rate limiting)
CACHE_DRIVER=database

# CORS
CORS_ALLOWED_ORIGINS=chrome-extension://*
```

### Configuración CORS (config/cors.php):
```php
<?php

return [
    'paths' => ['api/*'],
    'allowed_methods' => ['*'],
    'allowed_origins' => [
        'chrome-extension://*',
    ],
    'allowed_origins_patterns' => [
        '/^chrome-extension:\/\/[a-z]{32}$/',
    ],
    'allowed_headers' => ['*'],
    'exposed_headers' => ['X-Request-ID'],
    'max_age' => 0,
    'supports_credentials' => false,
];
```

### Configuración Personalizada (config/ga4insights.php):
```php
<?php

return [
    'plans' => [
        'free' => [
            'api_call_limit' => 50,
            'reset_period' => 'monthly', // monthly, daily
        ],
        'premium_monthly' => [
            'api_call_limit' => null, // ilimitado
            'reset_period' => 'monthly',
        ],
    ],
    
    'rate_limits' => [
        'auth' => [
            'max_attempts' => 5,
            'decay_minutes' => 15,
        ],
        'api' => [
            'max_attempts' => 100,
            'decay_minutes' => 1,
        ],
    ],
    
    'google' => [
        'oauth_scopes' => [
            'https://www.googleapis.com/auth/analytics.readonly',
            'email',
            'profile',
        ],
        'token_info_url' => 'https://oauth2.googleapis.com/tokeninfo',
    ],
    
    'cache' => [
        'user_data_ttl' => 3600, // 1 hora
        'plan_data_ttl' => 86400, // 24 horas
    ],
];
```

## 🛣️ Rutas de API (routes/api.php):
```php
<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UsageController;
use App\Http\Controllers\PlanController;

// Rutas públicas (sin autenticación)
Route::prefix('v1')->group(function () {
    // Autenticación
    Route::post('/auth/verify-token', [AuthController::class, 'verifyToken'])
        ->middleware('throttle:auth');
    
    // Planes públicos
    Route::get('/plans', [PlanController::class, 'index']);
});

// Rutas protegidas (requieren JWT)
Route::prefix('v1')->middleware(['auth:sanctum', 'throttle:api'])->group(function () {
    // Autenticación
    Route::post('/auth/refresh-token', [AuthController::class, 'refreshToken']);
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    
    // Usuario
    Route::get('/users/profile', [UserController::class, 'profile']);
    Route::put('/users/preferences', [UserController::class, 'updatePreferences']);
    
    // Uso
    Route::post('/usage/confirm', [UsageController::class, 'confirm']);
    Route::get('/usage/status', [UsageController::class, 'status']);
});
```

## 🧪 Comandos Artisan Personalizados:
```php
<?php
// app/Console/Commands/ResetUsageCounters.php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Carbon\Carbon;

class ResetUsageCounters extends Command
{
    protected $signature = 'ga4insights:reset-usage';
    protected $description = 'Reset usage counters for users based on their billing period';

    public function handle()
    {
        $now = Carbon::now();
        
        $users = User::where('current_period_end', '<=', $now)->get();
        
        foreach ($users as $user) {
            $user->usage_count = 0;
            $user->current_period_start = $now;
            $user->current_period_end = $now->copy()->addMonth();
            $user->save();
            
            $this->info("Reset usage for user: {$user->email}");
        }
        
        $this->info("Usage reset completed for {$users->count()} users");
    }
}
```

## 📦 Dependencias Composer:
```json
{
    "require": {
        "php": "^8.1",
        "laravel/framework": "^10.0",
        "laravel/sanctum": "^3.2",
        "guzzlehttp/guzzle": "^7.2",
        "google/auth": "^1.28",
        "firebase/jwt": "^6.8"
    },
    "require-dev": {
        "phpunit/phpunit": "^10.0",
        "mockery/mockery": "^1.4.4",
        "laravel/pint": "^1.0"
    }
}
```

## 🚀 Comandos de Instalación:
```bash
# Crear proyecto Laravel
composer create-project laravel/laravel ga4-insights-backend

# Instalar dependencias adicionales
composer require laravel/sanctum google/auth firebase/jwt

# Configurar base de datos
php artisan migrate
php artisan db:seed

# Generar clave de aplicación
php artisan key:generate

# Publicar configuración Sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"

# Crear enlace simbólico para storage
php artisan storage:link
```

Esta estructura aprovecha las mejores prácticas de Laravel y se integra perfectamente con tu infraestructura PHP existente en pietramundodigital.com.
