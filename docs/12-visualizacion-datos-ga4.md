# Visualización de Datos GA4 - GA4 Insights

## 📊 Principios de Visualización

### Jerarquía de Información:
1. **KPIs Principales**: Métricas más importantes en la parte superior
2. **Datos de Contexto**: Información de apoyo en secciones secundarias
3. **Acciones**: Botones y controles en la parte inferior

### Diseño Visual:
- **Escaneable**: Información organizada en bloques claros
- **Comparativo**: Indicadores de tendencia y cambios
- **Accionable**: CTAs evidentes para profundizar
- **Responsive**: Adaptable al tamaño del popup

## 🎨 Mockups de Visualización de Datos

### 1. Vista "Esta Página" - Métricas Principales

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│                                         │
│ 📍 /productos/categoria-a               │
│ Como: [🔽 Ruta de Página            ▼] │
│ Período: [🔽 Últimos 7 días         ▼] │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │           MÉTRICAS CLAVE            │ │
│ ├─────────────────────────────────────┤ │
│ │ 👥 Usuarios Activos                 │ │
│ │    142  📈 +12% vs anterior         │ │
│ │                                     │ │
│ │ 👁️  Vistas de Página                │ │
│ │    89   📉 -5% vs anterior          │ │
│ │                                     │ │
│ │ ⚡ Tasa de Interacción              │ │
│ │    68%  📈 +3% vs anterior          │ │
│ │                                     │ │
│ │ ⏱️  Duración Promedio               │ │
│ │    2:34 📈 +8% vs anterior          │ │
│ │                                     │ │
│ │ 📉 Tasa de Rebote                   │ │
│ │    32%  📉 -3% vs anterior          │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [📊 Ver gráfico] [🔄 Actualizar]        │
│                                         │
└─────────────────────────────────────────┘
```

### 2. Vista "Esta Página" - Fuentes de Tráfico

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │        FUENTES DE TRÁFICO           │ │
│ ├─────────────────────────────────────┤ │
│ │ 🔍 google / organic                │ │
│ │    64 usuarios (45%)                │ │
│ │    ████████████████████████████     │ │
│ │                                     │ │
│ │ 📧 email / newsletter              │ │
│ │    33 usuarios (23%)                │ │
│ │    ███████████████                  │ │
│ │                                     │ │
│ │ 🔗 (direct) / (none)               │ │
│ │    26 usuarios (18%)                │ │
│ │    ████████████                     │ │
│ │                                     │ │
│ │ 📱 facebook / social               │ │
│ │    19 usuarios (14%)                │ │
│ │    █████████                        │ │
│ │                                     │ │
│ │ [Ver todas las fuentes...]          │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │         DISPOSITIVOS                │ │
│ ├─────────────────────────────────────┤ │
│ │ 📱 Mobile    78 (55%) ████████████  │ │
│ │ 💻 Desktop   51 (36%) ████████      │ │
│ │ 📟 Tablet    13 (9%)  ██            │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

### 3. Vista "Sitio Completo" - Resumen General

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│                                         │
│ 🏠 Mi Sitio Web (GA4-123456789)        │
│ Período: [🔽 Últimos 28 días        ▼] │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │         RESUMEN GENERAL             │ │
│ ├─────────────────────────────────────┤ │
│ │ 👥 Usuarios Activos    2,847 📈+15% │ │
│ │ 🆕 Usuarios Nuevos     1,203 📈+8%  │ │
│ │ 📊 Sesiones Totales    4,156 📈+12% │ │
│ │ ⚡ Tasa de Interacción   72% 📈+2%  │ │
│ │ 🎯 Conversiones          89 📈+23% │ │
│ │ 💰 Ingresos           €1,234 📈+18% │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │           TENDENCIA                 │ │
│ ├─────────────────────────────────────┤ │
│ │ Usuarios por día (últimos 7 días)   │ │
│ │                                     │ │
│ │ 150 ┤     ●                         │ │
│ │ 100 ┤   ●   ●   ●                   │ │
│ │  50 ┤ ●       ●   ●   ●             │ │
│ │     └─┬─┬─┬─┬─┬─┬─┬─                │ │
│ │      L M M J V S D                  │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ [📈 Ver más gráficos] [📤 Exportar]     │
│                                         │
└─────────────────────────────────────────┘
```

### 4. Vista "Sitio Completo" - Top Páginas

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │         TOP PÁGINAS                 │ │
│ ├─────────────────────────────────────┤ │
│ │ 🏠 /                               │ │
│ │    1,234 vistas │ 68% interacción   │ │
│ │    ████████████████████████████     │ │
│ │                                     │ │
│ │ 🛍️ /productos                      │ │
│ │    567 vistas │ 72% interacción     │ │
│ │    ██████████████                   │ │
│ │                                     │ │
│ │ 📝 /blog/articulo-popular          │ │
│ │    234 vistas │ 85% interacción     │ │
│ │    ██████                           │ │
│ │                                     │ │
│ │ 📞 /contacto                       │ │
│ │    123 vistas │ 45% interacción     │ │
│ │    ███                              │ │
│ │                                     │ │
│ │ 📋 /sobre-nosotros                 │ │
│ │    89 vistas │ 62% interacción      │ │
│ │    ██                               │ │
│ │                                     │ │
│ │ [📄 Ver todas las páginas...]       │ │
│ └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

### 5. Gráfico de Tendencias Detallado

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [← Volver] Usuarios Activos - 7 días    │
├─────────────────────────────────────────┤
│                                         │
│ ┌─────────────────────────────────────┐ │
│ │ 200 ┤                               │ │
│ │     │         ●                     │ │
│ │ 150 ┤       ●   ●                   │ │
│ │     │     ●       ●                 │ │
│ │ 100 ┤   ●           ●               │ │
│ │     │ ●               ●             │ │
│ │  50 ┤                   ●           │ │
│ │     └─┬───┬───┬───┬───┬───┬───┬─    │ │
│ │      Lun Mar Mié Jue Vie Sáb Dom   │ │
│ │                                     │ │
│ │ ● Esta página    ○ Sitio completo   │ │
│ └─────────────────────────────────────┘ │
│                                         │
│ 📊 Estadísticas del período:            │
│ • Promedio diario: 142 usuarios        │
│ • Día pico: Viernes (189 usuarios)     │
│ • Día bajo: Domingo (67 usuarios)      │
│ • Crecimiento: +12% vs período anterior│
│ • Tendencia: 📈 Creciente              │
│                                         │
│ [📤 Exportar PNG] [📋 Copiar datos]     │
│                                         │
└─────────────────────────────────────────┘
```

### 6. Estado Sin Datos

```
┌─────────────────────────────────────────┐
│  🔍 GA4 Insights              [⚙️] [❌]  │
├─────────────────────────────────────────┤
│ [📄 Esta Página] [🌐 Sitio Completo]    │
├─────────────────────────────────────────┤
│                                         │
│              [📊 Gráfico]               │
│                                         │
│         Sin datos disponibles           │
│                                         │
│    Esta página no tiene suficientes     │
│      datos en el período seleccionado   │
│                                         │
│    Sugerencias:                         │
│    • Ampliar el período de tiempo       │
│    • Verificar la configuración de GA4  │
│    • Comprobar que la página recibe     │
│      tráfico                            │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │    📅 Ampliar a 30 días             │ │
│  └─────────────────────────────────────┘ │
│                                         │
│  ┌─────────────────────────────────────┐ │
│  │    🌐 Ver sitio completo            │ │
│  └─────────────────────────────────────┘ │
│                                         │
└─────────────────────────────────────────┘
```

## 🎨 Sistema de Colores y Iconos

### Paleta de Colores:
```css
:root {
  /* Colores principales */
  --primary-blue: #1a73e8;
  --success-green: #34a853;
  --warning-orange: #fbbc04;
  --error-red: #ea4335;
  
  /* Colores de métricas */
  --metric-positive: #34a853;
  --metric-negative: #ea4335;
  --metric-neutral: #5f6368;
  
  /* Colores de fondo */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-card: #ffffff;
  --border-light: #e8eaed;
  
  /* Texto */
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-muted: #9aa0a6;
}
```

### Iconos por Categoría:
- **Usuarios**: 👥 👤 🆕
- **Páginas**: 👁️ 📄 🏠
- **Interacción**: ⚡ ⏱️ 📉
- **Fuentes**: 🔍 📧 🔗 📱
- **Dispositivos**: 💻 📱 📟
- **Tendencias**: 📈 📉 📊
- **Estados**: ✅ ⚠️ ❌ 🔄

## 🔧 Implementación de Componentes

### 1. Componente de Métrica
```javascript
// ui.js - Componente de métrica individual
function createMetricCard(metric) {
    const trend = metric.change > 0 ? 'positive' : 
                  metric.change < 0 ? 'negative' : 'neutral';
    const trendIcon = metric.change > 0 ? '📈' : 
                      metric.change < 0 ? '📉' : '➡️';
    
    return `
        <div class="metric-card">
            <div class="metric-header">
                <span class="metric-icon">${metric.icon}</span>
                <span class="metric-name">${metric.name}</span>
            </div>
            <div class="metric-value">
                <span class="value">${formatMetricValue(metric.value, metric.type)}</span>
                <span class="trend trend-${trend}">
                    ${trendIcon} ${formatChange(metric.change)}
                </span>
            </div>
        </div>
    `;
}
```

### 2. Componente de Gráfico Simple
```javascript
// features.js - Crear gráfico con Chart.js
function createTrendChart(canvasId, data, options = {}) {
    Logger.debug('Creating trend chart', { canvasId, dataPoints: data.length });
    
    const ctx = document.getElementById(canvasId).getContext('2d');
    
    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(d => d.label),
            datasets: [{
                label: options.label || 'Usuarios',
                data: data.map(d => d.value),
                borderColor: '#1a73e8',
                backgroundColor: 'rgba(26, 115, 232, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                x: { display: true },
                y: { 
                    display: true,
                    beginAtZero: true
                }
            }
        }
    });
}
```

### 3. Formateo de Valores
```javascript
// utils.js - Formatear valores de métricas
function formatMetricValue(value, type) {
    switch (type) {
        case 'number':
            return new Intl.NumberFormat('es-ES').format(value);
        case 'percentage':
            return `${value}%`;
        case 'duration':
            return formatDuration(value);
        case 'currency':
            return new Intl.NumberFormat('es-ES', {
                style: 'currency',
                currency: 'EUR'
            }).format(value);
        default:
            return value.toString();
    }
}

function formatChange(change) {
    const sign = change > 0 ? '+' : '';
    return `${sign}${change}%`;
}

function formatDuration(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}
```

## 📱 Responsive Design

### Breakpoints:
- **Pequeño**: < 400px (popup mínimo)
- **Mediano**: 400-600px (popup estándar)
- **Grande**: > 600px (popup expandido)

### Adaptaciones:
- **Métricas**: De lista vertical a grid 2x2
- **Gráficos**: Altura ajustable según espacio
- **Texto**: Tamaños de fuente escalables
- **Botones**: Tamaños touch-friendly

Esta especificación asegura que los datos se presenten de manera clara, accionable y visualmente atractiva para los usuarios.
