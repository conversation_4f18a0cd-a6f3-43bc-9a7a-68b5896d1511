# Especificación API del Backend - GA4 Insights

## 🏗️ Arquitectura del Backend

### Stack Tecnológico Seleccionado:
- **Lenguaje**: PHP 8.1+
- **Framework**: Lara<PERSON> 10+ o Symfony 6+ (recomenda<PERSON>)
- **Base de Datos**: MySQL 8.0+ / PostgreSQL 14+
- **Autenticación**: Google OAuth 2.0 + JWT (Laravel Sanctum)
- **Hosting**: Servidor web existente / VPS / Shared Hosting
- **Logging**: Monolog (integrado en Laravel)
- **Validación**: Laravel Validation / Symfony Validator
- **Testing**: PHPUnit + Laravel Testing

### Justificación de Tecnologías:

#### PHP + Laravel:
- **Experiencia**: Aprovecha tu conocimiento existente en PHP
- **Infraestructura**: Compatible con tu web actual
- **Ecosistema**: Excelente soporte para APIs REST y Google APIs
- **Desarrollo**: Rápido desarrollo con Eloquent ORM y Artisan
- **Mantenimiento**: Fácil integración con tu stack actual
- **Costo**: Utiliza infraestructura existente

#### MySQL/PostgreSQL:
- **Compatibilidad**: Se integra con tu base de datos actual
- **Rendimiento**: Excelente para consultas relacionales
- **Escalabilidad**: Probada en aplicaciones de gran escala
- **Herramientas**: Amplio ecosistema de herramientas de administración
- **Backup**: Sistemas de respaldo maduros y confiables

## 🛠️ Estructura de la API

### Base URL:
```
Desarrollo: http://localhost:8000/api/v1
Producción: https://pietramundodigital.com/api/v1
```

### Headers Comunes:
```http
Content-Type: application/json
Authorization: Bearer <jwt_token>
X-API-Version: 1.0
X-Request-ID: <uuid>
Accept: application/json
```

## 📋 Endpoints de la API

### 1. Autenticación

#### `POST /auth/verify-token`
Verifica el token de Google y crea/actualiza usuario en la base de datos.

**Request:**
```json
{
  "googleAccessToken": "ya29.a0AfH6SMC...",
  "clientInfo": {
    "extensionVersion": "0.1.0",
    "userAgent": "Chrome/120.0.0.0"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "userId": "user_123456",
    "email": "<EMAIL>",
    "plan": {
      "planId": "free",
      "name": "Plan Gratuito",
      "apiCallLimit": 50,
      "features": {
        "canCompareDates": false,
        "advancedFilters": false,
        "maxDateRangeDays": 30
      }
    },
    "usage": {
      "currentPeriodStart": "2024-01-01T00:00:00Z",
      "currentPeriodEnd": "2024-01-31T23:59:59Z",
      "usageCount": 15,
      "usageLimit": 50,
      "remaining": 35
    },
    "jwtToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenExpiry": "2024-01-15T12:00:00Z"
  },
  "requestId": "req_789012"
}
```

**Response (401):**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_TOKEN",
    "message": "El token de Google no es válido o ha expirado",
    "details": "Token verification failed"
  },
  "requestId": "req_789012"
}
```

#### `POST /auth/refresh-token`
Renueva el JWT token del usuario.

**Request:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "jwtToken": "new_jwt_token_here",
    "tokenExpiry": "2024-01-15T12:00:00Z"
  }
}
```

### 2. Gestión de Uso

#### `POST /usage/confirm`
Confirma el uso de una funcionalidad y actualiza el contador.

**Request:**
```json
{
  "action": "ga4_api_call",
  "metadata": {
    "propertyId": "properties/123456",
    "reportType": "page_analysis",
    "dimension": "pagePath",
    "dateRange": "7daysAgo"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "allowed": true,
    "usage": {
      "usageCount": 16,
      "usageLimit": 50,
      "remaining": 34,
      "resetDate": "2024-02-01T00:00:00Z"
    }
  },
  "requestId": "req_789013"
}
```

**Response (429):**
```json
{
  "success": false,
  "error": {
    "code": "USAGE_LIMIT_EXCEEDED",
    "message": "Has alcanzado el límite de tu plan actual",
    "details": {
      "currentUsage": 50,
      "limit": 50,
      "resetDate": "2024-02-01T00:00:00Z",
      "upgradeUrl": "https://your-domain.com/upgrade"
    }
  },
  "requestId": "req_789013"
}
```

#### `GET /usage/status`
Obtiene el estado actual de uso del usuario.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "usage": {
      "usageCount": 16,
      "usageLimit": 50,
      "remaining": 34,
      "percentage": 32,
      "resetDate": "2024-02-01T00:00:00Z"
    },
    "plan": {
      "planId": "free",
      "name": "Plan Gratuito"
    }
  }
}
```

### 3. Gestión de Usuarios

#### `GET /users/profile`
Obtiene el perfil del usuario autenticado.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "userId": "user_123456",
    "email": "<EMAIL>",
    "googleId": "google_user_id",
    "plan": {
      "planId": "free",
      "name": "Plan Gratuito",
      "features": {...}
    },
    "preferences": {
      "defaultDimension": "pagePath",
      "defaultDateRange": "7daysAgo",
      "language": "es"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "lastLoginAt": "2024-01-15T10:30:00Z"
  }
}
```

#### `PUT /users/preferences`
Actualiza las preferencias del usuario.

**Request:**
```json
{
  "preferences": {
    "defaultDimension": "landingPage",
    "defaultDateRange": "28daysAgo",
    "language": "en"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "preferences": {
      "defaultDimension": "landingPage",
      "defaultDateRange": "28daysAgo",
      "language": "en"
    }
  }
}
```

### 4. Planes y Suscripciones (Futuro)

#### `GET /plans`
Lista todos los planes disponibles.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "planId": "free",
        "name": "Plan Gratuito",
        "price": 0,
        "currency": "USD",
        "apiCallLimit": 50,
        "features": {
          "canCompareDates": false,
          "advancedFilters": false,
          "maxDateRangeDays": 30,
          "exportData": false
        }
      },
      {
        "planId": "premium_monthly",
        "name": "Plan Premium Mensual",
        "price": 9.99,
        "currency": "USD",
        "apiCallLimit": null,
        "features": {
          "canCompareDates": true,
          "advancedFilters": true,
          "maxDateRangeDays": 365,
          "exportData": true
        }
      }
    ]
  }
}
```

## 🔧 Middleware y Utilidades (Laravel)

### Middleware de Autenticación:
```php
<?php
// app/Http/Middleware/AuthenticateJWT.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\PersonalAccessToken;

class AuthenticateJWT
{
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();

        if (!$token) {
            Log::warning('Missing JWT token', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'endpoint' => $request->path()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'MISSING_TOKEN',
                    'message' => 'Token de autenticación requerido'
                ]
            ], 401);
        }

        $accessToken = PersonalAccessToken::findToken($token);

        if (!$accessToken || !$accessToken->tokenable) {
            Log::error('JWT verification failed', [
                'token_preview' => substr($token, 0, 20) . '...',
                'ip' => $request->ip()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVALID_TOKEN',
                    'message' => 'Token de autenticación inválido'
                ]
            ], 401);
        }

        $request->setUserResolver(function () use ($accessToken) {
            return $accessToken->tokenable;
        });

        Log::debug('JWT token verified', ['user_id' => $accessToken->tokenable->id]);

        return $next($request);
    }
}
```

### Middleware de Rate Limiting:
```php
<?php
// app/Http/Middleware/RateLimitMiddleware.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class RateLimitMiddleware
{
    public function handle(Request $request, Closure $next, $maxAttempts = 100, $decayMinutes = 1)
    {
        $key = $this->resolveRequestSignature($request);
        $maxAttempts = (int) $maxAttempts;
        $decayMinutes = (int) $decayMinutes;

        if ($this->tooManyAttempts($key, $maxAttempts)) {
            Log::warning('Rate limit exceeded', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'endpoint' => $request->path(),
                'key' => $key
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'RATE_LIMIT_EXCEEDED',
                    'message' => 'Demasiadas solicitudes. Intenta de nuevo en ' . $decayMinutes . ' minuto(s).'
                ]
            ], 429);
        }

        $this->hit($key, $decayMinutes);

        return $next($request);
    }

    protected function resolveRequestSignature(Request $request): string
    {
        return sha1($request->ip() . '|' . $request->path());
    }

    protected function tooManyAttempts(string $key, int $maxAttempts): bool
    {
        return Cache::get($key, 0) >= $maxAttempts;
    }

    protected function hit(string $key, int $decayMinutes): void
    {
        Cache::put($key, Cache::get($key, 0) + 1, now()->addMinutes($decayMinutes));
    }
}
```

### Middleware de Logging:
```php
<?php
// app/Http/Middleware/RequestLogger.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class RequestLogger
{
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        $requestId = $request->header('X-Request-ID') ?? Str::uuid()->toString();

        $request->headers->set('X-Request-ID', $requestId);

        Log::info('Request started', [
            'request_id' => $requestId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'user_id' => $request->user()?->id
        ]);

        $response = $next($request);

        $duration = round((microtime(true) - $startTime) * 1000, 2);

        Log::info('Request completed', [
            'request_id' => $requestId,
            'status_code' => $response->getStatusCode(),
            'duration' => $duration . 'ms'
        ]);

        $response->headers->set('X-Request-ID', $requestId);

        return $response;
    }
}
```

## 🗄️ Esquema de Base de Datos SQL

### Tabla `users`:
```sql
-- Migración Laravel: create_users_table.php
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    google_id VARCHAR(255) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    plan_id VARCHAR(50) NOT NULL DEFAULT 'free',
    stripe_customer_id VARCHAR(255) NULL,
    subscription_id VARCHAR(255) NULL,
    subscription_status ENUM('active', 'canceled', 'past_due', 'trialing') NULL,
    usage_count INT UNSIGNED NOT NULL DEFAULT 0,
    current_period_start TIMESTAMP NULL,
    current_period_end TIMESTAMP NULL,
    preferences JSON NULL,
    email_verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,

    INDEX idx_google_id (google_id),
    INDEX idx_email (email),
    INDEX idx_plan_id (plan_id),
    FOREIGN KEY (plan_id) REFERENCES plans(plan_id) ON UPDATE CASCADE
);
```

### Tabla `plans`:
```sql
-- Migración Laravel: create_plans_table.php
CREATE TABLE plans (
    plan_id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    price DECIMAL(8,2) NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    api_call_limit INT UNSIGNED NULL, -- NULL = ilimitado
    features JSON NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Datos iniciales
INSERT INTO plans (plan_id, name, description, price, api_call_limit, features) VALUES
('free', 'Plan Gratuito', 'Plan básico con funcionalidades limitadas', 0.00, 50,
 '{"canCompareDates": false, "advancedFilters": false, "maxDateRangeDays": 30, "exportData": false}'),
('premium_monthly', 'Plan Premium Mensual', 'Acceso completo con consultas ilimitadas', 9.99, NULL,
 '{"canCompareDates": true, "advancedFilters": true, "maxDateRangeDays": 365, "exportData": true}');
```

## 🔍 Sistema de Logging

### Configuración de Monolog (Laravel):
```php
<?php
// config/logging.php

return [
    'default' => env('LOG_CHANNEL', 'stack'),

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['single', 'daily'],
            'ignore_exceptions' => false,
        ],

        'single' => [
            'driver' => 'single',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
        ],

        'daily' => [
            'driver' => 'daily',
            'path' => storage_path('logs/laravel.log'),
            'level' => env('LOG_LEVEL', 'debug'),
            'days' => 14,
        ],

        'ga4_insights' => [
            'driver' => 'daily',
            'path' => storage_path('logs/ga4-insights.log'),
            'level' => 'debug',
            'days' => 30,
        ],
    ],
];
```

### Uso del Logger:
```php
<?php
// En cualquier controlador o servicio

use Illuminate\Support\Facades\Log;

// Logging básico
Log::info('User authenticated', ['user_id' => $user->id]);
Log::error('API call failed', ['error' => $exception->getMessage()]);

// Logging en canal específico
Log::channel('ga4_insights')->info('GA4 API request', [
    'property_id' => $propertyId,
    'dimension' => $dimension,
    'user_id' => $user->id
]);
```

Esta especificación proporciona una base sólida para el desarrollo del backend, con especial atención al logging, manejo de errores y escalabilidad.
