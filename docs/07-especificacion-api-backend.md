# Especificación API del Backend - GA4 Insights

## 🏗️ Arquitectura del Backend

### Stack Tecnológico Seleccionado:
- **Runtime**: Node.js 18+ LTS
- **Framework**: Express.js 4.18+
- **Base de Datos**: Firebase Firestore
- **Autenticación**: Google OAuth 2.0 + JWT
- **Hosting**: Google Cloud Run
- **Logging**: Winston + Google Cloud Logging
- **Validación**: Joi
- **Testing**: Jest + Supertest

### Justificación de Tecnologías:

#### Node.js + Express.js:
- **Ecosistema**: Amplio ecosistema de librerías para Google APIs
- **Rendimiento**: Excelente para I/O intensivo (APIs externas)
- **Desarrollo**: Mismo lenguaje que frontend (JavaScript)
- **Escalabilidad**: Fácil escalado horizontal en Cloud Run
- **Comunidad**: Gran soporte y documentación

#### Firebase Firestore:
- **Integración**: Nativa con Google Cloud y OAuth
- **Escalabilidad**: Automática sin configuración
- **Tiempo Real**: Capacidades real-time para futuras funcionalidades
- **Seguridad**: Reglas de seguridad integradas
- **Costo**: Pay-per-use, ideal para startup

## 🛠️ Estructura de la API

### Base URL:
```
Desarrollo: http://localhost:3000/api/v1
Producción: https://ga4-insights-api.your-domain.com/api/v1
```

### Headers Comunes:
```http
Content-Type: application/json
Authorization: Bearer <jwt_token>
X-API-Version: 1.0
X-Request-ID: <uuid>
```

## 📋 Endpoints de la API

### 1. Autenticación

#### `POST /auth/verify-token`
Verifica el token de Google y crea/actualiza usuario en la base de datos.

**Request:**
```json
{
  "googleAccessToken": "ya29.a0AfH6SMC...",
  "clientInfo": {
    "extensionVersion": "0.1.0",
    "userAgent": "Chrome/120.0.0.0"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "userId": "user_123456",
    "email": "<EMAIL>",
    "plan": {
      "planId": "free",
      "name": "Plan Gratuito",
      "apiCallLimit": 50,
      "features": {
        "canCompareDates": false,
        "advancedFilters": false,
        "maxDateRangeDays": 30
      }
    },
    "usage": {
      "currentPeriodStart": "2024-01-01T00:00:00Z",
      "currentPeriodEnd": "2024-01-31T23:59:59Z",
      "usageCount": 15,
      "usageLimit": 50,
      "remaining": 35
    },
    "jwtToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenExpiry": "2024-01-15T12:00:00Z"
  },
  "requestId": "req_789012"
}
```

**Response (401):**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_TOKEN",
    "message": "El token de Google no es válido o ha expirado",
    "details": "Token verification failed"
  },
  "requestId": "req_789012"
}
```

#### `POST /auth/refresh-token`
Renueva el JWT token del usuario.

**Request:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "jwtToken": "new_jwt_token_here",
    "tokenExpiry": "2024-01-15T12:00:00Z"
  }
}
```

### 2. Gestión de Uso

#### `POST /usage/confirm`
Confirma el uso de una funcionalidad y actualiza el contador.

**Request:**
```json
{
  "action": "ga4_api_call",
  "metadata": {
    "propertyId": "properties/123456",
    "reportType": "page_analysis",
    "dimension": "pagePath",
    "dateRange": "7daysAgo"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "allowed": true,
    "usage": {
      "usageCount": 16,
      "usageLimit": 50,
      "remaining": 34,
      "resetDate": "2024-02-01T00:00:00Z"
    }
  },
  "requestId": "req_789013"
}
```

**Response (429):**
```json
{
  "success": false,
  "error": {
    "code": "USAGE_LIMIT_EXCEEDED",
    "message": "Has alcanzado el límite de tu plan actual",
    "details": {
      "currentUsage": 50,
      "limit": 50,
      "resetDate": "2024-02-01T00:00:00Z",
      "upgradeUrl": "https://your-domain.com/upgrade"
    }
  },
  "requestId": "req_789013"
}
```

#### `GET /usage/status`
Obtiene el estado actual de uso del usuario.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "usage": {
      "usageCount": 16,
      "usageLimit": 50,
      "remaining": 34,
      "percentage": 32,
      "resetDate": "2024-02-01T00:00:00Z"
    },
    "plan": {
      "planId": "free",
      "name": "Plan Gratuito"
    }
  }
}
```

### 3. Gestión de Usuarios

#### `GET /users/profile`
Obtiene el perfil del usuario autenticado.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "userId": "user_123456",
    "email": "<EMAIL>",
    "googleId": "google_user_id",
    "plan": {
      "planId": "free",
      "name": "Plan Gratuito",
      "features": {...}
    },
    "preferences": {
      "defaultDimension": "pagePath",
      "defaultDateRange": "7daysAgo",
      "language": "es"
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "lastLoginAt": "2024-01-15T10:30:00Z"
  }
}
```

#### `PUT /users/preferences`
Actualiza las preferencias del usuario.

**Request:**
```json
{
  "preferences": {
    "defaultDimension": "landingPage",
    "defaultDateRange": "28daysAgo",
    "language": "en"
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "preferences": {
      "defaultDimension": "landingPage",
      "defaultDateRange": "28daysAgo",
      "language": "en"
    }
  }
}
```

### 4. Planes y Suscripciones (Futuro)

#### `GET /plans`
Lista todos los planes disponibles.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "planId": "free",
        "name": "Plan Gratuito",
        "price": 0,
        "currency": "USD",
        "apiCallLimit": 50,
        "features": {
          "canCompareDates": false,
          "advancedFilters": false,
          "maxDateRangeDays": 30,
          "exportData": false
        }
      },
      {
        "planId": "premium_monthly",
        "name": "Plan Premium Mensual",
        "price": 9.99,
        "currency": "USD",
        "apiCallLimit": null,
        "features": {
          "canCompareDates": true,
          "advancedFilters": true,
          "maxDateRangeDays": 365,
          "exportData": true
        }
      }
    ]
  }
}
```

## 🔧 Middleware y Utilidades

### Middleware de Autenticación:
```javascript
// middleware/auth.js
const authenticateJWT = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  
  if (!token) {
    logger.warn('Missing JWT token', { 
      ip: req.ip, 
      userAgent: req.get('User-Agent') 
    });
    return res.status(401).json({
      success: false,
      error: {
        code: 'MISSING_TOKEN',
        message: 'Token de autenticación requerido'
      }
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    logger.debug('JWT token verified', { userId: decoded.userId });
    next();
  } catch (error) {
    logger.error('JWT verification failed', { 
      error: error.message,
      token: token.substring(0, 20) + '...' 
    });
    return res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: 'Token de autenticación inválido'
      }
    });
  }
};
```

### Middleware de Rate Limiting:
```javascript
// middleware/rateLimiter.js
const rateLimit = require('express-rate-limit');

const createRateLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.path
      });
      res.status(429).json(res.locals.rateLimitMessage);
    }
  });
};

// Diferentes límites para diferentes endpoints
const authLimiter = createRateLimiter(
  15 * 60 * 1000, // 15 minutos
  5, // máximo 5 intentos
  'Demasiados intentos de autenticación. Intenta de nuevo en 15 minutos.'
);

const apiLimiter = createRateLimiter(
  60 * 1000, // 1 minuto
  100, // máximo 100 requests
  'Demasiadas solicitudes. Intenta de nuevo en un minuto.'
);
```

### Middleware de Logging:
```javascript
// middleware/logger.js
const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const requestId = req.headers['x-request-id'] || generateUUID();
  
  req.requestId = requestId;
  res.setHeader('X-Request-ID', requestId);

  logger.info('Request started', {
    requestId,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId
  });

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.info('Request completed', {
      requestId,
      statusCode: res.statusCode,
      duration: `${duration}ms`
    });
  });

  next();
};
```

## 🗄️ Modelos de Base de Datos

### Colección `users`:
```javascript
// models/User.js
const userSchema = {
  userId: String, // PK
  googleId: String, // Único, indexado
  email: String, // Indexado
  planId: String, // FK a plans
  stripeCustomerId: String, // Opcional
  subscriptionId: String, // Opcional
  subscriptionStatus: String, // "active", "canceled", "past_due"
  usageCount: Number, // Default: 0
  currentPeriodStart: Timestamp,
  currentPeriodEnd: Timestamp,
  preferences: {
    defaultDimension: String,
    defaultDateRange: String,
    language: String
  },
  createdAt: Timestamp,
  updatedAt: Timestamp,
  lastLoginAt: Timestamp
};
```

### Colección `plans`:
```javascript
// models/Plan.js
const planSchema = {
  planId: String, // PK
  name: String,
  price: Number, // Opcional
  currency: String, // "USD"
  apiCallLimit: Number, // null para ilimitado
  features: {
    canCompareDates: Boolean,
    advancedFilters: Boolean,
    maxDateRangeDays: Number,
    exportData: Boolean
  },
  isActive: Boolean,
  createdAt: Timestamp
};
```

## 🔍 Sistema de Logging

### Configuración de Winston:
```javascript
// utils/logger.js
const winston = require('winston');
const { LoggingWinston } = require('@google-cloud/logging-winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { 
    service: 'ga4-insights-api',
    version: process.env.APP_VERSION || '0.1.0'
  },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// En producción, agregar Google Cloud Logging
if (process.env.NODE_ENV === 'production') {
  logger.add(new LoggingWinston());
}
```

Esta especificación proporciona una base sólida para el desarrollo del backend, con especial atención al logging, manejo de errores y escalabilidad.
