# Especificación: Datos de GA4 a Mostrar en la Extensión

## Decisión de Alcance de Datos

### Opción Seleccionada: **Combinación (c)**
La extensión mostrará una **combinación inteligente** de datos para la URL activa y para la propiedad GA4 completa, con la capacidad de alternar entre vistas.

### Justificación

1. **Relevancia Contextual**: Mostrar datos específicos de la página actual es extremadamente útil para editores de contenido, marketers y desarrolladores que quieren entender el rendimiento de la página que están viendo.

2. **Vista Completa**: También es importante poder ver el rendimiento general de la propiedad para tomar decisiones estratégicas.

3. **Flexibilidad**: Permitir alternar entre ambas vistas da máximo valor al usuario.

4. **Diferenciación**: Muchas herramientas solo muestran datos generales; enfocarse en la página actual es un diferenciador clave.

## Estructura de Datos por Vista

### Vista 1: "Página Actual" (URL Activa)
**Filtro**: `pagePath` coincide con la ruta de la URL activa

#### Métricas Principales:
- **Usuarios Activos** (últimos 7 días)
- **Vistas de Página** (últimos 7 días)
- **Tasa de Interacción** (últimos 7 días)
- **Duración Promedio de Sesión** (últimos 7 días)
- **Tasa de Rebote** (últimos 7 días)

#### Dimensiones de Análisis:
- **Fuentes de Tráfico** (sourceMedium) - Top 5
- **Dispositivos** (deviceCategory)
- **Tendencia Temporal** (date) - últimos 7 días

#### Datos Adicionales:
- **Eventos Principales** en esta página (eventName + eventCount)
- **Páginas de Destino** que llevan a esta página
- **Siguiente Página** más común desde esta página

### Vista 2: "Propiedad Completa" (GA4 Property)
**Filtro**: Sin filtros de página específica

#### Métricas Principales:
- **Usuarios Activos** (período seleccionado)
- **Usuarios Nuevos** (período seleccionado)
- **Sesiones** (período seleccionado)
- **Tasa de Interacción** (período seleccionado)
- **Conversiones** (período seleccionado)
- **Ingresos Totales** (si aplica)

#### Dimensiones de Análisis:
- **Fuentes de Tráfico** (sourceMedium) - Top 10
- **Páginas Principales** (pagePath) - Top 10
- **Dispositivos** (deviceCategory)
- **Países** (country) - Top 5
- **Tendencia Temporal** (date)

## Lógica de Detección de URL

### Proceso de Identificación:
1. **Obtener URL Activa**: Usar `chrome.tabs.query()` para obtener la URL de la pestaña activa
2. **Extraer Ruta**: Parsear la URL para obtener solo la ruta (sin dominio, parámetros de consulta)
3. **Normalización**: 
   - Remover parámetros de consulta por defecto
   - Convertir a minúsculas
   - Manejar trailing slashes de forma consistente
4. **Validación**: Verificar que la URL pertenece a un dominio configurado en GA4

### Ejemplo de Normalización:
```
URL Original: https://ejemplo.com/Productos/Categoria-A?utm_source=google&page=2
Ruta Normalizada: /productos/categoria-a
```

## Interfaz de Usuario

### Selector de Vista:
- **Toggle/Pestañas** en la parte superior del popup
- **"Esta Página"** vs **"Sitio Completo"**
- **Indicador visual** de qué vista está activa

### Información Contextual:
- **URL Actual** mostrada de forma abreviada en vista "Esta Página"
- **Nombre de la Propiedad GA4** en vista "Sitio Completo"
- **Período de Tiempo** claramente visible

## Casos Especiales

### URL No Encontrada en GA4:
- Mostrar mensaje: "No hay datos disponibles para esta página"
- Ofrecer cambiar a vista "Sitio Completo"
- Sugerir verificar configuración de GA4

### Múltiples Propiedades GA4:
- Permitir selección de propiedad
- Recordar última propiedad seleccionada
- Mostrar nombre de propiedad activa

### Páginas Dinámicas:
- Opción para agrupar URLs similares
- Configuración de patrones de URL (para usuarios avanzados)

## Beneficios de esta Aproximación

1. **Accionable**: Los datos de página específica son inmediatamente accionables
2. **Contextual**: Relevante para lo que el usuario está viendo
3. **Completo**: No sacrifica la vista general del sitio
4. **Eficiente**: Reduce la cantidad de datos a procesar y mostrar
5. **Intuitivo**: Fácil de entender qué datos se están mostrando

## Consideraciones Técnicas

### Rendimiento:
- **Cache** separado para datos de página vs. datos generales
- **Consultas optimizadas** con filtros específicos
- **Carga progresiva** (página actual primero, luego datos generales)

### Limitaciones de API:
- **Cuotas de GA4**: Optimizar número de consultas
- **Filtros complejos**: Manejar limitaciones de la API de filtrado
- **Datos en tiempo real**: Clarificar latencia de datos (24-48h típicamente)
