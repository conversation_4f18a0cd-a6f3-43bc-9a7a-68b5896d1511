# Consultas API de GA4 - GA4 Insights

## 🎯 Estrategia de Consultas

### Principios de Optimización:
- **Consultas mínimas**: Combinar métricas relacionadas en una sola consulta
- **Cache inteligente**: <PERSON><PERSON>lizar datos cuando sea posible
- **Filtros eficientes**: Usar filtros específicos para reducir datos
- **Paginación**: Limitar resultados para mejorar rendimiento

## 📊 Consultas para Vista "Esta Página"

### 1. Métricas Principales de Página
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "7daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "screenPageViews" },
    { "name": "engagementRate" },
    { "name": "averageSessionDuration" },
    { "name": "bounceRate" }
  ],
  "dimensions": [
    { "name": "date" }
  ],
  "dimensionFilter": {
    "filter": {
      "fieldName": "pagePath",
      "stringFilter": {
        "matchType": "EXACT",
        "value": "/productos/categoria-a"
      }
    }
  },
  "orderBys": [
    {
      "dimension": {
        "dimensionName": "date"
      }
    }
  ],
  "limit": 7,
  "metricAggregations": ["TOTAL"]
}
```

### 2. Fuentes de Tráfico para Página Específica
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "7daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "sessions" },
    { "name": "engagementRate" }
  ],
  "dimensions": [
    { "name": "sessionSourceMedium" }
  ],
  "dimensionFilter": {
    "filter": {
      "fieldName": "pagePath",
      "stringFilter": {
        "matchType": "EXACT",
        "value": "/productos/categoria-a"
      }
    }
  },
  "orderBys": [
    {
      "metric": {
        "metricName": "activeUsers"
      },
      "desc": true
    }
  ],
  "limit": 5
}
```

### 3. Análisis de Dispositivos para Página
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "7daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "sessions" },
    { "name": "engagementRate" }
  ],
  "dimensions": [
    { "name": "deviceCategory" }
  ],
  "dimensionFilter": {
    "filter": {
      "fieldName": "pagePath",
      "stringFilter": {
        "matchType": "EXACT",
        "value": "/productos/categoria-a"
      }
    }
  },
  "orderBys": [
    {
      "metric": {
        "metricName": "activeUsers"
      },
      "desc": true
    }
  ]
}
```

### 4. Consulta con Dimensión Personalizable
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "7daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "screenPageViews" },
    { "name": "engagementRate" }
  ],
  "dimensions": [
    { "name": "date" }
  ],
  "dimensionFilter": {
    "filter": {
      "fieldName": "{{SELECTED_DIMENSION}}", // pagePath, landingPage, exitPage
      "stringFilter": {
        "matchType": "EXACT",
        "value": "{{PAGE_VALUE}}"
      }
    }
  },
  "orderBys": [
    {
      "dimension": {
        "dimensionName": "date"
      }
    }
  ],
  "limit": 30
}
```

## 🌐 Consultas para Vista "Sitio Completo"

### 5. Resumen General del Sitio
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "28daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "newUsers" },
    { "name": "sessions" },
    { "name": "engagementRate" },
    { "name": "conversions" },
    { "name": "totalRevenue" }
  ],
  "dimensions": [
    { "name": "date" }
  ],
  "orderBys": [
    {
      "dimension": {
        "dimensionName": "date"
      }
    }
  ],
  "limit": 28,
  "metricAggregations": ["TOTAL"]
}
```

### 6. Top Páginas del Sitio
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "28daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "screenPageViews" },
    { "name": "activeUsers" },
    { "name": "engagementRate" },
    { "name": "averageSessionDuration" }
  ],
  "dimensions": [
    { "name": "pagePath" },
    { "name": "pageTitle" }
  ],
  "orderBys": [
    {
      "metric": {
        "metricName": "screenPageViews"
      },
      "desc": true
    }
  ],
  "limit": 10
}
```

### 7. Fuentes de Tráfico Principales
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "28daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "sessions" },
    { "name": "engagementRate" },
    { "name": "conversions" }
  ],
  "dimensions": [
    { "name": "sessionSourceMedium" }
  ],
  "orderBys": [
    {
      "metric": {
        "metricName": "activeUsers"
      },
      "desc": true
    }
  ],
  "limit": 10
}
```

### 8. Análisis Geográfico
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "28daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "sessions" },
    { "name": "engagementRate" }
  ],
  "dimensions": [
    { "name": "country" },
    { "name": "city" }
  ],
  "orderBys": [
    {
      "metric": {
        "metricName": "activeUsers"
      },
      "desc": true
    }
  ],
  "limit": 5
}
```

## 🔄 Consultas de Comparación (Plan Premium)

### 9. Comparación con Período Anterior
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "7daysAgo",
      "endDate": "yesterday",
      "name": "current_period"
    },
    {
      "startDate": "14daysAgo",
      "endDate": "8daysAgo",
      "name": "previous_period"
    }
  ],
  "metrics": [
    { "name": "activeUsers" },
    { "name": "sessions" },
    { "name": "engagementRate" }
  ],
  "dimensions": [
    { "name": "date" }
  ],
  "dimensionFilter": {
    "filter": {
      "fieldName": "pagePath",
      "stringFilter": {
        "matchType": "EXACT",
        "value": "/productos/categoria-a"
      }
    }
  },
  "orderBys": [
    {
      "dimension": {
        "dimensionName": "date"
      }
    }
  ]
}
```

## 🎯 Consultas de Eventos Personalizados

### 10. Eventos en Página Específica
```json
{
  "property": "properties/123456789",
  "dateRanges": [
    {
      "startDate": "7daysAgo",
      "endDate": "yesterday"
    }
  ],
  "metrics": [
    { "name": "eventCount" },
    { "name": "eventsPerSession" }
  ],
  "dimensions": [
    { "name": "eventName" }
  ],
  "dimensionFilter": {
    "andGroup": {
      "expressions": [
        {
          "filter": {
            "fieldName": "pagePath",
            "stringFilter": {
              "matchType": "EXACT",
              "value": "/productos/categoria-a"
            }
          }
        },
        {
          "filter": {
            "fieldName": "eventName",
            "stringFilter": {
              "matchType": "PARTIAL_REGEXP",
              "value": "^(click|scroll|download|form_submit).*"
            }
          }
        }
      ]
    }
  },
  "orderBys": [
    {
      "metric": {
        "metricName": "eventCount"
      },
      "desc": true
    }
  ],
  "limit": 10
}
```

## 🔧 Plantillas de Consulta Dinámicas

### Template Builder para Consultas
```javascript
// api.js - Constructor de consultas dinámicas
class GA4QueryBuilder {
    constructor(propertyId) {
        this.propertyId = propertyId;
        this.query = {
            property: `properties/${propertyId}`,
            dateRanges: [],
            metrics: [],
            dimensions: [],
            limit: 10
        };
        
        Logger.debug('GA4QueryBuilder initialized', { propertyId });
    }
    
    setDateRange(startDate, endDate, name = null) {
        const dateRange = { startDate, endDate };
        if (name) dateRange.name = name;
        
        this.query.dateRanges.push(dateRange);
        Logger.debug('Date range added', { startDate, endDate, name });
        return this;
    }
    
    addMetrics(metrics) {
        const metricObjects = metrics.map(name => ({ name }));
        this.query.metrics.push(...metricObjects);
        Logger.debug('Metrics added', { metrics });
        return this;
    }
    
    addDimensions(dimensions) {
        const dimensionObjects = dimensions.map(name => ({ name }));
        this.query.dimensions.push(...dimensionObjects);
        Logger.debug('Dimensions added', { dimensions });
        return this;
    }
    
    addPageFilter(dimension, pageValue) {
        this.query.dimensionFilter = {
            filter: {
                fieldName: dimension,
                stringFilter: {
                    matchType: "EXACT",
                    value: pageValue
                }
            }
        };
        Logger.debug('Page filter added', { dimension, pageValue });
        return this;
    }
    
    setLimit(limit) {
        this.query.limit = limit;
        return this;
    }
    
    setOrderBy(field, isMetric = false, desc = true) {
        const orderBy = {};
        if (isMetric) {
            orderBy.metric = { metricName: field };
        } else {
            orderBy.dimension = { dimensionName: field };
        }
        orderBy.desc = desc;
        
        this.query.orderBys = [orderBy];
        Logger.debug('Order by set', { field, isMetric, desc });
        return this;
    }
    
    build() {
        Logger.info('GA4 query built', { 
            metrics: this.query.metrics.length,
            dimensions: this.query.dimensions.length,
            hasFilter: !!this.query.dimensionFilter
        });
        return { ...this.query };
    }
}
```

### Uso del Query Builder
```javascript
// features.js - Ejemplos de uso
async function getPageAnalytics(propertyId, pagePath, dimension, dateRange) {
    Logger.info('Building page analytics query', { 
        propertyId, pagePath, dimension, dateRange 
    });
    
    const query = new GA4QueryBuilder(propertyId)
        .setDateRange(dateRange.start, dateRange.end)
        .addMetrics(['activeUsers', 'screenPageViews', 'engagementRate'])
        .addDimensions(['date'])
        .addPageFilter(dimension, pagePath)
        .setOrderBy('date', false, false)
        .setLimit(30)
        .build();
    
    return await API.runGA4Report(query);
}

async function getTopPages(propertyId, dateRange) {
    Logger.info('Building top pages query', { propertyId, dateRange });
    
    const query = new GA4QueryBuilder(propertyId)
        .setDateRange(dateRange.start, dateRange.end)
        .addMetrics(['screenPageViews', 'activeUsers', 'engagementRate'])
        .addDimensions(['pagePath', 'pageTitle'])
        .setOrderBy('screenPageViews', true, true)
        .setLimit(10)
        .build();
    
    return await API.runGA4Report(query);
}
```

## 📈 Optimizaciones de Rendimiento

### Estrategias de Cache:
1. **Cache por consulta**: Hash de la consulta como clave
2. **TTL diferenciado**: Datos recientes (15min), históricos (1h)
3. **Invalidación inteligente**: Al cambiar propiedad o período
4. **Compresión**: Comprimir respuestas grandes

### Límites y Cuotas:
- **Consultas por día**: 25,000 (límite de Google)
- **Consultas por usuario gratuito**: 50/mes
- **Timeout**: 30 segundos por consulta
- **Retry**: 3 intentos con backoff exponencial

### Manejo de Errores:
```javascript
// api.js - Manejo de errores específicos de GA4
function handleGA4ApiError(error, query) {
    Logger.error('GA4 API error', { 
        error: error.message,
        status: error.status,
        query: JSON.stringify(query, null, 2)
    });
    
    switch (error.status) {
        case 400:
            throw new Error('Consulta inválida. Verifica los parámetros.');
        case 403:
            throw new Error('Sin permisos para acceder a esta propiedad.');
        case 429:
            throw new Error('Límite de consultas alcanzado. Intenta más tarde.');
        case 500:
            throw new Error('Error del servidor de Google Analytics.');
        default:
            throw new Error('Error desconocido al consultar GA4.');
    }
}
```

Estas consultas proporcionan una base sólida para obtener todos los datos necesarios de GA4 de manera eficiente y optimizada.
