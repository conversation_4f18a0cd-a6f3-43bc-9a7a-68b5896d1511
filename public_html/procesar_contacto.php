<?php
// --- Configuración ---
// Correo electrónico donde recibirás los mensajes de contacto
$recipient_email = "<EMAIL>"; // ¡TU EMAIL!
$email_subject_prefix = "[Contacto Web] ";

// Clave secreta de reCAPTCHA v3 (¡Mantenla segura!)
$recaptcha_secret_key = "6LcJ_QsrAAAAAMh3dsrvv1IhzRSPD3NAVRbTeAkx"; // ¡TU CLAVE SECRETA!

// Configuración SMTP (¡Reemplaza con tus datos!)
$smtp_host = 'smtp.gmail.com'; // O el de tu proveedor
$smtp_port = 465;                 // 465 para SSL, 587 para TLS
$smtp_security = 'ssl';           // 'ssl' o 'tls'
$smtp_username = '<EMAIL>'; // Tu dirección de correo SMTP
$smtp_password = 'cncw upcr pohw lejy'; // Tu contraseña de aplicación SMTP

// --- <PERSON>luir PHP<PERSON>ailer ---
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Si usas Composer:
// require 'vendor/autoload.php'; // Comentado porque usamos instalación manual
// Si descargaste manualmente (ajusta la ruta si es necesario):

require 'libs/PHPMailer/src/Exception.php';
require 'libs/PHPMailer/src/PHPMailer.php';
require 'libs/PHPMailer/src/SMTP.php';

// --- Funciones Auxiliares ---
function clean_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// --- Validaciones ---
$errors = [];
$response_status = 'contact_error'; // Estado por defecto
$error_message = ''; // Para mensajes específicos

// Verificar método POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // 1. Validar reCAPTCHA
    if (!isset($_POST['recaptcha_response'])) {
        $errors[] = 'No se recibió el token reCAPTCHA.';
    } else {
        $recaptcha_token = $_POST['recaptcha_response'];
        $recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
        $recaptcha_data = [
            'secret'   => $recaptcha_secret_key,
            'response' => $recaptcha_token,
            'remoteip' => $_SERVER['REMOTE_ADDR'],
        ];

        $options = [
            'http' => [
                'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => http_build_query($recaptcha_data),
            ],
        ];
        $context = stream_context_create($options);
        $recaptcha_result_json = @file_get_contents($recaptcha_url, false, $context);

        if ($recaptcha_result_json === FALSE) {
            $errors[] = 'No se pudo conectar con el servicio reCAPTCHA.';
        } else {
            $recaptcha_result = json_decode($recaptcha_result_json);
            if (!$recaptcha_result || !$recaptcha_result->success || $recaptcha_result->score < 0.5 || $recaptcha_result->action != 'submit_contacto') {
                $errors[] = 'Verificación reCAPTCHA fallida. Por favor, inténtalo de nuevo.';
                 // error_log("reCAPTCHA failed. Result: " . print_r($recaptcha_result, true));
            }
        }
    }

    // 2. Validar datos del formulario (si reCAPTCHA pasó)
    if (empty($errors)) {
        $nombre = clean_input($_POST['contact-name'] ?? '');
        $email = clean_input($_POST['contact-email'] ?? '');
        $telefono = clean_input($_POST['contact-phone'] ?? '');
        $servicio_interes = clean_input($_POST['contact-service'] ?? '');
        $mensaje = clean_input($_POST['contact-message'] ?? '');
        $privacy_policy = isset($_POST['privacy_policy']) && $_POST['privacy_policy'] === 'accepted';

        // Campos obligatorios
        if (empty($nombre)) $errors[] = 'El nombre es obligatorio.';
        if (empty($email)) $errors[] = 'El email es obligatorio.';
        elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'El formato del email no es válido.';
        // if (empty($mensaje)) $errors[] = 'El mensaje es obligatorio.'; // Comentada la validación del mensaje
        if (!$privacy_policy) $errors[] = 'Debes aceptar la política de privacidad.';

        // Si no hay errores de validación, intentar enviar email
        if (empty($errors)) {
            $mail = new PHPMailer(true);

            try {
                // Configuración del servidor SMTP
                $mail->isSMTP();
                $mail->Host       = $smtp_host;
                $mail->SMTPAuth   = true;
                $mail->Username   = $smtp_username;
                $mail->Password   = $smtp_password;
                $mail->SMTPSecure = ($smtp_security === 'ssl') ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
                $mail->Port       = $smtp_port;
                $mail->CharSet    = 'UTF-8';

                // Remitente y Destinatario(s)
                $mail->setFrom($smtp_username, 'Contacto Web PietraMundo');
                $mail->addAddress($recipient_email);
                $mail->addReplyTo($email, $nombre);

                // Contenido del Email
                $mail->isHTML(true);
                $mail->Subject = $email_subject_prefix . $nombre;

                $body = "<h2>Nuevo mensaje desde el Formulario de Contacto:</h2>";
                $body .= "<p><strong>Nombre:</strong> " . $nombre . "</p>";
                $body .= "<p><strong>Email:</strong> " . $email . "</p>";
                if (!empty($telefono)) $body .= "<p><strong>Teléfono:</strong> " . $telefono . "</p>";
                if (!empty($servicio_interes)) $body .= "<p><strong>Servicio de Interés:</strong> " . $servicio_interes . "</p>";
                $body .= "<p><strong>Mensaje:</strong><br>" . nl2br($mensaje) . "</p>";
                $body .= "<hr><p><em>Enviado desde el formulario de contacto de pietramundodigital.com</em></p>";

                $mail->Body = $body;

                $altBody = "Nuevo mensaje desde el Formulario de Contacto:\n";
                $altBody .= "Nombre: " . $nombre . "\n";
                $altBody .= "Email: " . $email . "\n";
                if (!empty($telefono)) $altBody .= "Teléfono: " . $telefono . "\n";
                if (!empty($servicio_interes)) $altBody .= "Servicio de Interés: " . $servicio_interes . "\n";
                $altBody .= "Mensaje:\n" . $mensaje . "\n";
                $altBody .= "\n---\nEnviado desde el formulario de contacto de pietramundodigital.com";

                $mail->AltBody = $altBody;

                $mail->send();
                $response_status = 'contact_success'; // Éxito

            } catch (Exception $e) {
                $errors[] = "Error al enviar el mensaje: {$mail->ErrorInfo}";
                $error_message = $mail->ErrorInfo; // Guardar mensaje específico
                // error_log("PHPMailer Error (Contacto): " . $e->getMessage());
            }
        }
    }

} else {
    $errors[] = 'Método de solicitud no válido.';
}

// --- Respuesta JSON --- 
header('Content-Type: application/json'); // Indicar que la respuesta es JSON

$response = [];

if ($response_status === 'contact_success') {
    $response['status'] = 'success';
    $response['message'] = '¡Mensaje enviado con éxito! En breve nos pondremos en contacto.'; // Mensaje específico
} else {
    $response['status'] = 'error';
    // Construir mensaje de error detallado
    $error_detail = implode(" ", $errors); // Concatenar todos los errores
    if (!empty($error_message)) { // Añadir error SMTP si existe
        $error_detail .= " (Detalle SMTP: " . $error_message . ")";
    }
    $response['message'] = $error_detail ?: 'Error desconocido al procesar el mensaje.';
}

echo json_encode($response);
exit; // Detener el script después de enviar la respuesta JSON
?> 