<?php
// Habilitar muestra de errores (SOLO PARA DEPURACIÓN - Comentar/eliminar en producción)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir el autoloader de Composer
// Asume que este script está en public_html y la carpeta vendor está ahí
require __DIR__ . '/vendor/autoload.php';

// --- Configuración ---
// Ruta COMPLETA y SEGURA al archivo JSON de la clave de la cuenta de servicio
$keyFilePath = '/home/<USER>/google_api_credentials/pietra-mundo-digital-4647ba8bf35a.json'; // <-- RUTA DEDUCIDA

// Verificar si el archivo de clave existe antes de continuar
if (!file_exists($keyFilePath)) {
    die('<p style="color: red;">Error Crítico: El archivo de clave de la cuenta de servicio NO se encontró en la ruta especificada: ' . htmlspecialchars($keyFilePath) . '. Verifica la ruta y los permisos.</p>');
}

// --- Inicializar Cliente de Google ---
try {
    $client = new Google\Client();
    // Establecer un identificador de aplicación (buena práctica)
    $client->setApplicationName("PietraMundoDigital_Website_Reviews");
    $client->setAuthConfig($keyFilePath);
    // Solicitar los permisos necesarios (scopes)
    $client->addScope([
        'https://www.googleapis.com/auth/business.manage', // Permiso general
         // Podríamos intentar ser más específicos si business.manage da problemas,
         // aunque generalmente es necesario para listar cuentas/ubicaciones.
         // 'https://www.googleapis.com/auth/plus.business.manage' // Otro scope relacionado
    ]);

    // Crear servicio de Google My Business (Business Profile)
    $businessProfileService = new Google\Service\MyBusinessBusinessInformation($client);
    // El servicio de Reseñas parece estar dentro del mismo paquete ahora o requiere una clase distinta
    // Vamos a intentar obtenerlo a través del Business Information API como recurso

    echo "<h1>Obteniendo Reseñas de Google</h1>";

    // --- Obtener Cuentas y Ubicaciones ---
    $accounts = $businessProfileService->accounts->listAccounts()->getAccounts();

    if (empty($accounts)) {
        throw new Exception("La cuenta de servicio no tiene acceso a ninguna cuenta de Google Business Profile. Asegúrate de haberla añadido como usuario (Owner/Manager/Viewer) en la configuración del perfil.");
    }
    $firstAccountName = $accounts[0]->getName(); // Formato: accounts/{accountId}
    echo "<p>Cuenta encontrada: " . htmlspecialchars($firstAccountName) . "</p>";

    // Listar ubicaciones
    $locationsResponse = $businessProfileService->accounts_locations->listAccountsLocations($firstAccountName, ['readMask' => 'name,title']);
    $locations = $locationsResponse->getLocations();

     if (empty($locations)) {
        throw new Exception("No se encontraron ubicaciones (negocios) en la cuenta " . htmlspecialchars($firstAccountName));
    }

    // Asumimos la primera ubicación encontrada. Si tienes varias, necesitarías lógica para seleccionarla.
    $locationName = $locations[0]->getName(); // Formato: accounts/{accountId}/locations/{locationId}
    $businessTitle = $locations[0]->getTitle();

    echo "<h2>Reseñas para: " . htmlspecialchars($businessTitle) . "</h2>";
    echo "<p>(Identificador de Ubicación: " . htmlspecialchars($locationName) . ")</p>";

    // --- Obtener Reseñas usando el locationName como 'parent' ---
    // La API de reseñas ahora podría estar bajo MyBusinessVerifications o una estructura similar,
    // o seguir usando el locationName como identificador. Probemos con Business Information.
    // NOTA: La forma exacta puede cambiar con las versiones de la API. Consultar documentación es clave.
    // Vamos a intentar acceder a las reseñas a través del recurso 'locations.reviews'
    $reviewsResponse = $businessProfileService->accounts_locations_reviews->listAccountsLocationsReviews($locationName, [
        'pageSize' => 10,         // Cuántas reseñas obtener por página (máx 50)
        'orderBy' => 'updateTime desc' // Ordenar por fecha de actualización descendente (más recientes primero)
    ]);
    $reviews = $reviewsResponse->getReviews();

    if (empty($reviews)) {
        echo "<p>No se encontraron reseñas para esta ubicación.</p>";
    } else {
        echo "<ul style='list-style: none; padding: 0;'>";
        foreach ($reviews as $review) {
            // Procesar y mostrar cada reseña
            $reviewer = $review->getReviewer();
            $reply = $review->getReviewReply();

            echo "<li style='margin-bottom: 20px; padding: 15px; border: 1px solid #ccc; border-radius: 5px;'>";
            // Estrellas (ejemplo simple con caracteres)
            $stars = $review->getStarRating(); // Devuelve 'FIVE', 'FOUR', etc.
            $starRatingNumeric = 0;
            switch ($stars) {
                case 'FIVE': $starRatingNumeric = 5; break;
                case 'FOUR': $starRatingNumeric = 4; break;
                case 'THREE': $starRatingNumeric = 3; break;
                case 'TWO': $starRatingNumeric = 2; break;
                case 'ONE': $starRatingNumeric = 1; break;
            }
             echo "<strong>Puntuación:</strong> " . str_repeat('⭐', $starRatingNumeric) . " (" . $starRatingNumeric . "/5) <br>";

            // Nombre del autor (si está disponible)
             $displayName = $reviewer && $reviewer->getDisplayName() ? htmlspecialchars($reviewer->getDisplayName()) : 'Anónimo';
            echo "<strong>Autor:</strong> " . $displayName . "<br>";

            // Comentario (si existe)
            if ($review->getComment()) {
                echo "<strong>Comentario:</strong> " . nl2br(htmlspecialchars($review->getComment())) . "<br>";
            }

            // Fecha de actualización
            echo "<em>Fecha: " . date('d/m/Y', strtotime($review->getUpdateTime())) . "</em><br>";

            // Respuesta del propietario (si existe)
            if ($reply && $reply->getComment()) {
                 echo "<blockquote style='margin-top: 10px; margin-left: 20px; border-left: 3px solid #eee; padding-left: 10px; font-size: 0.9em;'>";
                 echo "<strong>Respuesta del propietario:</strong><br>" . nl2br(htmlspecialchars($reply->getComment()));
                 echo "<br><em>Fecha respuesta: " . date('d/m/Y', strtotime($reply->getUpdateTime())) . "</em>";
                 echo "</blockquote>";
            }

            echo "</li>";
        }
        echo "</ul>";

        // Opcional: Manejar paginación si hay más reseñas
        if ($reviewsResponse->getNextPageToken()) {
            echo "<p>Hay más reseñas disponibles (paginación no implementada en este ejemplo).</p>";
        }
    }

} catch (\Google\Service\Exception $serviceException) {
    // Errores específicos de la API de Google
     echo '<p style="color: red;">Error de Servicio Google: ' . htmlspecialchars($serviceException->getMessage()) . '</p>';
     // Mostrar más detalles si están disponibles
     $errors = $serviceException->getErrors();
     if (!empty($errors)) {
         echo '<pre style="color: red;">Detalles: ' . htmlspecialchars(print_r($errors, true)) . '</pre>';
     }
} catch (Exception $e) {
    // Otros errores generales (conexión, configuración, etc.)
    echo '<p style="color: red;">Error General: ' . htmlspecialchars($e->getMessage()) . '</p>';
    // error_log('GMB API Error: ' . $e->getMessage());
}

?>