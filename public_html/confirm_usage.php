<?php
// Habilitar el registro de errores para depuración (opcional, comentar en producción)
// ini_set('log_errors', 1);
// ini_set('error_log', __DIR__ . '/php-error.log'); // Especifica una ruta para el log
// error_reporting(E_ALL);

// Lista de orígenes de extensión de Chrome permitidos
$allowed_origins = [
    'chrome-extension://llglmegjjadpomhgnoofhpboooadegga', // Asumo que este es tu ID de producción o anterior
    'chrome-extension://olgpmdpgmagkdgjmhfceefdclgbgbhnm'  // Tu ID de desarrollo actual
    // Puedes añadir más IDs aquí si es necesario en el futuro
];

// Obtener el origen de la solicitud actual
$request_origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';

// Establecer la cabecera Access-Control-Allow-Origin dinámicamente
if (in_array($request_origin, $allowed_origins)) {
    header("Access-Control-Allow-Origin: " . $request_origin);
} else {
    // Opcional: Si el origen no está en la lista, no se envía la cabecera Access-Control-Allow-Origin,
    // lo que hará que el navegador bloquee la solicitud por CORS por defecto.
    // Podrías loguear el origen no permitido para depuración si lo deseas:
    // error_log("CORS: Origen no permitido - " . $request_origin . " - URI: " . $_SERVER['REQUEST_URI']);
    // Para producción, es mejor no enviar la cabecera si el origen no es explícitamente permitido.
}

// Permitir los métodos HTTP que usa tu extensión
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
// Permitir las cabeceras que usa tu extensión
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
// Opcional: Permitir credenciales si las usas (no es común para tokens Bearer)
// header("Access-Control-Allow-Credentials: true");

// Establecer el tipo de contenido de la respuesta como JSON
// Es importante que esta cabecera se envíe para todas las respuestas que sean JSON.
// Si tu script a veces devuelve HTML u otro contenido, necesitarás una lógica más condicional aquí.
header('Content-Type: application/json');

// Manejar solicitud preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    // Las cabeceras Access-Control-* ya se han establecido arriba.
    // Simplemente respondemos con un 204 No Content.
    http_response_code(204);
    exit;
}

// --- Incluir dependencias ---
// ¡Asegúrate que estas rutas son correctas y están fuera del directorio web si contienen información sensible!
// Considera usar una ruta absoluta basada en __DIR__ para mayor robustez.
$baseDir = __DIR__ . '/../../'; // Asumiendo que api está en public_html/api/
require_once $baseDir . 'includes/config.php';
require_once $baseDir . 'includes/db.php';

// --- Lógica Principal con Try/Catch para devolver errores JSON ---
try {
    // --- Autenticación (Validando Access Token GSC vía UserInfo) ---
    $googleUserId = null;
    $accessToken = null;
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;

    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $accessToken = $matches[1];
        // (Tu lógica de validación de token con Google UserInfo API...)
        // Simulación de la validación del token y obtención del googleUserId
        // En un caso real, aquí llamarías a la API de Google UserInfo.
        // Por ahora, para evitar llamadas externas en este ejemplo, asumimos que si hay token, es válido
        // y extraemos un ID de usuario simulado o lo buscamos en la BD si ya existe.
        // ESTA PARTE ES CRUCIAL Y DEBE SER SEGURA EN PRODUCCIÓN.

        // Ejemplo simplificado (REEMPLAZAR CON VALIDACIÓN REAL):
        // Para este ejemplo, si el token no está vacío, intentaremos buscar un usuario.
        // En producción, DEBES validar el token con Google.
        if (empty($accessToken)) { // Solo como ejemplo, la validación real es más compleja
            throw new Exception('Token de acceso vacío o inválido.');
        }
        // Aquí iría la lógica para obtener $googleUserId a partir del $accessToken
        // Por ejemplo, llamando a la API de Google UserInfo
        // $userInfo = ... (resultado de la llamada a la API de Google)
        // $googleUserId = $userInfo['id'] ?? null;

        // Simulación para este ejemplo:
        // Si tienes una forma de obtener el google_id a partir del token sin llamar a Google aquí
        // (quizás ya lo hiciste en verify.php y lo pasaste de alguna forma), úsala.
        // Si no, necesitarás llamar a Google UserInfo API como en verify.php.
        // Por ahora, vamos a asumir que el token es válido y que ya tienes el google_id
        // (esto es una simplificación para el ejemplo de CORS)
        // En un flujo real, verify.php establecería una sesión con el google_id,
        // y confirm_usage.php usaría esa sesión.
        // Como la extensión envía el token en cada petición, repetimos la validación.

        try {
            $userInfoUrl = 'https://www.googleapis.com/oauth2/v2/userinfo';
            $options = [
                'http' => [
                    'header' => "Authorization: Bearer " . $accessToken . "\r\n" .
                                "Accept: application/json\r\n",
                    'method' => 'GET',
                    'ignore_errors' => true // Para poder leer el cuerpo de la respuesta en caso de error
                ]
            ];
            $context = stream_context_create($options);
            $response = @file_get_contents($userInfoUrl, false, $context); // Usar @ para suprimir errores de file_get_contents si el servidor no responde
            
            if ($response === FALSE) {
                 // Intentar obtener el código de estado de los encabezados de respuesta si están disponibles
                $statusCode = 0;
                if (isset($http_response_header) && is_array($http_response_header) && count($http_response_header) > 0) {
                    preg_match('{HTTP/\d\.\d\s+(\d+)\s+}', $http_response_header[0], $statusMatch);
                    $statusCode = isset($statusMatch[1]) ? (int)$statusMatch[1] : 0;
                }
                error_log("Fallo al conectar con UserInfo API (confirm_usage.php). Token: " . substr($accessToken, 0, 10) . "... Status: " . $statusCode);
                throw new Exception('Error de conexión con el servicio de autenticación de Google.');
            }

            $status_line = $http_response_header[0];
            preg_match('{HTTP/\d\.\d\s+(\d+)\s+}', $status_line, $match_status);
            $statusCode = isset($match_status[1]) ? (int)$match_status[1] : 0;

            if ($statusCode === 200) {
                $userInfo = json_decode($response, true);
                $googleUserId = $userInfo['id'] ?? null;
                if (!$googleUserId) {
                    error_log("UserInfo OK pero sin Google ID en la respuesta para token (confirm_usage): " . substr($accessToken, 0, 10) . "...");
                    throw new Exception('UserInfo no devolvió ID de Google.');
                }
            } else {
                error_log("Token inválido según UserInfo API (confirm_usage.php): Status " . $statusCode . " - Token: " . substr($accessToken, 0, 10) . "...");
                http_response_code(401); // No autorizado
                echo json_encode(['error' => 'Token de autenticación inválido o expirado.']);
                exit;
            }
        } catch (Exception $e) {
            error_log("Error verificando Access Token vía UserInfo (confirm_usage.php): " . $e->getMessage());
            http_response_code(401);
            echo json_encode(['error' => 'Error al verificar token de autenticación: ' . $e->getMessage()]);
            exit;
        }

    } else {
        http_response_code(401); // No autorizado
        echo json_encode(['error' => 'Autenticación requerida (Cabecera Authorization: Bearer <token>).']);
        exit;
    }
    
    if (!$googleUserId) { // Doble verificación por si acaso
        http_response_code(401);
        echo json_encode(['error' => 'Autenticación fallida (No se pudo obtener Google ID).']);
        exit;
    }
    // --- Fin Autenticación ---

    // --- Conectar a la Base de Datos ---
    $db = getDbConnection();
    if (!$db) {
        throw new Exception('No se pudo conectar a la base de datos.');
    }

    // --- Incrementar Contador de Uso ---
    $stmtUser = $db->prepare("SELECT id, plan, usage_count, usage_limit FROM users WHERE google_id = :google_id");
    if (!$stmtUser) throw new Exception("Error preparando consulta SELECT usuario: " . ($db->errorInfo()[2] ?? 'Error desconocido DB'));
    
    $stmtUser->execute([':google_id' => $googleUserId]);
    $user = $stmtUser->fetch(PDO::FETCH_ASSOC);
    // $stmtUser->closeCursor(); // No es necesario si no se reutiliza el statement inmediatamente para otra ejecución

    if (!$user) {
        http_response_code(404); // No encontrado
        echo json_encode(['error' => 'Usuario no encontrado para confirmar uso.']);
        exit;
    }
    
    // Lógica para verificar si el límite de uso ya fue alcanzado ANTES de incrementar
    // Esto es importante si el plan tiene un límite numérico
    $current_usage_limit = $user['usage_limit']; // Podría ser null para ilimitado
    if ($user['plan'] === 'free' && defined('FREE_PLAN_LIMIT')) { // Usar constante para free
        $current_usage_limit = FREE_PLAN_LIMIT;
    }

    if ($current_usage_limit !== null && $user['usage_count'] >= $current_usage_limit) {
        // Límite ya alcanzado, no incrementar, devolver estado actual
        // (La extensión ya debería haber bloqueado esto, pero es una doble seguridad)
        $newState = [
            'google_id' => $googleUserId,
            'email' => null, 
            'plan' => $user['plan'],
            'status' => $user['subscription_status'] ?? 'active', // Asumir active si no está
            'usage_count' => (int)$user['usage_count'],
            'usage_limit' => $current_usage_limit,
            'current_period_end' => $user['current_period_end'] ?? null
        ];
        http_response_code(200); // OK, pero indicando que el límite está alcanzado
        echo json_encode(['success' => true, 'message' => 'Límite de uso ya alcanzado.', 'newState' => $newState, 'limit_reached' => true]);
        exit;
    }


    // Incrementar contador
    $updateStmt = $db->prepare("UPDATE users SET usage_count = usage_count + 1 WHERE id = :user_id");
    if (!$updateStmt) throw new Exception("Error preparando consulta UPDATE: " . ($db->errorInfo()[2] ?? 'Error desconocido DB'));
    
    $updated = $updateStmt->execute([':user_id' => $user['id']]);
    // $updateStmt->closeCursor(); // No es necesario

    if ($updated) {
        $stmtNewState = $db->prepare("SELECT plan, subscription_status, usage_count, usage_limit, current_period_end FROM users WHERE id = :user_id");
        if (!$stmtNewState) throw new Exception("Error preparando consulta SELECT newState: " . ($db->errorInfo()[2] ?? 'Error desconocido DB'));
        
        $stmtNewState->execute([':user_id' => $user['id']]);
        $newStateData = $stmtNewState->fetch(PDO::FETCH_ASSOC);
        // $stmtNewState->closeCursor(); // No es necesario

        if (!$newStateData) {
            throw new Exception("No se pudo obtener el nuevo estado del usuario después de actualizar.");
        }

        // Función para obtener el límite de uso basado en el plan
        function get_usage_limit_confirm($plan_from_db, $usage_limit_from_db) {
            if ($plan_from_db === 'free') {
                return defined('FREE_PLAN_LIMIT') ? FREE_PLAN_LIMIT : 0;
            }
            // Para planes de pago, el límite podría estar en la BD (si es variable) o ser null (ilimitado)
            // O podrías tener constantes también para planes de pago si son fijos.
            // Aquí priorizamos el valor de la BD si existe, sino null.
            return $usage_limit_from_db !== null ? (int)$usage_limit_from_db : null;
        }

        $final_usage_limit = get_usage_limit_confirm($newStateData['plan'], $newStateData['usage_limit']);

        $newState = [
            'google_id' => $googleUserId, // Incluir para consistencia con verify.php si es necesario
            'email' => null, // El email no es necesario en la respuesta de confirm_usage
            'plan' => $newStateData['plan'],
            'status' => $newStateData['subscription_status'] ?? 'active', // Asumir active si no está
            'usage_count' => (int)$newStateData['usage_count'],
            'usage_limit' => $final_usage_limit,
            'current_period_end' => $newStateData['current_period_end'] ?? null
        ];

        http_response_code(200); // OK
        echo json_encode(['success' => true, 'message' => 'Uso confirmado y contador incrementado.', 'newState' => $newState]);

    } else {
        throw new Exception("La consulta UPDATE no afectó ninguna fila. ID Usuario: " . $user['id']);
    }

} catch (PDOException $e) { // Captura errores específicos de PDO
    http_response_code(500);
    error_log("Error PDO en confirm_usage.php: " . $e->getMessage());
    echo json_encode([
        'error' => 'Error de base de datos en confirm_usage.php.',
        'details' => $e->getMessage()
    ]);
} catch (Throwable $e) { // Captura cualquier otro Error o Excepción
    http_response_code(500);
    error_log("Error general en confirm_usage.php: " . $e->getMessage() . " en " . $e->getFile() . ":" . $e->getLine());
    echo json_encode([
        'error' => 'Error interno del servidor en confirm_usage.php.',
        'details' => $e->getMessage(),
        'file' => basename($e->getFile()),
        'line' => $e->getLine()
    ]);
} finally {
    // PDO cierra la conexión automáticamente al final del script si $db sale del alcance
    // o si se establece a null. No es estrictamente necesario aquí.
    // if (isset($db)) { $db = null; }
}

exit;
?>
