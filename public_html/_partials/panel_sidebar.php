<?php
// Este parcial asume que la sesión ya está iniciada y $_SESSION['user_id'] está disponible.
// También necesita acceso a la conexión a la BD si la lógica para mostrar enlaces condicionales se mantiene aquí.

// Si la lógica de obtener plan/estado se hace en cada página llamadora, 
// esas variables ($userPlan, $subscriptionStatus) deberían pasarse o estar disponibles en este scope.
// Por simplicidad, repetimos la consulta mínima aquí.

$userId = $_SESSION['user_id'] ?? null;
$showManageLink = false;

// --- Language Detection (Assuming $currentLang is set in header.php) --- START ---
// If $currentLang is not available, default to Spanish
$currentLang = $currentLang ?? 'es'; 
// --- Language Detection --- END ---


if ($userId) { // Solo intentar consultar si tenemos user ID
    // Incluir DB solo si es necesario (podría optimizarse pasándolo como argumento)
    // require_once __DIR__ . '/../../includes/db.php'; // Ruta desde _partials
    // $dbCheck = getDbConnection(); // Asume que getDbConnection está disponible globalmente o ya incluido
    // Re-initialize $db if it's not available in this scope but needed
    if (!isset($db) || !$db) {
        // Temporarily include db.php if connection not available - review this approach
        require_once __DIR__ . '/../../includes/db.php'; // Asegúrate que la ruta es correcta
        $db = getDbConnection();
    } else {
        $db = $db; // Use existing $db if passed
    }

    if ($db) {
        try {
            $stmtCheck = $db->prepare("SELECT plan, subscription_status FROM users WHERE id = :id");
            $stmtCheck->execute([':id' => $userId]);
            $planData = $stmtCheck->fetch(PDO::FETCH_ASSOC);
            if ($planData && $planData['plan'] !== 'free' && in_array($planData['subscription_status'], ['active', 'trialing', 'past_due'])) {
                $showManageLink = true;
            }
        } catch (PDOException $e) {
            // Manejar el error silenciosamente o registrarlo, para no romper el sidebar
            error_log("Error DB en panel_sidebar.php para User ID {$userId}: " . $e->getMessage());
        }
    } else {
        error_log("Error DB Conn en panel_sidebar.php para User ID {$userId}: No se pudo obtener conexión");
    }
}

// Determinar la página actual para marcarla como activa
// $currentPage = basename($_SERVER['PHP_SELF']); // Using REQUEST_URI is more reliable with routing/subdirs
$requestUri = $_SERVER['REQUEST_URI'];

// Define colores para los planes (basado en gsc-inspector.php)
$planColors = [
    'free' => 'bg-gray-600 text-gray-300',
    'personal' => 'bg-accent text-black',
    'plus' => 'bg-yellow-500 text-black',
    'agency' => 'bg-blue-400 text-white', // Assuming agency uses blue
    'default' => 'bg-gray-500 text-white' // Fallback
];
$currentPlan = $planData['plan'] ?? ($currentLang === 'en' ? 'Unknown' : 'Desconocido');
$currentPlanColor = $planColors[strtolower($currentPlan)] ?? $planColors['default'];

?>
<!-- Sidebar (visible en md y superior, oculto en móvil) -->
<aside class="w-full md:w-64 bg-gray-900 text-gray-300 flex-shrink-0 md:flex flex-col hidden">
    <div class="p-4 border-b border-gray-700">
        <h2 class="text-xl font-semibold text-white mb-1"><?php echo ($currentLang === 'en' ? 'My Account' : 'Mi Cuenta'); ?></h2>
        <?php if ($currentPlan !== ($currentLang === 'en' ? 'Unknown' : 'Desconocido')):
            // Link to the correct pricing page based on language
            $pricingLink = ($currentLang === 'en' ? '/en/gsc-inspector#pricing-plans' : '/gsc-inspector#planes-precios');
         ?>
            <a href="<?= $pricingLink ?>" title="<?php echo ($currentLang === 'en' ? 'View all plans' : 'Ver todos los planes'); ?>">
                <span class="inline-block px-2 py-0.5 <?= $currentPlanColor ?> rounded text-xs font-semibold capitalize hover:opacity-80 transition-opacity">
                    <?= htmlspecialchars($currentPlan) ?>
                </span>
            </a>
        <?php endif; ?>
    </div>
    <nav class="flex-grow p-4 space-y-1">
        <span class="px-3 text-xs font-semibold uppercase text-gray-500 tracking-wider"><?php echo ($currentLang === 'en' ? 'Dashboard' : 'Panel'); ?></span>
        <a href="<?php echo ($currentLang === 'en' ? '/en/panel' : '/panel'); ?>" class="flex items-center px-3 py-2 rounded-md hover:bg-gray-700 hover:text-white transition duration-150 <?= (strpos($requestUri, '/panel') !== false && strpos($requestUri, '/panel/') === false) ? 'bg-gray-700 text-white' : '' ?>">
            <?php echo ($currentLang === 'en' ? 'Overview' : 'Resumen'); ?>
        </a>
        <a href="/panel/perfil" class="flex items-center px-3 py-2 rounded-md hover:bg-gray-700 hover:text-white transition duration-150 <?= (strpos($requestUri, '/panel/perfil') !== false || strpos($requestUri, '/panel/profile') !== false) ? 'bg-gray-700 text-white' : '' ?>">
            <?php echo ($currentLang === 'en' ? 'Profile' : 'Perfil'); ?>
        </a>
         <a href="/panel/cambiar-contrasena" class="flex items-center px-3 py-2 rounded-md hover:bg-gray-700 hover:text-white transition duration-150 <?= (strpos($requestUri, '/panel/cambiar-contrasena') !== false || strpos($requestUri, '/panel/change-password') !== false) ? 'bg-gray-700 text-white' : '' ?>">
             <?php echo ($currentLang === 'en' ? 'Password' : 'Contraseña'); ?>
         </a>

        <?php if ($showManageLink): ?>
            <hr class="border-gray-700 my-2">
            <span class="px-3 text-xs font-semibold uppercase text-gray-500 tracking-wider">Stripe</span>
            <a href="/api/create_customer_portal_session.php" class="flex items-center px-3 py-2 rounded-md hover:bg-gray-700 hover:text-white transition duration-150">
                <?php echo ($currentLang === 'en' ? 'Manage Subscription' : 'Gestionar Suscripción'); ?>
            </a>
             <a href="/api/create_customer_portal_session.php" class="flex items-center px-3 py-2 rounded-md hover:bg-gray-700 hover:text-white transition duration-150" title="<?php echo ($currentLang === 'en' ? 'View billing history' : 'Ver historial de facturas'); ?>">
                 <?php echo ($currentLang === 'en' ? 'Billing' : 'Facturación'); ?>
             </a>
         <?php endif; ?>
    </nav>
    <div class="p-4 mt-auto border-t border-gray-700">
         <!-- Logout Button/Link -->
        <form action="<?php echo ($currentLang === 'en' ? '/en/logout' : '/logout'); ?>" method="POST" class="w-full">
            <button type="submit" class="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition duration-300 flex items-center justify-center">
                 <?php echo ($currentLang === 'en' ? 'Log Out' : 'Cerrar Sesión'); ?>
            </button>
        </form>
    </div>
</aside> 