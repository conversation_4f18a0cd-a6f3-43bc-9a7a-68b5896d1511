<!-- Contacto Section -->
<section id="contacto" class="py-20 bg-darker">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8"> 
        <div class="text-center mb-16">
             <h2 class="text-3xl md:text-4xl font-bold mb-4"><?php echo ($currentLang === 'en' ? 'Contact' : 'Contacta con'); ?> <span class="text-accent"><?php echo ($currentLang === 'en' ? 'Us' : 'Nosotros'); ?></span></h2>
             <p class="text-gray-400 max-w-2xl mx-auto"><?php echo ($currentLang === 'en' ? 'We are here to help you achieve your digital goals' : 'Estamos aquí para ayudarte a alcanzar tus objetivos digitales'); ?></p>
         </div>
         <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
             <!-- Formulario de Contacto -->
             <div class="bg-dark p-8 rounded-custom">
                 <h3 class="text-xl font-bold mb-6 text-accent"><?php echo ($currentLang === 'en' ? 'Write to us' : 'Escríbenos'); ?></h3>
                 <form id="contact-form" action="/procesar_contacto.php" method="post"> <!-- Assuming processor is at root -->
                     <div class="mb-4">
                          <label for="contact-name" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Name' : 'Nombre'); ?><span class="text-red-500 ml-1">*</span></label>
                         <input type="text" id="contact-name" name="contact-name" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5" required>
                     </div>
                     <div class="mb-4">
                          <label for="contact-email" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Email' : 'Email'); ?><span class="text-red-500 ml-1">*</span></label>
                         <input type="email" id="contact-email" name="contact-email" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5" required>
                     </div>
                      <div class="mb-4">
                         <label for="contact-phone" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Phone' : 'Teléfono'); ?></label>
                         <input type="tel" id="contact-phone" name="contact-phone" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5">
                     </div>
                     <div class="mb-4">
                         <label for="contact-service" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Service of interest' : 'Servicio de interés'); ?></label>
                         <select id="contact-service" name="contact-service" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5">
                             <option value=""><?php echo ($currentLang === 'en' ? 'Select a service' : 'Selecciona un servicio'); ?></option>
                             <option value="SEO"><?php echo ($currentLang === 'en' ? 'SEO & Positioning' : 'SEO y Posicionamiento'); ?></option>
                             <option value="web"><?php echo ($currentLang === 'en' ? 'Web Design & Development' : 'Diseño y Desarrollo Web'); ?></option>
                             <option value="contenido"><?php echo ($currentLang === 'en' ? 'Content Marketing' : 'Marketing de Contenidos'); ?></option> 
                             <option value="redes"><?php echo ($currentLang === 'en' ? 'Social Media' : 'Redes Sociales'); ?></option>
                             <option value="ecommerce"><?php echo ($currentLang === 'en' ? 'E-commerce' : 'E-commerce'); ?></option> 
                             <option value="analitica"><?php echo ($currentLang === 'en' ? 'Web Analytics' : 'Analítica Web'); ?></option> 
                             <option value="formacion"><?php echo ($currentLang === 'en' ? 'Training' : 'Formación'); ?></option>
                             <option value="otros"><?php echo ($currentLang === 'en' ? 'Other' : 'Otros'); ?></option>
                         </select>
                     </div>
                     <div class="mb-4">
                         <label for="contact-message" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Message' : 'Mensaje'); ?></label>
                         <textarea id="contact-message" name="contact-message" rows="4" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5"></textarea>
                     </div>
                     <!-- Campo oculto para reCAPTCHA -->
                     <input type="hidden" name="recaptcha_response" id="recaptchaResponse_contact">
                     <!-- Checkbox Política de Privacidad -->
                     <div class="mb-4 flex items-start">
                        <div class="flex items-center h-5">
                            <input id="privacy_policy_contact" name="privacy_policy" type="checkbox" value="accepted" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded" required>
                        </div>
                        <div class="ml-3 text-sm">
                             <?php 
                             // Generate Privacy Policy link based on language
                             $privacyLinkContact = ($currentLang === 'en' ? '/en/privacy-policy' : '/politica-privacidad');
                             ?>
                            <label for="privacy_policy_contact" class="text-gray-400"><?php echo ($currentLang === 'en' ? 'I have read and accept the' : 'He leído y acepto la'); ?> <a href="<?= $privacyLinkContact ?>" target="_blank" class="text-accent hover:underline"><?php echo ($currentLang === 'en' ? 'Privacy Policy' : 'Política de Privacidad'); ?></a><span class="text-red-500 ml-1">*</span></label>
                        </div>
                     </div>
                     <button type="submit" class="w-full bg-accent hover:bg-green-500 text-black font-bold py-3 px-4 rounded-custom transition duration-300">
                         <?php echo ($currentLang === 'en' ? 'Send Message' : 'Enviar Mensaje'); ?>
                     </button>
                     
                     <!-- Mensajes de Estado (Movidos DENTRO del form) -->
                     <div id="contact-success-message" class="mt-4 p-4 bg-green-900 text-green-100 rounded-custom hidden"> <!-- Éxito - hidden por defecto -->
                        <?php echo ($currentLang === 'en' ? 'Message sent successfully! We will contact you shortly.' : '¡Mensaje enviado con éxito! En breve nos pondremos en contacto.'); ?>
                     </div>
                     <div id="contact-error-message" class="mt-4 p-4 bg-red-900 text-red-100 rounded-custom hidden"> <!-- Error - hidden por defecto -->
                        <?php echo ($currentLang === 'en' ? 'Error sending message. Please try again.' : 'Error al enviar el mensaje. Inténtalo de nuevo.'); ?>
                     </div>
                     <!-- Fin Mensajes de Estado -->
                     
                 </form>
             </div>
             <!-- Información de Contacto -->
             <div>
                 <h3 class="text-xl font-bold mb-6 text-accent"><?php echo ($currentLang === 'en' ? 'Contact Information' : 'Información de contacto'); ?></h3>
                  <div class="space-y-6">
                       <div class="flex items-start">
                            <div class="text-accent text-xl mr-4 mt-1"><i class="fas fa-map-marker-alt"></i></div>
                            <div><h4 class="font-bold text-white mb-1"><?php echo ($currentLang === 'en' ? 'Location' : 'Ubicación'); ?></h4><p class="text-gray-400">Madrid / Leganés, España</p><p class="text-gray-400 text-sm"><?php echo ($currentLang === 'en' ? 'In-person services in Madrid and online throughout Spain' : 'Servicios presenciales en Madrid y online para toda España'); ?></p></div>
                       </div>
                        <div class="flex items-start">
                           <div class="text-accent text-xl mr-4 mt-1"><i class="fas fa-envelope"></i></div>
                           <div><h4 class="font-bold text-white mb-1"><?php echo ($currentLang === 'en' ? 'Email' : 'Email'); ?></h4><a href="mailto:<EMAIL>" class="text-gray-400 hover:text-accent transition"><EMAIL></a><p class="text-gray-400 text-sm"><?php echo ($currentLang === 'en' ? 'Response in less than 24 hours' : 'Respuesta en menos de 24 horas'); ?></p></div>
                       </div>
                       <div class="flex items-start">
                           <div class="text-accent text-xl mr-4 mt-1"><i class="fas fa-phone-alt"></i></div>
                           <div><h4 class="font-bold text-white mb-1"><?php echo ($currentLang === 'en' ? 'Phone' : 'Teléfono'); ?></h4><a href="tel:+34634482497" class="text-gray-400 hover:text-accent transition">+34 634 482 497</a><p class="text-gray-400 text-sm"><?php echo ($currentLang === 'en' ? 'Monday to Saturday, 8:00 AM to 8:00 PM' : 'Lunes a Sábados, 8:00 a 20:00 hs'); ?></p></div>
                       </div>
                       <div class="flex items-start">
                           <div class="text-accent text-xl mr-4 mt-1"><i class="fas fa-calendar-alt"></i></div>
                           <div><h4 class="font-bold text-white mb-1"><?php echo ($currentLang === 'en' ? 'Hours' : 'Horario'); ?></h4><p class="text-gray-400"><?php echo ($currentLang === 'en' ? 'Monday to Friday: 9:00 AM - 7:00 PM' : 'Lunes a Viernes: 9:00 - 19:00'); ?></p><p class="text-gray-400"><?php echo ($currentLang === 'en' ? 'Saturdays: 10:00 AM - 2:00 PM' : 'Sábados: 10:00 - 14:00'); ?></p></div>
                       </div>
                 </div>
                 <div class="mt-8">
                     <h4 class="font-bold text-white mb-4"><?php echo ($currentLang === 'en' ? 'Follow us on social media' : 'Síguenos en redes'); ?></h4>
                     <div class="flex space-x-4">
                          <a href="#" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn" class="text-gray-400 hover:text-accent transition duration-300 text-xl"><i class="fab fa-linkedin-in"></i></a>
                          <a href="#" target="_blank" rel="noopener noreferrer" aria-label="Instagram" class="text-gray-400 hover:text-accent transition duration-300 text-xl"><i class="fab fa-instagram"></i></a>
                          <a href="#" target="_blank" rel="noopener noreferrer" aria-label="X (Twitter)" class="text-gray-400 hover:text-accent transition duration-300 text-xl"><i class="fab fa-x-twitter"></i></a>
                     </div>
                 </div>
             </div>
         </div>
    </div>
</section>
