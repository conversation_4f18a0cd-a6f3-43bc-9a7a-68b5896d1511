<!-- Calculadora SEO Section -->
 <section id="calculadora" class="py-20 bg-dark">
     <div class="container mx-auto px-4 sm:px-6 lg:px-8"> 
          <div class="text-center mb-16">
             <h2 class="text-3xl md:text-4xl font-bold mb-4"><?php echo ($currentLang === 'en' ? 'Calculate your' : 'Calcula tu'); ?> <span class="text-accent"><?php echo ($currentLang === 'en' ? 'SEO Plan' : 'Plan SEO'); ?></span></h2>
             <p class="text-gray-400 max-w-2xl mx-auto"><?php echo ($currentLang === 'en' ? 'Select the services you need and we will send you a personalized quote' : 'Selecciona los servicios que necesitas y te enviaremos un presupuesto personalizado'); ?></p>
         </div>
         
         <div class="max-w-4xl mx-auto bg-darker p-8 rounded-custom">
             <form id="seo-calculator-form" action="/procesar_calculadora.php" method="post"> <!-- Assuming processor is at root -->
                 <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                     <!-- Servicios Disponibles -->
                     <div>
                         <h3 class="text-xl font-bold mb-6 text-accent"><?php echo ($currentLang === 'en' ? 'SEO Services' : 'Servicios SEO'); ?></h3>
                         <div class="space-y-4">
                            <div class="flex items-start">
                                 <div class="flex items-center h-5"><input id="auditoria" name="servicios[]" value="Auditoría SEO Completa" type="checkbox" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded"></div>
                                 <div class="ml-3 text-sm"><label for="auditoria" class="font-medium text-white"><?php echo ($currentLang === 'en' ? 'Complete SEO Audit' : 'Auditoría SEO Completa'); ?></label><p class="text-gray-400 text-xs"><?php echo ($currentLang === 'en' ? 'Technical, content, and link analysis of your website' : 'Análisis técnico, de contenido y enlaces de tu sitio web'); ?></p></div>
                             </div>
                            <div class="flex items-start">
                                 <div class="flex items-center h-5"><input id="tecnico" name="servicios[]" value="SEO Técnico" type="checkbox" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded"></div>
                                 <div class="ml-3 text-sm"><label for="tecnico" class="font-medium text-white"><?php echo ($currentLang === 'en' ? 'Technical SEO' : 'SEO Técnico'); ?></label><p class="text-gray-400 text-xs"><?php echo ($currentLang === 'en' ? 'Improvement of speed, indexing, and site structure' : 'Mejora de velocidad, indexación y estructura del sitio'); ?></p></div>
                             </div>
                            <div class="flex items-start">
                                 <div class="flex items-center h-5"><input id="onpage" name="servicios[]" value="SEO On-Page" type="checkbox" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded"></div>
                                 <div class="ml-3 text-sm"><label for="onpage" class="font-medium text-white"><?php echo ($currentLang === 'en' ? 'On-Page SEO' : 'SEO On-Page'); ?></label><p class="text-gray-400 text-xs"><?php echo ($currentLang === 'en' ? 'Optimization of content, meta tags, and internal structure' : 'Optimización de contenido, metaetiquetas y estructura interna'); ?></p></div>
                             </div>
                             <div class="flex items-start">
                                 <div class="flex items-center h-5"><input id="local" name="servicios[]" value="SEO Local" type="checkbox" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded"></div>
                                 <div class="ml-3 text-sm"><label for="local" class="font-medium text-white"><?php echo ($currentLang === 'en' ? 'Local SEO' : 'SEO Local'); ?></label><p class="text-gray-400 text-xs"><?php echo ($currentLang === 'en' ? 'Optimization for local searches and Google Business Profile' : 'Optimización para búsquedas locales y Google My Business'); ?></p></div>
                             </div>
                            <div class="flex items-start">
                                 <div class="flex items-center h-5"><input id="linkbuilding" name="servicios[]" value="Link Building" type="checkbox" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded"></div>
                                 <div class="ml-3 text-sm"><label for="linkbuilding" class="font-medium text-white"><?php echo ($currentLang === 'en' ? 'Link Building' : 'Link Building'); ?></label><p class="text-gray-400 text-xs"><?php echo ($currentLang === 'en' ? 'Strategy for obtaining quality links' : 'Estrategia de obtención de enlaces de calidad'); ?></p></div>
                             </div>
                            <div class="flex items-start">
                                 <div class="flex items-center h-5"><input id="keywords" name="servicios[]" value="Keyword Research" type="checkbox" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded"></div>
                                 <div class="ml-3 text-sm"><label for="keywords" class="font-medium text-white"><?php echo ($currentLang === 'en' ? 'Keyword Research' : 'Keyword Research'); ?></label><p class="text-gray-400 text-xs"><?php echo ($currentLang === 'en' ? 'Identification of strategic keywords' : 'Identificación de palabras clave estratégicas'); ?></p></div>
                             </div>
                            <div class="flex items-start">
                                 <div class="flex items-center h-5"><input id="contenido" name="servicios[]" value="Contenido SEO" type="checkbox" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded"></div>
                                 <div class="ml-3 text-sm"><label for="contenido" class="font-medium text-white"><?php echo ($currentLang === 'en' ? 'SEO Content' : 'Contenido SEO'); ?></label><p class="text-gray-400 text-xs"><?php echo ($currentLang === 'en' ? 'Creation of optimized articles and pages' : 'Creación de artículos y páginas optimizadas'); ?></p></div>
                             </div>
                         </div>
                     </div>
                     <!-- Información del Cliente -->
                     <div>
                         <h3 class="text-xl font-bold mb-6 text-accent"><?php echo ($currentLang === 'en' ? 'Your Details' : 'Tus Datos'); ?></h3>
                             <div class="mb-4">
                                  <label for="nombre" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Name' : 'Nombre'); ?><span class="text-red-500 ml-1">*</span></label>
                                 <input type="text" id="nombre" name="nombre" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5" required>
                             </div>
                             <div class="mb-4">
                                 <label for="email" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Email' : 'Email'); ?><span class="text-red-500 ml-1">*</span></label>
                                 <input type="email" id="email" name="email" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5" required>
                             </div>
                             <div class="mb-4">
                                 <label for="telefono" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Phone' : 'Teléfono'); ?></label>
                                 <input type="tel" id="telefono" name="telefono" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5">
                             </div>
                             <div class="mb-4">
                                 <label for="web" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Website (if any)' : 'Web (si tienes)'); ?></label>
                                 <input type="url" id="web" name="web" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5">
                             </div>
                             <div class="mb-4">
                                 <label for="mensaje" class="block text-sm font-medium text-white mb-1"><?php echo ($currentLang === 'en' ? 'Additional message' : 'Mensaje adicional'); ?></label>
                                 <textarea id="mensaje" name="mensaje" rows="3" class="bg-gray-800 border border-gray-700 text-white text-sm rounded-custom focus:ring-accent focus:border-accent block w-full p-2.5"></textarea>
                             </div>
                              <!-- Campo oculto para reCAPTCHA -->
                             <input type="hidden" name="recaptcha_response" id="recaptchaResponse_calc">
                             <!-- Checkbox Política de Privacidad -->
                             <div class="mb-4 flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="privacy_policy_calc" name="privacy_policy" type="checkbox" value="accepted" class="focus:ring-accent h-4 w-4 text-accent border-gray-300 rounded" required>
                                </div>
                                <div class="ml-3 text-sm">
                                     <?php 
                                     // Generate Privacy Policy link based on language
                                     $privacyLink = ($currentLang === 'en' ? '/en/privacy-policy' : '/politica-privacidad');
                                     ?>
                                    <label for="privacy_policy_calc" class="text-gray-400"><?php echo ($currentLang === 'en' ? 'I have read and accept the' : 'He leído y acepto la'); ?> <a href="<?= $privacyLink ?>" target="_blank" class="text-accent hover:underline"><?php echo ($currentLang === 'en' ? 'Privacy Policy' : 'Política de Privacidad'); ?></a><span class="text-red-500 ml-1">*</span></label>
                                </div>
                             </div>
                             <button type="submit" class="w-full bg-accent hover:bg-green-500 text-black font-bold py-3 px-4 rounded-custom transition duration-300">
                                 <?php echo ($currentLang === 'en' ? 'Request Quote' : 'Solicitar Presupuesto'); ?>
                             </button>
                     </div>
                 </div>
                 
                 <!-- Mensajes de Estado (Dentro del form, antes del cierre) -->
                 <div id="calculator-success-message" class="mt-4 p-4 bg-green-900 text-green-100 rounded-custom hidden"> <!-- Éxito - hidden por defecto -->
                    <?php echo ($currentLang === 'en' ? 'Request sent successfully! You will receive the quote shortly.' : '¡Solicitud enviada con éxito! En breve recibirás el presupuesto.'); ?>
                 </div>
                 <div id="calculator-error-message" class="mt-4 p-4 bg-red-900 text-red-100 rounded-custom hidden"> <!-- Error - hidden por defecto -->
                    <?php echo ($currentLang === 'en' ? 'Error sending request. Please try again.' : 'Error al enviar la solicitud. Inténtalo de nuevo.'); ?>
                 </div>
                 <!-- Fin Mensajes de Estado -->

             </form> 
         </div>
    </div>
</section>
