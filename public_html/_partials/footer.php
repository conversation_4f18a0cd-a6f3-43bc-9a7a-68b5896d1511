    <!-- Footer -->
    <footer class="bg-black py-12">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8"> 
            <div class="flex flex-col md:flex-row justify-between items-center mb-8"> 
                <div class="mb-6 md:mb-0 text-center md:text-left">
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>" class="text-2xl font-bold text-accent">PIETRA<span class="text-white">MUNDODIGITAL</span></a>
                    <p class="text-gray-500 mt-2 text-sm"><?php echo ($currentLang === 'en' ? 'Digital marketing consulting and training' : 'Consultoría y formación en marketing digital'); ?></p>
                     <div class="flex space-x-4 mt-4 justify-center md:justify-start"> 
                        <a href="#" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn" class="text-gray-400 hover:text-accent transition duration-300 text-lg">
                            <i class="fab fa-linkedin-in"></i> 
                        </a>
                        <a href="#" target="_blank" rel="noopener noreferrer" aria-label="Instagram" class="text-gray-400 hover:text-accent transition duration-300 text-lg">
                            <i class="fab fa-instagram"></i> 
                        </a>
                        <a href="#" target="_blank" rel="noopener noreferrer" aria-label="X (Twitter)" class="text-gray-400 hover:text-accent transition duration-300 text-lg">
                            <i class="fab fa-x-twitter"></i> 
                        </a>
                    </div>
                </div>
                
                <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-8 text-center md:text-left">
                    <div>
                        <h4 class="text-white font-bold mb-2"><?php echo ($currentLang === 'en' ? 'Services' : 'Servicios'); ?></h4>
                        <ul class="text-gray-500 space-y-1 text-sm">
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#servicios" class="hover:text-accent transition">SEO</a></li> 
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#servicios" class="hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Web Design' : 'Diseño Web'); ?></a></li>
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#servicios" class="hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Social Media' : 'Redes Sociales'); ?></a></li>
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#formacion" class="hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Training' : 'Formación'); ?></a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-white font-bold mb-2"><?php echo ($currentLang === 'en' ? 'Legal' : 'Legal'); ?></h4>
                        <ul class="text-gray-500 space-y-1 text-sm">
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/privacy-policy' : '/politica-privacidad'); ?>" class="hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Privacy Policy' : 'Política de Privacidad'); ?></a></li> 
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/cookie-policy' : '/politica-cookies'); ?>" class="hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Cookie Policy' : 'Cookies'); ?></a></li> 
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-white font-bold mb-2"><?php echo ($currentLang === 'en' ? 'SEO Tools' : 'Herramientas SEO'); ?></h4>
                        <ul class="text-gray-500 space-y-1 text-sm">
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/gsc-inspector' : '/gsc-inspector'); ?>" class="hover:text-accent transition">GSC Inspector Tool</a></li>
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/gsc-inspector/terms-and-conditions' : '/gsc-inspector/terminos-y-condiciones'); ?>" class="hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Terms & Conditions' : 'Términos y Condiciones'); ?></a></li> 
                            <li><a href="<?php echo ($currentLang === 'en' ? '/en/gsc-inspector/privacy-policy' : '/gsc-inspector/politica-de-privacidad'); ?>" class="hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Privacy Policy' : 'Política de Privacidad'); ?></a></li> 
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-white font-bold mb-2"><?php echo ($currentLang === 'en' ? 'Contact' : 'Contacto'); ?></h4>
                        <ul class="text-gray-500 space-y-1 text-sm">
                            <li><a href="mailto:<EMAIL>" class="hover:text-accent transition"><EMAIL></a></li> <?php // Email clicable ?>
                            <li><a href="tel:+34634482497" class="hover:text-accent transition">+34 634 482 497</a></li> <?php // Teléfono clicable ?>
                            <li><?php echo ($currentLang === 'en' ? 'Madrid, Spain' : 'Madrid, España'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-800 pt-8 text-center"> 
                 <p class="text-gray-500 text-sm">  <?php echo date('Y'); ?> Pietra Mundo Digital. <?php echo ($currentLang === 'en' ? 'All rights reserved.' : 'Todos los derechos reservados.'); ?></p>
            </div>
        </div>
    </footer>

    <?php // Carga de JS local (Ruta absoluta desde la raíz) ?>
    <script src="/assets/js/scripts.js"></script>

    <!-- ==== Modal de Cookies ==== -->
    <div id="cookie-consent-modal" class="fixed bottom-0 left-0 right-0 bg-gray-900 bg-opacity-90 p-4 md:p-6 z-50 hidden text-white">
        <div class="container mx-auto">
            <div class="flex flex-col lg:flex-row justify-between items-center">
                <p class="text-sm md:text-base mb-4 lg:mb-0 lg:mr-6">
                    <?php echo ($currentLang === 'en' ? 
                        'We use our own and third-party cookies to improve your experience. By continuing to browse, you accept their use. You can get more information in our ' :
                        'Utilizamos cookies propias y de terceros para mejorar tu experiencia. Al continuar navegando, aceptas su uso. Puedes obtener más información en nuestra '); ?>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/cookie-policy' : '/politica-cookies'); ?>" class="text-accent hover:underline"><?php echo ($currentLang === 'en' ? 'Cookie Policy' : 'Política de Cookies'); ?></a>.
                </p>
                <div class="flex flex-col sm:flex-row gap-3 flex-shrink-0"> 
                    <button id="manage-cookies-btn" class="bg-gray-700 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-custom transition duration-300 text-sm whitespace-nowrap">
                        <?php echo ($currentLang === 'en' ? 'Manage' : 'Gestionar'); ?>
                    </button>
                    <button id="reject-cookies-btn" class="bg-red-700 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-custom transition duration-300 text-sm whitespace-nowrap">
                        <?php echo ($currentLang === 'en' ? 'Reject' : 'Rechazar'); ?>
                    </button>
                    <button id="accept-cookies-btn" class="bg-accent hover:bg-green-500 text-black font-bold py-2 px-6 rounded-custom transition duration-300 text-sm whitespace-nowrap">
                        <?php echo ($currentLang === 'en' ? 'Accept' : 'Aceptar'); ?>
                    </button>
                </div>
            </div>
             <?php // Panel de gestión ?>
             <div id="cookie-manage-panel" class="hidden mt-4 border-t border-gray-700 pt-4 text-sm">
                  <p class="font-semibold mb-2"><?php echo ($currentLang === 'en' ? 'Manage cookie preferences:' : 'Gestionar preferencias de cookies:'); ?></p>
                 <div class="space-y-2">
                     <div class="flex items-center justify-between">
                         <label for="cookies-necessary" class="flex items-center cursor-pointer">
                             <input type="checkbox" id="cookies-necessary" name="cookies-necessary" checked disabled class="h-4 w-4 text-accent border-gray-500 rounded focus:ring-accent mr-2">
                             <span><?php echo ($currentLang === 'en' ? 'Necessary' : 'Necesarias'); ?></span>
                         </label>
                          <p class="text-xs text-gray-400"><?php echo ($currentLang === 'en' ? 'Always active, essential for functionality.' : 'Siempre activas, esenciales para el funcionamiento.'); ?></p>
                     </div>
                      <div class="flex items-center justify-between">
                         <label for="cookies-analytics" class="flex items-center cursor-pointer">
                             <input type="checkbox" id="cookies-analytics" name="cookies-analytics" checked class="h-4 w-4 text-accent border-gray-500 rounded focus:ring-accent mr-2">
                             <span><?php echo ($currentLang === 'en' ? 'Analytics' : 'Analíticas'); ?></span>
                         </label>
                          <p class="text-xs text-gray-400"><?php echo ($currentLang === 'en' ? 'Help us understand how the website is used.' : 'Nos ayudan a entender cómo se usa la web.'); ?></p>
                     </div>
                      <div class="flex items-center justify-between">
                         <label for="cookies-marketing" class="flex items-center cursor-pointer">
                             <input type="checkbox" id="cookies-marketing" name="cookies-marketing" checked class="h-4 w-4 text-accent border-gray-500 rounded focus:ring-accent mr-2">
                             <span><?php echo ($currentLang === 'en' ? 'Marketing' : 'Marketing'); ?></span>
                         </label>
                          <p class="text-xs text-gray-400"><?php echo ($currentLang === 'en' ? 'Used to display relevant advertising.' : 'Usadas para mostrar publicidad relevante.'); ?></p>
                     </div>
                 </div>
                 <button id="save-cookie-prefs-btn" class="mt-4 bg-blue-600 hover:bg-blue-500 text-white font-bold py-1 px-4 rounded-custom transition duration-300 text-sm">
                     <?php echo ($currentLang === 'en' ? 'Save preferences' : 'Guardar preferencias'); ?>
                 </button>
             </div>
        </div>
    </div>
    <!-- ==== Fin Modal de Cookies ==== -->

</body>
</html>