<?php
// Define un título por defecto si no se pasa uno específico
if (!isset($pageTitle)) {
    $pageTitle = 'Pietra Mundo Digital | Consultoría y Formación en Marketing Digital';
}
// Define descripción y keywords por defecto (puedes hacerlas dinámicas también si quieres)
$pageDescription = isset($pageDescription) ? $pageDescription : 'Expertos en SEO, diseño web y formación en marketing digital en Madrid. Aumenta tu visibilidad online con nuestras estrategias personalizadas.';
$pageKeywords = isset($pageKeywords) ? $pageKeywords : 'SEO Madrid, marketing digital, formación marketing, consultoría SEO, posicionamiento web, redes sociales, analítica web';

// Iniciar sesión para poder verificar si el usuario ya está logueado
if (session_status() === PHP_SESSION_NONE) {
    session_start(); // Iniciar solo si no está iniciada ya
}

// Determinar la URL base para enlaces absolutos (mejora)
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
$domainName = $_SERVER['HTTP_HOST'];
$baseUrl = $protocol . $domainName;

// Variables por defecto para SEO (pueden ser sobrescritas por cada página)
$defaultTitle = 'Pietra Mundo Digital | Consultoría y Formación en Marketing Digital';
$defaultDescription = 'Ayudamos a negocios a crecer online con estrategias SEO, SEM y formación personalizada. Impulsa tu visibilidad y ventas.';
$currentTitle = $pageTitle ?? $defaultTitle;
$currentDescription = $pageDescription ?? $defaultDescription;
$currentCanonical = isset($canonicalUrl) ? $canonicalUrl : $baseUrl . $_SERVER['REQUEST_URI'];

// --- NUEVO: Definir Client ID Web aquí para usarlo abajo --- 
$webClientIdHeader = '37136391139-mpciacvvb3mn7tdtfea58b5dmm4sv0vc.apps.googleusercontent.com';

// --- Hreflang & Language Logic --- START ---
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH); // Get path without query string

$currentLang = 'es'; // Default language
$pathWithoutLang = $path;

// Check if the path starts with /en/
if (strpos($path, '/en/') === 0) {
    $currentLang = 'en';
    // Remove /en prefix, handle homepage case (/en/ becomes /)
    $pathWithoutLang = substr($path, 3); // Removes /en
    if (empty($pathWithoutLang) || $pathWithoutLang === '/') {
         $pathWithoutLang = '/'; // Root path
    } elseif ($pathWithoutLang[0] !== '/') {
        $pathWithoutLang = '/' . $pathWithoutLang; // Ensure leading slash
    }
} elseif ($path === '/') {
    // Explicitly handle root path for Spanish
    $pathWithoutLang = '/';
}

// Construct alternative URLs ($baseUrl is defined earlier)
// Ensure trailing slash consistency for root paths
$esUrl = rtrim($baseUrl, '/') . ($pathWithoutLang === '/' ? '/' : rtrim($pathWithoutLang, '/'));
$enUrl = rtrim($baseUrl, '/') . '/en' . ($pathWithoutLang === '/' ? '/' : rtrim($pathWithoutLang, '/'));

// --- Hreflang & Language Logic --- END ---

?>
<!DOCTYPE html>
<html lang="<?php echo $currentLang; // Dynamic language attribute ?>">
<head>
    <meta charset="UTF-8">
    
    <!-- === Google Consent Mode v2 - Default State === -->
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}

        // Google Consent Mode v2 - Default Consent State
        gtag('consent', 'default', {
          'ad_storage': 'denied',
          'analytics_storage': 'denied',
          'ad_user_data': 'denied',
          'ad_personalization': 'denied',
          'functionality_storage': 'denied', // Opcional pero bueno tenerlo
          'security_storage': 'granted', // Normalmente necesario para la seguridad
          'personalization_storage': 'denied', // Opcional
          'wait_for_update': 500 // Milisegundos para esperar una actualización antes de disparar etiquetas
        });
        // Empujar evento al dataLayer para compatibilidad
        dataLayer.push({'event': 'default_consent'});
    </script>

    <!-- Meta Title, Description, Canonical, Hreflang -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($currentTitle); // Título dinámico ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($currentDescription); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($pageKeywords); ?>">
    <link rel="canonical" href="<?php echo htmlspecialchars($currentCanonical); ?>" />
    
    <!-- Hreflang Tags -->
    <link rel="alternate" hreflang="es" href="<?php echo htmlspecialchars($esUrl); ?>" />
    <link rel="alternate" hreflang="en" href="<?php echo htmlspecialchars($enUrl); ?>" />
    <!-- End Hreflang Tags -->

    <?php if (isset($noIndex) && $noIndex): ?>
    <meta name="robots" content="noindex, nofollow">
    <?php endif; ?>
    
    <!-- === Google Tag Manager === -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5JRZN5J');</script>
    <!-- === End Google Tag Manager === -->

    <link rel="icon" href="/assets/images/favicon.jpg" type="image/jpeg">

    <?php // Carga de Recursos Externos ?>
    <script src="https://cdn.tailwindcss.com"></script> 
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Sintony:wght@400;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"> 
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script> <?php // Asegúrate que el Kit ID es correcto y permite tu dominio ?>

    <?php // Carga de CSS local (Ruta absoluta desde la raíz) ?>
    <link rel="stylesheet" href="/style.css"> 

    <!-- Google reCAPTCHA v3 -->
    <script src="https://www.google.com/recaptcha/api.js?render=6LcJ_QsrAAAAAIPElRrOTj0xVm_iuntmM-kUOP4E"></script> 
    <!-- FIN Google reCAPTCHA v3 -->

    <!-- NUEVO: Google Sign-In Library (también se carga en login.php, pero no daña) -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>

    <?php // Paddle.js - Necesario para Paddle Checkout ?>
    <script src="https://cdn.paddle.com/paddle/paddle.js"></script>

</head>
<body class="bg-darker text-white">
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5JRZN5J" <?php // ID GTM añadido ?>
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <!-- Navigation -->
    <nav class="bg-darker fixed w-full z-50 shadow-lg">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-4"> 
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                     <?php // Logo o Nombre del sitio (enlace a la raíz del idioma actual) ?>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>" class="text-2xl font-bold text-accent">PIETRA<span class="text-white">MUNDODIGITAL</span></a>
                </div>
                <div class="hidden lg:flex items-center space-x-8">
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#servicios" class="nav-link text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Services' : 'Servicios'); ?></a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#formacion" class="nav-link text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Training' : 'Formación'); ?></a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#calculadora" class="nav-link text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'SEO Calculator' : 'Calculadora SEO'); ?></a>
                    <!-- Inicio Nuevo Dropdown Herramientas SEO -->
                    <div class="relative group">
                        <button class="nav-link text-white hover:text-accent transition inline-flex items-center">
                            <?php echo ($currentLang === 'en' ? 'SEO Tools' : 'Herramientas SEO'); ?>
                            <svg class="w-4 h-4 ml-1 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M10 12.586l-4.293-4.293a1 1 0 0 1 1.414-1.414L10 9.758l3.879-3.879a1 1 0 1 1 1.414 1.414L10 12.586z"/></svg>
                        </button>
                        <div class="absolute left-0 mt-2 w-48 bg-dark rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-60">
                            <a href="<?php echo ($currentLang === 'en' ? '/en/gsc-inspector' : '/gsc-inspector'); ?>" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 hover:text-accent">GSC Inspector Tool</a>
                            <!-- Puedes añadir más herramientas aquí en el futuro -->
                        </div>
                    </div>
                    <!-- Fin Nuevo Dropdown Herramientas SEO -->
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#clientes" class="nav-link text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Clients' : 'Clientes'); ?></a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#contacto" class="nav-link text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Contact' : 'Contacto'); ?></a>
                    <!-- Inicio Selector de Idioma -->
                    <div class="relative group ml-4">
                        <button class="nav-link text-white hover:text-accent transition inline-flex items-center">
                            <?php echo strtoupper($currentLang); ?>
                            <svg class="w-4 h-4 ml-1 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M10 12.586l-4.293-4.293a1 1 0 0 1 1.414-1.414L10 9.758l3.879-3.879a1 1 0 1 1 1.414 1.414L10 12.586z"/></svg>
                        </button>
                        <div class="absolute right-0 mt-2 w-20 bg-dark rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-60">
                            <?php if ($currentLang === 'en'): ?>
                                <a href="<?php echo htmlspecialchars($esUrl); ?>" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 hover:text-accent">ES</a>
                            <?php else: ?>
                                <a href="<?php echo htmlspecialchars($enUrl); ?>" class="block px-4 py-2 text-sm text-white hover:bg-gray-700 hover:text-accent">EN</a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <!-- Fin Selector de Idioma -->

                    <!-- Bloque Usuario para Escritorio (lg y superior) -->
                    <div class="flex items-center space-x-2 ml-4"> <!-- Añadido ml-4 y space-x-2 -->
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <a href="/panel" class="bg-accent hover:bg-accent-dark text-black font-medium px-4 py-2 rounded-md transition duration-300">Panel</a>
                            <a href="/logout" class="bg-gray-600 hover:bg-gray-700 text-white font-medium px-4 py-2 rounded-md transition duration-300">Salir</a>
                        <?php else: ?>
                            <a href="<?php echo ($currentLang === 'en' ? '/en/login' : '/login'); ?>" class="bg-accent text-black px-3 py-1 rounded-md text-sm font-medium hover:bg-accent-dark transition duration-300"><?php echo ($currentLang === 'en' ? 'Login' : 'Acceder'); ?></a>
                            <!-- Contenedor para Google One Tap (se mostrará si hay sesión previa de Google) -->
                            <div id="g_id_onload_header"
                                    data-client_id="<?= $webClientIdHeader ?>"
                                    data-context="signin"
                                    data-ux_mode="popup"
                                    data-callback="handleGoogleSignInHeader"
                                    data-auto_prompt="false"> <?php // Cambiado a false para evitar solapamiento con el botón ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <!-- FIN Bloque Usuario para Escritorio -->
                </div>

                <div class="lg:hidden"> <!-- Eliminado pl-3 -->
                    <button id="menu-btn" class="text-white focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <!-- Mobile menu -->
            <div id="mobile-menu" class="hidden lg:hidden mt-4 px-4 sm:px-6 lg:px-8"> 
                <div class="flex flex-col space-y-4">
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#servicios" class="block text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Services' : 'Servicios'); ?></a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#formacion" class="block text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Training' : 'Formación'); ?></a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#calculadora" class="block text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'SEO Calculator' : 'Calculadora SEO'); ?></a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/gsc-inspector' : '/gsc-inspector'); ?>" class="block text-white hover:text-accent transition">GSC Inspector Tool</a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#clientes" class="block text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Clients' : 'Clientes'); ?></a>
                    <a href="<?php echo ($currentLang === 'en' ? '/en/' : '/'); ?>#contacto" class="block text-white hover:text-accent transition"><?php echo ($currentLang === 'en' ? 'Contact' : 'Contacto'); ?></a>
                    <!-- Inicio Selector de Idioma Móvil -->
                     <div class="mt-4 border-t border-gray-700 pt-4">
                        <span class="block text-gray-400 text-sm mb-2">Language:</span>
                         <?php if ($currentLang === 'en'): ?>
                            <a href="<?php echo htmlspecialchars($esUrl); ?>" class="block text-white hover:text-accent transition">Español (ES)</a>
                        <?php else: ?>
                            <a href="<?php echo htmlspecialchars($enUrl); ?>" class="block text-white hover:text-accent transition">English (EN)</a>
                        <?php endif; ?>
                    </div>
                     <!-- Fin Selector de Idioma Móvil -->

                     <!-- Bloque Usuario para Móvil (oculto en lg y superior) -->
                    <div class="lg:hidden mt-4 border-t border-gray-700 pt-4">
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <a href="/panel" class="block text-accent hover:text-white font-semibold transition duration-300">Panel</a>
                            <a href="/logout" class="block text-accent hover:text-white font-semibold transition duration-300 mt-2">Salir</a>
                        <?php else: ?>
                            <a href="<?php echo ($currentLang === 'en' ? '/en/login' : '/login'); ?>" class="block bg-accent text-black px-4 py-2 rounded-md hover:bg-accent-dark transition duration-300 mt-4"><?php echo ($currentLang === 'en' ? 'Login' : 'Acceder'); ?></a>
                        <?php endif; ?>
                    </div>
                     <!-- FIN Bloque Usuario Móvil -->
                </div>
            </div>
        </div>
    </nav> 

    <!-- NUEVO: Script para manejar Google Sign-In desde el Header -->
    <script>
        function handleGoogleSignInHeader(response) {
            console.log("Google Sign-In Response (Header One-Tap):", response);
            const idToken = response.credential;

            // Enviar token al backend (igual que en login.php, podríamos refactorizar a función común)
            fetch('/api/auth/verify_google_web.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ credential: idToken })
            })
            .then(res => {
                if (!res.ok) {
                     return res.json().catch(() => null).then(errData => {
                         throw new Error(errData?.error || `Error del servidor: ${res.statusText}`);
                     });
                }
                return res.json();
            })
            .then(data => {
                if (data.success) {
                    // Éxito: Recargar la página actual para que el header refleje el estado de login
                     window.location.reload();
                } else {
                    // Error: Podríamos mostrar un mensaje no intrusivo o simplemente loguearlo
                    console.error("Error en inicio de sesión automático con Google:", data.error);
                    // alert("Error al iniciar sesión con Google: " + data.error); // Evitar alerts
                }
            })
            .catch(error => {
                console.error('Error en fetch a verify_google_web (header):', error);
                // alert("Error al conectar para inicio de sesión con Google."); // Evitar alerts
            });
        }

        // Script para menú móvil
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }
    </script>

    <!-- El contenido principal de la página irá aquí -->
    <!-- <main> ... </main> -->
</body>
</html> 