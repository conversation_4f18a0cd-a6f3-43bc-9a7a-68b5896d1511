<?php
// public_html/api/status.php

// Permitir acceso desde la extensión de Chrome específica
header("Access-Control-Allow-Origin: chrome-extension://llglmegjjadpomhgnoofhpboooadegga");
// Permitir métodos necesarios (incluyendo OPTIONS para preflight)
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
// Permitir cabeceras necesarias (como Authorization para el token)
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Manejar solicitud preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200); // OK
    exit;
}

// Cargar configuración y helper de BD
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/db.php';
// Cargar dependencias de Composer (necesario si usas JWT u otras librerías para auth)
require_once __DIR__ . '/../../vendor/autoload.php';

// --- Cabeceras CORS y Tipo de Contenido ---
$extensionId = 'llglmegjjadpomhgnoofhpboooadegga'; // ¡ID de prueba proporcionado!
header("Access-Control-Allow-Origin: chrome-extension://" . $extensionId);
header("Access-Control-Allow-Methods: GET, OPTIONS"); // GET para obtener estado
header("Access-Control-Allow-Headers: Content-Type, Authorization"); // Permitir Cabecera Authorization
header('Content-Type: application/json');

// --- Manejo de Petición OPTIONS (Preflight) ---
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(204); // No Content
    exit;
}

// --- Solo permitir método GET ---
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['error' => 'Método no permitido. Se requiere GET.']);
    exit;
}

// --- Autenticación (¡SIMULADA E INSEGURA PARA DEV!) ---
// En producción: Validar un token de sesión (JWT) o el Google ID Token enviado en la cabecera Authorization.
$googleId = null;
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;

if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    $token = $matches[1];
    // Para DEV: Asumimos que el token ES el google_id directamente.
    // ¡NO HACER ESTO EN PRODUCCIÓN!
    // $googleId = $token; 

    // EN PRODUCCIÓN: Deberías validar el token aquí. Si usas Google ID Token:
     $client = new Google_Client(['client_id' => GOOGLE_CLIENT_ID]);
     try {
         $payload = $client->verifyIdToken($token);
         if ($payload) {
             $googleId = $payload['sub'];
         } else {
            // Token inválido o expirado
             http_response_code(401); 
             echo json_encode(['error' => 'Token de autenticación inválido o expirado.']);
             exit;
         }
     } catch (Exception $e) {
         error_log("Token verification error en status.php: " . $e->getMessage());
         http_response_code(401);
         echo json_encode(['error' => 'Error al verificar token de autenticación.']);
         exit;
     }
    // Si usaras tu propio JWT, la validación sería diferente aquí.
    
} else {
    // No se envió cabecera Authorization o no tiene el formato Bearer
    http_response_code(401); // Unauthorized
    echo json_encode(['error' => 'Autenticación requerida (Cabecera Authorization: Bearer <token>).']);
    exit;
}

if (!$googleId) { // Doble chequeo por si la validación falló silenciosamente
    http_response_code(401);
    echo json_encode(['error' => 'Autenticación fallida.']);
    exit;
}
// --- Fin Autenticación ---


// --- Conectar a la Base de Datos ---
$db = getDbConnection();
if (!$db) {
    http_response_code(503); // Service Unavailable
    echo json_encode(['error' => 'No se pudo conectar a la base de datos.']);
    exit;
}

// --- Obtener Estado del Usuario y Resetear Contador Free si es necesario ---
try {
    // Obtener datos del usuario
    $stmt = $db->prepare("SELECT google_id, email, plan, subscription_status, current_period_end, usage_count, last_usage_reset FROM users WHERE google_id = :google_id");
    $stmt->execute([':google_id' => $googleId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        http_response_code(404); // Not Found
        echo json_encode(['error' => 'Usuario no encontrado.']);
        exit;
    }

    // --- Preparar y Devolver Respuesta ---
    $usage_count = isset($user['usage_count']) ? (int)$user['usage_count'] : 0;
    $userData = [
        'google_id' => $user['google_id'],
        'email' => $user['email'],
        'plan' => $user['plan'],
        'status' => $user['subscription_status'],
        'usage_count' => $usage_count,
        'usage_limit' => null,
        'current_period_end' => $user['current_period_end']
    ];

    http_response_code(200); // OK
    echo json_encode($userData);

} catch (PDOException $e) {
    error_log("Database Error en status.php: " . $e->getMessage());
    http_response_code(500); // Internal Server Error
    echo json_encode(['error' => 'Error de base de datos al obtener el estado del usuario.']);
    exit;
} catch (Exception $e) { // Capturar otras excepciones generales
    error_log("General Error en status.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ocurrió un error inesperado al obtener el estado.']);
    exit;
}

?>
