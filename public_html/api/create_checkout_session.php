<?php
// public_html/api/create_checkout_session.php

// --- Habilitar errores para depuración inicial ---
ini_set('display_errors', 1);
error_reporting(E_ALL);
// --- Fin Habilitar errores ---

// --- <PERSON><PERSON><PERSON><PERSON> CORS y Tipo de Contenido ---
header("Access-Control-Allow-Origin: chrome-extension://llglmegjjadpomhgnoofhpboooadegga"); // Reemplaza con tu ID de extensión real si es diferente
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

// --- <PERSON><PERSON><PERSON> de Petición OPTIONS (Preflight) ---
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(204); // No Content
    exit;
}

// --- Incluir dependencias ---
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/db.php';
require_once __DIR__ . '/../../vendor/autoload.php'; // Asegúrate de tener la librería de Stripe instalada vía Composer

// --- Solo permitir método POST ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Método no permitido. Se requiere POST.']);
    exit;
}

// --- Inicializar Stripe ---
try {
    \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
} catch (\Exception $e) {
    error_log("Error inicializando Stripe: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Error interno del servidor (configuración Stripe).']);
    exit;
}

// --- Autenticación (Priorizando Sesión PHP, luego Token Bearer) ---
$authenticated_user_id = null; // ID interno del usuario de la BD
$googleUserId = null; // Google ID (solo si usamos token)

// 1. Verificar Sesión PHP
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (isset($_SESSION['user_id'])) {
    $authenticated_user_id = $_SESSION['user_id'];
    error_log("Auth successful via PHP Session. User ID: " . $authenticated_user_id);
} else {
    // 2. Si no hay sesión PHP, intentar verificar Token Bearer (para posible uso futuro o API externa)
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
    if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $accessToken = $matches[1];
        try {
            $userInfoUrl = 'https://www.googleapis.com/oauth2/v2/userinfo';
            $options = [
                'http' => [
                    'header' => "Authorization: Bearer " . $accessToken . "\r\nAccept: application/json\r\n",
                    'method' => 'GET',
                    'ignore_errors' => true
                ]
            ];
            $context = stream_context_create($options);
            $response = file_get_contents($userInfoUrl, false, $context);
            $status_line = $http_response_header[0] ?? '';
            preg_match('{HTTP/\d\.\d\s+(\d+)\s+}', $status_line, $match);
            $statusCode = isset($match[1]) ? (int)$match[1] : 0;

            if ($statusCode === 200) {
                $userInfo = json_decode($response, true);
                $googleUserId = $userInfo['id'] ?? null;
                if (!$googleUserId) {
                     throw new Exception('UserInfo OK pero no devolvió ID de Google.');
                }
                 error_log("Auth successful via Bearer Token. Google ID: " . $googleUserId);
                 // Necesitamos obtener el ID interno de la BD a partir del Google ID
                 $dbCheck = getDbConnection();
                 if ($dbCheck) {
                     $stmtCheck = $dbCheck->prepare("SELECT id FROM users WHERE google_id = :google_id");
                     $stmtCheck->execute([':google_id' => $googleUserId]);
                     $foundUser = $stmtCheck->fetch(PDO::FETCH_ASSOC);
                     if ($foundUser) {
                         $authenticated_user_id = $foundUser['id'];
                     } else {
                          error_log("Bearer Token valid, but Google ID {$googleUserId} not found in DB.");
                          // Considerar si se debe crear el usuario aquí o devolver error
                     }
                 }

            } else {
                error_log("Token Bearer inválido según UserInfo API (create_checkout): Status " . $statusCode);
                // No salimos con error 401 aquí, porque la ausencia de sesión/token válido se maneja abajo
            }
        } catch (Exception $e) {
            error_log("Error verificando Access Token vía UserInfo (create_checkout): " . $e->getMessage());
             // No salimos con error 401 aquí
        }
    }
}

// 3. Verificar si se obtuvo un ID de usuario (por sesión o token)
if ($authenticated_user_id === null) {
    http_response_code(401);
    echo json_encode(['error' => 'Autenticación requerida. Inicia sesión o proporciona un token válido.']);
    exit;
}
// --- Fin Autenticación ---

// --- Obtener Price ID del cuerpo de la petición ---
$input = json_decode(file_get_contents('php://input'), true);
$priceId = $input['priceId'] ?? null;

if (empty($priceId)) {
    http_response_code(400);
    echo json_encode(['error' => 'Falta el priceId en la solicitud.']);
    exit;
}

// Validar que el Price ID sea uno de los permitidos (opcional pero recomendado)
$allowedPriceIds = [
    STRIPE_PRICE_ID_PERSONAL,
    STRIPE_PRICE_ID_PLUS,
    STRIPE_PRICE_ID_AGENCY
    // Añade otros IDs si los tienes (ej. anuales)
];
if (!in_array($priceId, $allowedPriceIds)) {
    http_response_code(400);
    echo json_encode(['error' => 'Price ID no válido.']);
    exit;
}

// --- Conectar a la BD y obtener datos del usuario ---
try {
    $db = getDbConnection();
    if (!$db) {
        throw new Exception("No se pudo conectar a la base de datos.");
    }

    // Buscar datos adicionales del usuario usando el ID interno obtenido
    // $stmt = $db->prepare("SELECT id, stripe_customer_id FROM users WHERE google_id = :google_id"); // Ya no buscamos por google_id aquí
    // $stmt->execute([':google_id' => $googleUserId]);
    $stmt = $db->prepare("SELECT email, stripe_customer_id FROM users WHERE id = :user_id");
    $stmt->execute([':user_id' => $authenticated_user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        // Esto sería muy raro si la autenticación tuvo éxito
        http_response_code(404);
        echo json_encode(['error' => 'Usuario autenticado pero no encontrado en la base de datos (ID: ' . $authenticated_user_id . ').']);
        exit;
    }

    // $userId = $user['id']; // Ya lo tenemos en $authenticated_user_id
    $userEmail = $user['email']; // Obtener email directamente
    $stripeCustomerId = $user['stripe_customer_id']; // Puede ser null

    // --- Crear Sesión de Stripe Checkout ---
    $checkout_params = [
        'line_items' => [[
            'price' => $priceId,
            'quantity' => 1,
        ]],
        'mode' => 'subscription',
        // Define tus URLs de éxito y cancelación
        'success_url' => 'https://pietramundodigital.com/panel?checkout=success',
        'cancel_url' => 'https://pietramundodigital.com/gsc-inspector#planes-precios',
        'metadata' => [
            // Guardamos el ID INTERNO del usuario para identificarlo en el webhook
            'user_id' => $authenticated_user_id // Usar el ID obtenido de la sesión o token
        ],
         // Habilitar Stripe Tax si lo has configurado
         'automatic_tax' => ['enabled' => true],
    ];

    // Si ya tenemos un Stripe Customer ID, lo reutilizamos
    if ($stripeCustomerId) {
        $checkout_params['customer'] = $stripeCustomerId;
    } else {
        // Si no, podemos pre-rellenar el email en Checkout
         // $stmtEmail = $db->prepare("SELECT email FROM users WHERE id = :user_id"); // Ya tenemos el email
         // $stmtEmail->execute([':user_id' => $userId]);
         // $userEmail = $stmtEmail->fetchColumn();
         if ($userEmail) { // Usar el email obtenido antes
            $checkout_params['customer_email'] = $userEmail;
         }
    }


    $checkout_session = \Stripe\Checkout\Session::create($checkout_params);

    // --- Devolver URL de Checkout ---
    http_response_code(200);
    echo json_encode(['url' => $checkout_session->url]);

} catch (PDOException $e) {
    error_log("Database Error en create_checkout_session.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Error de base de datos.']);
    exit;
} catch (\Stripe\Exception\ApiErrorException $e) {
    error_log("Stripe API Error en create_checkout_session.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Error al crear la sesión de pago: ' . $e->getMessage()]);
    exit;
} catch (Exception $e) {
    error_log("General Error en create_checkout_session.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ocurrió un error inesperado.']);
    exit;
}

?> 