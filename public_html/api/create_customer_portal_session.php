<?php
session_start();

// 1. Verificar si el usuario está logueado
if (!isset($_SESSION['user_id'])) {
    // No está logueado, redirigir a login o mostrar error
    header('Location: /login?error=' . urlencode('Debes iniciar sesión para gestionar tu cuenta.'));
    exit;
}

// 2. Incluir configuración y dependencias
require_once __DIR__ . '/../../includes/config.php'; // Ajusta la ruta si es necesario
require_once __DIR__ . '/../../includes/db.php';     // Ajusta la ruta si es necesario
require_once __DIR__ . '/../../vendor/autoload.php'; // Ruta al autoload de Composer

// 3. Obtener el ID de cliente de Stripe del usuario desde la BD
$userId = $_SESSION['user_id'];
$stripeCustomerId = null;

$db = getDbConnection();
if ($db) {
    try {
        $stmt = $db->prepare("SELECT stripe_customer_id FROM users WHERE id = :id");
        $stmt->execute([':id' => $userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && !empty($result['stripe_customer_id'])) {
            $stripeCustomerId = $result['stripe_customer_id'];
        } else {
            // Error: Usuario logueado pero sin ID de cliente de Stripe
            error_log("Portal Error: Usuario ID {$userId} no tiene stripe_customer_id en la BD.");
            // Redirigir al panel con un mensaje de error o mostrar página de error
            header('Location: /panel?error=' . urlencode('No se encontró información de cliente para gestionar la suscripción.'));
            exit;
        }
    } catch (PDOException $e) {
        error_log("Error BD obteniendo stripe_customer_id para User ID {$userId}: " . $e->getMessage());
        header('Location: /panel?error=' . urlencode('Error al consultar la base de datos.'));
        exit;
    }
} else {
    error_log("Portal Error: No se pudo conectar a la BD.");
    header('Location: /panel?error=' . urlencode('Error de conexión interna.'));
    exit;
}

// 4. Inicializar Stripe
if (!defined('STRIPE_SECRET_KEY') || STRIPE_SECRET_KEY === 'tu_clave_secreta_stripe') {
    error_log("Portal Error: STRIPE_SECRET_KEY no está definida en config.php.");
    header('Location: /panel?error=' . urlencode('Error de configuración del servidor.'));
    exit;
}

\Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);

// 5. Crear la sesión del Portal de Cliente de Stripe

// --- Asegurar que $baseUrl está definida y tiene protocolo ---
if (!isset($baseUrl) || empty($baseUrl)) {
    // Intentar construirla si no está definida
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    $domainName = $_SERVER['HTTP_HOST'] ?? 'www.pietramundodigital.com'; // Fallback por si acaso
    $baseUrl = $protocol . $domainName;
    error_log("Advertencia: $baseUrl no estaba definida, se construyó como: {$baseUrl}");
} elseif (strpos($baseUrl, 'http') !== 0) {
    // Añadir https:// si falta el protocolo
    $baseUrl = 'https://' . ltrim($baseUrl, '/');
    error_log("Advertencia: $baseUrl no tenía protocolo, se ajustó a: {$baseUrl}");
}
// --- Fin asegurar $baseUrl ---

$returnUrl = rtrim($baseUrl, '/') . '/panel'; // Construir URL de retorno asegurando una sola barra

try {
    // Verificar que la URL de retorno sea válida antes de enviarla
    if (!filter_var($returnUrl, FILTER_VALIDATE_URL)) {
        throw new Exception("La URL de retorno generada ($returnUrl) no es válida.");
    }

    $portalSession = \Stripe\BillingPortal\Session::create([
        'customer' => $stripeCustomerId,
        'return_url' => $returnUrl,
    ]);

    // 6. Redirigir al usuario a la URL del Portal
    header("Location: " . $portalSession->url);
    exit;

} catch (\Stripe\Exception\ApiErrorException $e) {
    // Error al crear la sesión del portal
    $stripeErrorMessage = $e->getMessage(); // Obtener el mensaje de error específico de Stripe
    error_log("Error Stripe API creando Portal Session para Customer {$stripeCustomerId}: " . $stripeErrorMessage);
    // Añadir el mensaje de error de Stripe a la URL de redirección para depurar
    $errorQueryParam = 'error=' . urlencode('No se pudo iniciar la gestión de la cuenta. Inténtalo más tarde.');
    $stripeErrorQueryParam = 'stripe_error=' . urlencode($stripeErrorMessage);
    header("Location: /panel?{$errorQueryParam}&{$stripeErrorQueryParam}"); // Redirigir con ambos errores
    exit;
} catch (Exception $e) {
    // Otro tipo de error
    $genericErrorMessage = $e->getMessage();
    error_log("Error inesperado creando Portal Session para Customer {$stripeCustomerId}: " . $genericErrorMessage);
     header('Location: /panel?error=' . urlencode('Ocurrió un error inesperado.') . '&details=' . urlencode($genericErrorMessage));
    exit;
}

?> 