<?php
// public_html/api/check.php

// Permitir acceso desde la extensión de Chrome específica
header("Access-Control-Allow-Origin: chrome-extension://llglmegjjadpomhgnoofhpboooadegga");
// Permitir métodos necesarios (incluyendo OPTIONS para preflight)
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
// Permitir cabeceras necesarias (como Authorization para el token)
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Manejar solicitud preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200); // OK
    exit;
}

// Cargar configuración y helper de BD
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/db.php';
// Cargar dependencias de Composer (no necesario para esta lógica específica)
// require_once __DIR__ . '/../../vendor/autoload.php';

// --- Cabeceras CORS y Tipo de Contenido ---
$extensionId = 'llglmegjjadpomhgnoofhpboooadegga'; // ID de la extensión cargada descomprimida
header("Access-Control-Allow-Origin: chrome-extension://" . $extensionId);
header("Access-Control-Allow-Methods: POST, OPTIONS"); // POST para verificar acción
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

// --- Manejo de Petición OPTIONS (Preflight) ---
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(204); // No Content
    exit;
}

// --- Solo permitir método POST ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['error' => 'Método no permitido. Se requiere POST.']);
    exit;
}

// --- Autenticación (Validando Access Token de la cabecera llamando a UserInfo) ---
$googleUserId = null;
$accessToken = null;
$authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? null;

if ($authHeader && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
    $accessToken = $matches[1];
    try {
        // Llamar a Google UserInfo API para validar el token y obtener el ID de usuario
        $userInfoUrl = 'https://www.googleapis.com/oauth2/v2/userinfo';
        $options = [
            'http' => [
                'header' => "Authorization: Bearer " . $accessToken . "\r\n" .
                            "Accept: application/json\r\n",
                'method' => 'GET',
                'ignore_errors' => true // Para leer la respuesta incluso si es error
            ]
        ];
        $context = stream_context_create($options);
        $response = file_get_contents($userInfoUrl, false, $context);
        $status_line = $http_response_header[0];
        preg_match('{HTTP/\d\.\d\s+(\d+)\s+}', $status_line, $match);
        $statusCode = isset($match[1]) ? (int)$match[1] : 0;

        if ($statusCode === 200) {
            $userInfo = json_decode($response, true);
            $googleUserId = $userInfo['id'] ?? null; // Usar 'id' de userinfo
            if (!$googleUserId) {
                 error_log("UserInfo OK pero sin Google ID en la respuesta para token: " . substr($accessToken, 0, 10) . "...");
                 throw new Exception('UserInfo no devolvió ID de Google.'); // Tratar como error de autenticación
            }
        } else {
            error_log("Token inválido según UserInfo API (check.php): Status " . $statusCode . " - Token: " . substr($accessToken, 0, 10) . "...");
            http_response_code(401);
            echo json_encode(['error' => 'Token de autenticación inválido o expirado.']);
            exit;
        }
    } catch (Exception $e) {
        error_log("Error verificando Access Token vía UserInfo (check.php): " . $e->getMessage());
        http_response_code(401);
        echo json_encode(['error' => 'Error al verificar token de autenticación.']);
        exit;
    }
} else {
    http_response_code(401);
    echo json_encode(['error' => 'Autenticación requerida (Cabecera Authorization: Bearer <token>).']);
    exit;
}

if (!$googleUserId) {
    // Este caso ya debería haber sido manejado arriba, pero por si acaso.
    http_response_code(401);
    echo json_encode(['error' => 'Autenticación fallida (No se pudo obtener Google ID).']);
    exit;
}
// --- Fin Autenticación ---

// --- Conectar a la Base de Datos ---
$db = getDbConnection();
if (!$db) {
    http_response_code(503);
    echo json_encode(['error' => 'No se pudo conectar a la base de datos.']);
    exit;
}

// --- Verificar Permiso de Acción ---
try {
    // Obtener datos relevantes del usuario usando el $googleUserId obtenido
    $stmt = $db->prepare("SELECT id, plan, subscription_status, usage_count, current_period_end, last_usage_reset FROM users WHERE google_id = :google_id"); // Añadimos id, last_usage_reset
    $stmt->execute([':google_id' => $googleUserId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        // Esto no debería pasar si el usuario pasó por verify.php, pero por seguridad:
        http_response_code(404);
        echo json_encode(['error' => 'Usuario no encontrado para el token proporcionado.']);
        exit;
    }

    $allow = false;
    $reason = 'disallowed'; // Razón por defecto
    $newState = null; // Para devolver el estado actualizado si se incrementa

    // Función auxiliar para obtener el límite de uso basado en el plan
    function get_usage_limit($plan) {
        switch ($plan) {
            case 'free':
                return FREE_PLAN_LIMIT;
            case 'personal': // Asumiendo estos nombres de plan en la DB
                return PERSONAL_PLAN_LIMIT;
            case 'plus':
                return PLUS_PLAN_LIMIT;
            case 'agency':
                return AGENCY_PLAN_LIMIT;
            case 'monthly': // Mantener por si acaso se usan
            case 'yearly':
            case 'gift':
                // Mantener ilimitados (null) como estaban.
                return null; 
            default:
                return 0; // Límite 0 para planes desconocidos
        }
    }

    // --- Lógica Principal de Permisos ---
    switch ($user['plan']) {
        case 'personal': // Añadir los nuevos planes aquí
        case 'plus':
        case 'agency':
        case 'monthly': // Mantener estos si siguen siendo posibles
        case 'yearly':
        case 'gift':
            // Primero, verificar si la suscripción está activa y dentro del periodo
            if (!($user['subscription_status'] === 'active' || $user['subscription_status'] === 'trialing') ||
                ($user['current_period_end'] !== null && new DateTime() >= new DateTime($user['current_period_end']))) {
                
                if ($user['current_period_end'] !== null && new DateTime() >= new DateTime($user['current_period_end'])) {
                     $reason = 'subscription_expired';
                } else {
                     $reason = 'subscription_inactive';
                }
                $allow = false;
                break; // Salir del case si la suscripción no está activa/válida
            }
            
            // Si la suscripción está activa, verificar el límite de uso
            $limit = get_usage_limit($user['plan']);
            $current_usage = isset($user['usage_count']) ? (int)$user['usage_count'] : 0;

            // Si el límite es null, permitir (ilimitado)
            if ($limit === null) {
                 $allow = true;
            } elseif ($current_usage < $limit) {
                $allow = true;
            } else {
                $reason = 'limit_reached';
                $allow = false;
            }
            break;

        case 'free':
            // La lógica para 'free' ahora también usa get_usage_limit
            $limit = get_usage_limit('free'); 
            $current_usage = isset($user['usage_count']) ? (int)$user['usage_count'] : 0;
            if ($current_usage < $limit) {
                $allow = true;
            } else {
                $reason = 'limit_reached';
                $allow = false; // Asegurarse que $allow es false aquí
            }
            break;

        default:
            $reason = 'unknown_plan';
            $allow = false;
            break;
    }
    // --- Fin Lógica de Permisos ---

    // --- Preparar estado ACTUAL para devolver --- 
    $currentState = [
        'google_id' => $googleUserId,
        'email' => null, // No es necesario aquí
        'plan' => $user['plan'],
        'status' => $user['subscription_status'],
        'usage_count' => $user['usage_count'], // El contador actual, sin incrementar
        'usage_limit' => get_usage_limit($user['plan']),
        'current_period_end' => $user['current_period_end']
    ];
    // --- Fin Preparar Estado Actual ---

    // --- Devolver Respuesta ---    
    http_response_code(200); // OK
    // Incluir el estado actual en la respuesta
    $response_data = ['allow' => $allow, 'reason' => $reason, 'currentState' => $currentState]; 
    /* 
    if ($newState !== null) { // Ya no usamos newState
        $response_data['newState'] = $newState;
    }
    */
    echo json_encode($response_data);

} catch (PDOException $e) {
    error_log("Database Error en check.php: " . $e->getMessage());
    http_response_code(500); // Internal Server Error
    echo json_encode(['error' => 'Error de base de datos al verificar permisos.']);
    exit;
} catch (Exception $e) { // Capturar otras excepciones generales
    error_log("General Error en check.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ocurrió un error inesperado al verificar permisos.']);
    exit;
}

?>
