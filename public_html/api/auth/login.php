<?php
// /api/auth/login.php

ini_set('display_errors', 1); 
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../../../includes/config.php'; 
require_once __DIR__ . '/../../../includes/db.php'; 

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// --- Solo permitir método POST ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: /login?error=' . urlencode('Método no permitido.'));
    exit;
}

// --- Obtener Datos del Formulario --- 
$email = filter_var(trim($_POST['email'] ?? ''), FILTER_VALIDATE_EMAIL);
$password = $_POST['password'] ?? '';

if (empty($email) || empty($password)) {
     header('Location: /login?error=' . urlencode('Faltan email o contraseña.'));
     exit;
}

// --- Conectar a la BD --- 
$db = getDbConnection();
if (!$db) {
    header('Location: /login?error=' . urlencode('Error interno del servidor (BD).'));
    exit;
}

try {
    // --- Buscar Usuario por Email --- 
    // Seleccionamos ID, email, hash y estado de verificación
    $stmt = $db->prepare("SELECT id, email, password_hash, email_verified_at FROM users WHERE email = :email AND google_id IS NULL"); // Solo buscar usuarios registrados manualmente
    $stmt->execute([':email' => $email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && password_verify($password, $user['password_hash'])) {
        // Contraseña correcta, ¿pero está verificado?
        if ($user['email_verified_at'] === null) {
            // Email no verificado
            // Opcional: Podríamos implementar reenvío de email aquí
            header('Location: /login?error=' . urlencode('Debes verificar tu dirección de email antes de iniciar sesión. Revisa tu bandeja de entrada (y spam).'));
            exit;
        }
        
        // ¡Email verificado y contraseña correcta!
        
        // --- Actualizar last_login --- (Opcional pero recomendado)
        $updateLoginStmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = :id");
        $updateLoginStmt->execute([':id' => $user['id']]);
        
        // --- Iniciar Sesión --- 
        session_regenerate_id(true); 
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        // No establecer google_id para login manual

        // --- Redirigir al Panel --- 
        header('Location: /panel');
        exit;

    } else {
        // Usuario no encontrado o contraseña incorrecta
        header('Location: /login?error=' . urlencode('Email o contraseña incorrectos.'));
        exit;
    }

} catch (PDOException $e) {
    error_log("Error de BD en login: " . $e->getMessage());
    header('Location: /login?error=' . urlencode('Error interno del servidor (BD).'));
    exit;
} catch (Exception $e) {
    error_log("Error general en login: " . $e->getMessage());
     header('Location: /login?error=' . urlencode('Ocurrió un error inesperado.'));
    exit;
}

?> 