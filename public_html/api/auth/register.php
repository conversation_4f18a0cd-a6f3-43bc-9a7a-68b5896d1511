<?php
// /api/auth/register.php

ini_set('display_errors', 1); // Activar errores para depuración
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Incluir PHPMailer manualmente
use P<PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use P<PERSON>Mailer\PHPMailer\Exception;

// ¡AJUSTA LA RUTA SI ES NECESARIO!
// Desde /api/auth/ hasta /public_html/libs/PHPMailer/src/
require_once __DIR__ . '/../../../libs/PHPMailer/src/Exception.php';
require_once __DIR__ . '/../../../libs/PHPMailer/src/PHPMailer.php';
require_once __DIR__ . '/../../../libs/PHPMailer/src/SMTP.php';

require_once __DIR__ . '/../../../includes/config.php'; 
require_once __DIR__ . '/../../../includes/db.php'; 

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// --- Solo permitir método POST ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    // Redirigir al registro con error si no es POST
    header('Location: /registro?error=' . urlencode('Método no permitido.'));
    exit;
}

// --- Obtener y Validar Datos del Formulario --- 
$name = trim($_POST['name'] ?? '');
$email = filter_var(trim($_POST['email'] ?? ''), FILTER_VALIDATE_EMAIL);
$password = $_POST['password'] ?? '';
$confirm_password = $_POST['confirm_password'] ?? '';
$terms_accepted = isset($_POST['terms']);

// Validaciones básicas
if (empty($email) || empty($password) || empty($confirm_password)) {
     header('Location: /registro?error=' . urlencode('Faltan campos obligatorios (Email, Contraseña).'));
     exit;
}

if (!$terms_accepted) {
    header('Location: /registro?error=' . urlencode('Debes aceptar los términos y condiciones.'));
    exit;
}

// Validación de fortaleza de la contraseña
$password_pattern = '/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\W).{8,}$/';
if (!preg_match($password_pattern, $password)) {
    header('Location: /registro?error=' . urlencode('La contraseña no cumple los requisitos: mínimo 8 caracteres, mayúscula, minúscula, número y símbolo.'));
    exit;
}

if ($password !== $confirm_password) {
    header('Location: /registro?error=' . urlencode('Las contraseñas no coinciden.'));
    exit;
}

// --- Conectar a la BD --- 
$db = getDbConnection(); // Usamos la función que devuelve PDO
if (!$db) {
    header('Location: /registro?error=' . urlencode('Error interno del servidor (BD). No se pudo conectar.'));
    exit;
}

try {
    // --- Verificar si el email ya existe (y si está verificado o no) --- 
    $stmtCheck = $db->prepare("SELECT id, email_verified_at FROM users WHERE email = :email");
    $stmtCheck->execute([':email' => $email]);
    $existingUser = $stmtCheck->fetch(PDO::FETCH_ASSOC);
    
    if ($existingUser && $existingUser['email_verified_at'] !== null) {
        // Email ya existe Y está verificado
        header('Location: /registro?error=' . urlencode('El email introducido ya está registrado y verificado. Intenta iniciar sesión.'));
        exit;
    } 
    // Si existe pero no está verificado, podríamos reenviar el email, pero por ahora lo simple es permitir el re-registro que sobreescribirá el token.
    $stmtCheck->closeCursor();

    // --- Generar Token y Expiración --- 
    $verification_token = bin2hex(random_bytes(32)); // Token seguro de 64 caracteres
    $token_expires_at = (new DateTime('+1 hour'))->format('Y-m-d H:i:s'); // Token válido por 1 hora

    // --- Crear Hash Seguro de la Contraseña --- 
    $password_hash = password_hash($password, PASSWORD_DEFAULT); // Recomendado por PHP

    // --- Insertar o Actualizar Usuario (si existía pero no verificado) --- 
    $plan = 'free';
    $subscription_status = 'active';
    $usage_count = 0;
    
    // Usamos INSERT ... ON DUPLICATE KEY UPDATE para manejar si el email ya existía pero no estaba verificado
    // Esto requiere que la columna `email` tenga un índice UNIQUE en la BD.
    // ASEGÚRATE DE TENER UN ÍNDICE UNIQUE EN LA COLUMNA `email`
    $sqlUpsert = "INSERT INTO users (email, password_hash, name, plan, subscription_status, usage_count, email_verify_token, email_verify_expires_at, created_at, updated_at, last_login, email_verified_at) 
                  VALUES (:email, :password_hash, :name, :plan, :subscription_status, :usage_count, :token, :expires, NOW(), NOW(), NULL, NULL)
                  ON DUPLICATE KEY UPDATE 
                  password_hash = VALUES(password_hash), name = VALUES(name), email_verify_token = VALUES(email_verify_token), email_verify_expires_at = VALUES(email_verify_expires_at), updated_at = NOW(), email_verified_at = NULL"; // Resetea verificación si se re-registra
                  
    $stmtUpsert = $db->prepare($sqlUpsert);
    
    $successUpsert = $stmtUpsert->execute([
        ':email' => $email,
        ':password_hash' => $password_hash,
        ':name' => $name ?: null, 
        ':plan' => $plan,
        ':subscription_status' => $subscription_status,
        ':usage_count' => $usage_count,
        ':token' => $verification_token,
        ':expires' => $token_expires_at
    ]);

    if ($successUpsert) {
        $user_id = $db->lastInsertId(); // Obtiene ID si fue INSERT, 0 si fue UPDATE
        if (!$user_id && $existingUser) {
             $user_id = $existingUser['id']; // Usar el ID existente si fue un UPDATE
        }
         error_log("[Registro OK] Usuario insertado/actualizado. ID: " . $user_id . " Email: " . $email . " Token: " . $verification_token);

        // --- Enviar Email de Verificación --- 
        $mail = new PHPMailer(true);
        $verification_link = "https://www.pietramundodigital.com/verificar-email?token=" . $verification_token;

        try {
            // Configuración del Servidor SMTP (¡USAR CREDENCIALES REALES!) 
            $mail->isSMTP();                                           
            $mail->Host       = 'smtp.hostinger.com'; // Servidor SMTP de Hostinger (o el que uses)
            $mail->SMTPAuth   = true;                                   
            $mail->Username   = '<EMAIL>'; // Tu dirección de email
            $mail->Password   = 'TU_CONTRASEÑA_EMAIL'; // Contraseña de tu email o contraseña de aplicación
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // Usar SSL/TLS (recomendado)
            $mail->Port       = 465; // Puerto SMTP para SSL (o 587 para TLS si SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS)

            // Remitente y Destinatario
            $mail->setFrom('<EMAIL>', 'Pietra Mundo Digital'); // Email y nombre del remitente
            $mail->addAddress($email, $name ?: 'Usuario'); // Email y nombre (opcional) del destinatario
            
            // Contenido del Email
            $mail->isHTML(true);                                  
            $mail->Subject = 'Verifica tu direccion de email en Pietra Mundo Digital';
            $mail->Body    = "Hola " . ($name ? htmlspecialchars($name) : 'Usuario') . ",<br><br>Gracias por registrarte. Por favor, haz clic en el siguiente enlace para verificar tu dirección de email:<br><br><a href='" . $verification_link . "'>" . $verification_link . "</a><br><br>Si no te registraste, puedes ignorar este mensaje.<br><br>Saludos,<br>El equipo de Pietra Mundo Digital";
            $mail->AltBody = "Hola " . ($name ? htmlspecialchars($name) : 'Usuario') . ",\n\nGracias por registrarte. Por favor, copia y pega el siguiente enlace en tu navegador para verificar tu dirección de email:\n\n" . $verification_link . "\n\nSi no te registraste, puedes ignorar este mensaje.\n\nSaludos,\nEl equipo de Pietra Mundo Digital";

            $mail->send();
            error_log('Email de verificación enviado a: ' . $email);
            
            // --- Redirigir con Mensaje de Éxito --- 
            // Redirigir a login o una página específica que diga "Revisa tu email"
            header('Location: /login?success=' . urlencode('¡Registro casi completo! Revisa tu email (' . $email . ') para verificar tu cuenta.'));
            exit;

        } catch (Exception $e) {
            error_log("Error al enviar email de verificación a {$email}: {$mail->ErrorInfo}");
            // Informar al usuario, pero el registro en BD ya se hizo
            header('Location: /registro?error=' . urlencode('Se creó tu cuenta, pero hubo un error al enviar el email de verificación. Contacta con soporte.'));
            exit;
        }

    } else {
         // Error al insertar/actualizar
         $errorInfo = $stmtUpsert->errorInfo();
         error_log("[Registro ERROR BD] Falló el UPSERT: " . ($errorInfo[2] ?? 'Error desconocido') . " Email: " . $email);
         header('Location: /registro?error=' . urlencode('Error interno al procesar el registro en la base de datos.'));
         exit;
    }

} catch (PDOException $e) {
    error_log("Error de BD en registro: " . $e->getMessage());
    header('Location: /registro?error=' . urlencode('Error interno del servidor (BD).'));
    exit;
} catch (Exception $e) {
    error_log("Error general en registro: " . $e->getMessage());
     header('Location: /registro?error=' . urlencode('Ocurrió un error inesperado.'));
    exit;
}

?> 