<?php
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../../../vendor/autoload.php';
require_once __DIR__ . '/../../../includes/config.php'; 
require_once __DIR__ . '/../../../includes/db.php';

session_start(); // Necesario para manejar sesiones si se usan

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *'); // Ajusta según sea necesario para producción
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    // Pre-flight request. Respond successfully:
    http_response_code(204);
    exit;
}

// Obtener el cuerpo de la solicitud
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, TRUE); //Convertir JSON a array asociativo

$credential = isset($input['credential']) ? $input['credential'] : null;

if (!$credential) {
    http_response_code(400);
    echo json_encode(['error' => 'No se proporcionó credencial.']);
    exit;
}

try { // <--- INICIO DEL BLOQUE TRY
    $client = new Google_Client(['client_id' => GOOGLE_CLIENT_ID_WEB]); 
    $payload = $client->verifyIdToken($credential);

    if ($payload) {
        $google_id = $payload['sub']; 
        $email = $payload['email'];
        $name = isset($payload['name']) ? $payload['name'] : ''; // Opcional
        // Puedes obtener más datos del payload si los necesitas

        // Conectar a la base de datos - ¡Llamar con el nombre correcto!
        $conn = getDbConnection();
        if (!$conn) {
            // El error de conexión ya se maneja en get_db_connection, pero por si acaso
             throw new Exception("Error al conectar a la base de datos.");
        }

        // Buscar usuario por google_id
        // ¡IMPORTANTE! El código subsiguiente usa $conn->prepare, etc., que son métodos de mysqli,
        // pero getDbConnection() devuelve un objeto PDO. Hay que reescribir las consultas a PDO.
        
        // Convertir la conexión mysqli a PDO si es posible (NO RECOMENDADO, mejor usar PDO directamente)
        // O reescribir la lógica de BD para usar PDO

        // --- REESCRITURA CON PDO --- 
        $sqlSelect = "SELECT id, email, plan, subscription_status, current_period_end, usage_count FROM users WHERE google_id = :google_id";
        $stmtSelect = $conn->prepare($sqlSelect);
        if (!$stmtSelect) {
            throw new Exception("Error preparando la consulta (SELECT): " . $conn->errorInfo()[2]); // Error PDO
        }
        $stmtSelect->execute([':google_id' => $google_id]);
        $user = $stmtSelect->fetch(PDO::FETCH_ASSOC); // Fetch con PDO

        if ($user) {
            // Usuario encontrado - Devolver sus datos
            $_SESSION['user_id'] = $user['id']; 
            $_SESSION['google_id'] = $google_id;
            $_SESSION['user_email'] = $user['email'];
            http_response_code(200);
            echo json_encode([
                'success' => true,
                'user_exists' => true,
                'user_data' => [
                    'email' => $user['email'],
                    'plan' => $user['plan'],
                    'subscription_status' => $user['subscription_status'],
                    'current_period_end' => $user['current_period_end'],
                    'usage_count' => $user['usage_count'],
                    'google_id' => $google_id 
                ]
            ]);
             $stmtSelect->closeCursor(); // Cerrar cursor PDO
        } else {
            // Usuario no encontrado - Crear nuevo usuario con plan 'free'
            $plan = 'free';
            $subscription_status = 'active';
            $current_period_end = null;
            $usage_count = 0;
            
            $sqlInsert = "INSERT INTO users (google_id, email, name, plan, subscription_status, usage_count, created_at, updated_at) VALUES (:google_id, :email, :name, :plan, :subscription_status, :usage_count, NOW(), NOW())";
            $stmtInsert = $conn->prepare($sqlInsert);
             if (!$stmtInsert) {
                throw new Exception("Error preparando la consulta (INSERT): " . $conn->errorInfo()[2]); // Error PDO
            }
            
            $successInsert = $stmtInsert->execute([
                ':google_id' => $google_id,
                ':email' => $email,
                ':name' => $name,
                ':plan' => $plan,
                ':subscription_status' => $subscription_status,
                ':usage_count' => $usage_count
            ]);

            if ($successInsert) {
                $new_user_id = $conn->lastInsertId(); // Obtener último ID con PDO
                $_SESSION['user_id'] = $new_user_id;
                $_SESSION['google_id'] = $google_id;
                $_SESSION['user_email'] = $email;
                 http_response_code(201); // 201 Created
                 echo json_encode([
                    'success' => true,
                    'user_exists' => false,
                    'user_data' => [
                        'id' => $new_user_id, 
                        'email' => $email,
                        'plan' => $plan,
                        'subscription_status' => $subscription_status,
                        'current_period_end' => $current_period_end,
                        'usage_count' => $usage_count,
                        'google_id' => $google_id
                    ]
                ]);
            } else {
                 // Obtener info del error PDO
                $errorInfo = $stmtInsert->errorInfo();
                throw new Exception("Error al crear el usuario: " . ($errorInfo[2] ?? 'Error desconocido'));
            }
             $stmtInsert->closeCursor(); // Cerrar cursor PDO
        }

        // Ya no se usa $stmt->close() ni $conn->close() porque PDO maneja la conexión de forma diferente
        // y los cursores se cierran explícitamente arriba.
        // La conexión PDO se cierra automáticamente al final del script o cuando $conn se destruye.

    } else {
        // Token inválido o expirado
        http_response_code(401);
        echo json_encode(['error' => 'Credencial de Google inválida o expirada.']);
    }
} catch (Throwable $e) { // <--- BLOQUE CATCH PARA CAPTURAR CUALQUIER ERROR/EXCEPCIÓN
    http_response_code(500); 
    // Devolver el mensaje de error real
    echo json_encode([
        'error' => 'Error interno del servidor.', 
        'details' => $e->getMessage(), // Mensaje específico del error
        'file' => basename($e->getFile()), // Nombre del archivo donde ocurrió
        'line' => $e->getLine() // Línea donde ocurrió
        ]); 
}

exit;
?> 