<?php
// public_html/api/stripe_webhook.php

// --- Habilitar errores para depuración inicial (¡Desactiva display_errors en producción!) ---
ini_set('display_errors', 1); // Ayuda a ver errores durante el desarrollo del webhook
ini_set('log_errors', 1); // Siempre loguear errores
// Asegúrate que PHP pueda escribir en el archivo de log definido en php.ini
error_reporting(E_ALL);
// --- Fin Habilitar errores ---

// --- Incluir dependencias ---
require_once __DIR__ . '/../../includes/config.php';
require_once __DIR__ . '/../../includes/db.php';
require_once __DIR__ . '/../../vendor/autoload.php'; // Asegúrate de tener la librería de Stripe

// --- Constantes y Funciones Auxiliares ---
// Mapeo de Price ID de Stripe a nombre interno del plan
function mapStripePriceIdToPlanName($priceId) {
    switch ($priceId) {
        case STRIPE_PRICE_ID_PERSONAL: return 'personal';
        case STRIPE_PRICE_ID_PLUS: return 'plus';
        case STRIPE_PRICE_ID_AGENCY: return 'agency';
        // Añade otros si los tienes (ej. anuales)
        default:
            error_log("Webhook: Price ID desconocido recibido: " . $priceId);
            return 'unknown'; // O manejar el error de forma más específica
    }
}

// Obtener límite de uso basado en el plan (copiada/adaptada de check.php)
function get_usage_limit_webhook($plan) {
    switch ($plan) {
        case 'free': return FREE_PLAN_LIMIT;
        case 'personal': return PERSONAL_PLAN_LIMIT;
        case 'plus': return PLUS_PLAN_LIMIT;
        case 'agency': return AGENCY_PLAN_LIMIT;
        // Planes ilimitados
        case 'monthly': // Si aún existen
        case 'yearly':
        case 'gift':
            return null;
        default: return 0; // Límite 0 para planes desconocidos
    }
}


// --- Inicializar Stripe ---
try {
    \Stripe\Stripe::setApiKey(STRIPE_SECRET_KEY);
} catch (\Exception $e) {
    error_log("Webhook Error: Stripe SDK init failed: " . $e->getMessage());
    http_response_code(500); // Error interno
    exit(); // Salir temprano si Stripe no se puede inicializar
}


// --- Verificación de Firma del Webhook ---
$payload = @file_get_contents('php://input');
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? null;
$endpoint_secret = STRIPE_WEBHOOK_SECRET; // Tu secreto de webhook desde config.php
$event = null;

if (!$sig_header) {
     http_response_code(400);
     error_log("Webhook Error: No signature header received.");
     exit();
}

try {
    $event = \Stripe\Webhook::constructEvent(
        $payload, $sig_header, $endpoint_secret
    );
} catch(\UnexpectedValueException $e) {
    // Payload inválido
    http_response_code(400);
    error_log("Webhook Error: Invalid payload. " . $e->getMessage());
    exit();
} catch(\Stripe\Exception\SignatureVerificationException $e) {
    // Firma inválida
    http_response_code(400);
    error_log("Webhook Error: Invalid signature. " . $e->getMessage());
    exit();
} catch(Exception $e) {
    // Otro error durante la construcción del evento
    http_response_code(400);
    error_log("Webhook Error: Event construction failed. " . $e->getMessage());
    exit();
}

// --- Conexión a la Base de Datos ---
$db = getDbConnection();
if (!$db) {
     error_log("Webhook Error: DB Connection failed.");
     // Devolver 500 para que Stripe reintente más tarde si es un problema temporal de DB
     http_response_code(500);
     exit();
}

// --- Manejar el Evento Específico ---
$eventType = $event->type;
error_log("Webhook Received: Event ID: {$event->id}, Type: {$eventType}"); // Loguear evento recibido

try {
    $db->beginTransaction(); // Iniciar transacción

    switch ($eventType) {
        case 'checkout.session.completed':
            $session = $event->data->object; // \Stripe\Checkout\Session
            $userId = $session->metadata->user_id ?? null;
            $stripeCustomerId = $session->customer;
            $stripeSubscriptionId = $session->subscription;

            if ($userId && $stripeCustomerId && $stripeSubscriptionId) {
                 // Obtener detalles completos de la suscripción recién creada
                 $subscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);
                 $planId = $subscription->items->data[0]->price->id; // Price ID
                 $planName = mapStripePriceIdToPlanName($planId);
                 $status = $subscription->status; // 'active', 'trialing', etc.
                 $currentPeriodEnd = date('Y-m-d H:i:s', $subscription->current_period_end);
                 $newLimit = get_usage_limit_webhook($planName);

                 // Actualizar el usuario en la BD con los datos de Stripe
                 $stmt = $db->prepare("UPDATE users SET
                     plan = :plan,
                     subscription_status = :status,
                     current_period_end = :period_end,
                     stripe_customer_id = :customer_id,
                     stripe_subscription_id = :subscription_id,
                     usage_limit = :limit,
                     usage_count = 0 -- Resetear contador al iniciar suscripción
                 WHERE id = :user_id");

                 $stmt->execute([
                     ':plan' => $planName,
                     ':status' => $status,
                     ':period_end' => $currentPeriodEnd,
                     ':customer_id' => $stripeCustomerId,
                     ':subscription_id' => $stripeSubscriptionId,
                     ':limit' => $newLimit,
                     ':user_id' => $userId
                 ]);
                 error_log("Webhook Handler: checkout.session.completed processed for User ID: {$userId}, Sub ID: {$stripeSubscriptionId}, Plan: {$planName}, Status: {$status}");

            } else {
                 error_log("Webhook Handler Error checkout.session.completed: Missing data. UserID: " . ($userId ?? 'null') . " CustID: " . ($stripeCustomerId ?? 'null') . " SubID: " . ($stripeSubscriptionId ?? 'null') . " Event ID: {$event->id}");
                 // Considera NO confirmar la transacción si faltan datos críticos
                 $db->rollBack();
                 http_response_code(400); // Bad request - datos faltantes
                 exit();
            }
            break;

        case 'customer.subscription.updated':
            $subscription = $event->data->object; // \Stripe\Subscription
            $stripeSubscriptionId = $subscription->id;
            $status = $subscription->status; // 'active', 'past_due', 'canceled', 'unpaid', etc.
            $currentPeriodEnd = date('Y-m-d H:i:s', $subscription->current_period_end);
            $planId = $subscription->items->data[0]->price->id;
            $planName = mapStripePriceIdToPlanName($planId);
            $newLimit = get_usage_limit_webhook($planName);

            // Si la suscripción se cancela pero aún tiene tiempo restante ('cancel_at_period_end' = true)
            // Status puede ser 'active', pero 'cancel_at_period_end' estará timestamp.
            // Podrías querer manejar este caso específicamente si necesitas un estado intermedio.
            // Por ahora, actualizamos basado en el 'status' principal.

            $stmt = $db->prepare("UPDATE users SET
                 plan = :plan,
                 subscription_status = :status,
                 current_period_end = :period_end,
                 usage_limit = :limit
                 -- No reseteamos contador aquí, solo en inicio o renovación pagada
              WHERE stripe_subscription_id = :subscription_id");
            $stmt->execute([
                 ':plan' => $planName,
                 ':status' => $status,
                 ':period_end' => $currentPeriodEnd,
                 ':limit' => $newLimit,
                 ':subscription_id' => $stripeSubscriptionId
            ]);
            error_log("Webhook Handler: customer.subscription.updated processed for Sub ID: {$stripeSubscriptionId}, New Status: {$status}, Plan: {$planName}");
            break;

        case 'customer.subscription.deleted':
            // Ocurre al final del periodo si fue cancelada, o inmediatamente si se borra.
            $subscription = $event->data->object; // \Stripe\Subscription
            $stripeSubscriptionId = $subscription->id;
            $stmt = $db->prepare("UPDATE users SET
                 subscription_status = 'canceled', -- O 'expired' si prefieres
                 current_period_end = NULL,
                 plan = 'free', -- Volver al plan free
                 usage_limit = :limit,
                 usage_count = 0, -- Resetear contador
                 stripe_subscription_id = NULL, -- Opcional: limpiar ID de Stripe
                 stripe_customer_id = stripe_customer_id -- Mantener customer ID
              WHERE stripe_subscription_id = :subscription_id");

            $freeLimit = get_usage_limit_webhook('free');
            $stmt->execute([
                ':limit' => $freeLimit,
                ':subscription_id' => $stripeSubscriptionId
                ]);
            error_log("Webhook Handler: customer.subscription.deleted processed for Sub ID: {$stripeSubscriptionId}. User reverted to free plan.");
            break;

        case 'invoice.paid':
            $invoice = $event->data->object; // \Stripe\Invoice
            // Solo nos interesa si es por una renovación ('subscription_cycle') y fue pagada
            if ($invoice->paid && $invoice->billing_reason === 'subscription_cycle') {
                $stripeSubscriptionId = $invoice->subscription;
                if ($stripeSubscriptionId) {
                    // Obtener la suscripción para la nueva fecha de fin
                    $subscription = \Stripe\Subscription::retrieve($stripeSubscriptionId);
                    $currentPeriodEnd = date('Y-m-d H:i:s', $subscription->current_period_end);
                    $status = $subscription->status; // Debería ser 'active'

                    $stmt = $db->prepare("UPDATE users SET
                       subscription_status = :status,
                       current_period_end = :period_end,
                       usage_count = 0 -- Resetear contador en renovación exitosa
                    WHERE stripe_subscription_id = :subscription_id");
                    $stmt->execute([
                        ':status' => $status, // Asegurar que esté activa
                        ':period_end' => $currentPeriodEnd,
                        ':subscription_id' => $stripeSubscriptionId
                        ]);
                    error_log("Webhook Handler: invoice.paid (renewal) processed for Sub ID: {$stripeSubscriptionId}. Period ends: {$currentPeriodEnd}");
                }
            } else {
                 error_log("Webhook Handler: invoice.paid ignored. Paid: " . ($invoice->paid ? 'Yes' : 'No') . ", Reason: {$invoice->billing_reason}. Event ID: {$event->id}");
            }
            break;

        case 'invoice.payment_failed':
            $invoice = $event->data->object; // \Stripe\Invoice
            $stripeSubscriptionId = $invoice->subscription;
            // Marcar como 'past_due' si falla el pago de una suscripción
            if ($stripeSubscriptionId && $invoice->billing_reason === 'subscription_cycle') {
                 $stmt = $db->prepare("UPDATE users SET subscription_status = 'past_due'
                                      WHERE stripe_subscription_id = :subscription_id");
                 $stmt->execute([':subscription_id' => $stripeSubscriptionId]);
                 error_log("Webhook Handler: invoice.payment_failed processed for Sub ID: {$stripeSubscriptionId}. Status set to past_due.");
                 // Aquí podrías añadir lógica para enviar un email al usuario
            } else {
                 error_log("Webhook Handler: invoice.payment_failed ignored. Reason: {$invoice->billing_reason}. Event ID: {$event->id}");
            }
            break;

        // Puedes añadir más casos aquí si necesitas manejar otros eventos

        default:
            error_log('Webhook Handler: Received unhandled event type: ' . $eventType . " Event ID: {$event->id}");
    }

    $db->commit(); // Confirmar transacción si todo fue bien

} catch (PDOException $e) {
    $db->rollBack(); // Revertir cambios en caso de error de BD
    error_log("Webhook Handler DB Error: " . $e->getMessage() . " for Event Type: {$eventType}, Event ID: {$event->id}");
    http_response_code(500); // Error interno (DB)
    exit();
} catch (\Stripe\Exception\ApiErrorException $e) {
    // Error comunicando con Stripe API (ej. al hacer retrieve)
    $db->rollBack();
    error_log("Webhook Handler Stripe API Error: " . $e->getMessage() . " for Event Type: {$eventType}, Event ID: {$event->id}");
    http_response_code(500); // Error interno (Stripe API call failed)
    exit();
} catch (Exception $e) {
    // Otros errores inesperados
    $db->rollBack();
    error_log("Webhook Handler General Error: " . $e->getMessage() . " for Event Type: {$eventType}, Event ID: {$event->id}");
    http_response_code(500); // Error interno general
    exit();
}


// --- Responder a Stripe ---
// Si llegamos aquí, el evento fue procesado (o ignorado intencionalmente) sin errores fatales.
http_response_code(200); // OK - Informar a Stripe que recibimos y procesamos el evento
echo json_encode(['status' => 'success', 'event_received' => $eventType]);

?> 