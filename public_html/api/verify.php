<?php
// public_html/api/verify.php

// --- <PERSON><PERSON><PERSON><PERSON>AR ERRORES PARA DEPURACIÓN ---
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
// --- <PERSON>IN HABILITAR ERRORES ---

// Lista de orígenes permitidos (IDs de extensión)
$allowed_origins = [
    'chrome-extension://llglmegjjadpomhgnoofhpboooadegga', // Publicada
    'chrome-extension://olgpmdpgmagkdgjmhfceefdclgbgbhnm'  // Desarrollo
];

// Verificar el origen de la solicitud
if (isset($_SERVER['HTTP_ORIGIN']) && in_array($_SERVER['HTTP_ORIGIN'], $allowed_origins)) {
    header("Access-Control-Allow-Origin: " . $_SERVER['HTTP_ORIGIN']);
} else {
    // Opcional: podrías registrar o denegar el acceso si el origen no está permitido.
    // Por ahora, si no es uno de los permitidos, no se establecerá la cabecera Access-Control-Allow-Origin,
    // lo que podría causar que el navegador bloquee la solicitud si el origen es diferente.
    // Si quieres ser más estricto y solo permitir estos, podrías añadir:
    // http_response_code(403); echo json_encode(['error' => 'Origen no permitido.']); exit;
}

// Permitir métodos necesarios (incluyendo OPTIONS para preflight)
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
// Permitir cabeceras necesarias (como Authorization para el token)
header("Access-Control-Allow-Headers: Content-Type, Authorization");
// ASEGURAR Content-Type JSON
header('Content-Type: application/json');

// Manejar solicitud preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200); // OK
    exit;
}

// Cargar configuración y DB ANTES de cualquier lógica
try {
    require_once __DIR__ . '/../../includes/config.php';
    require_once __DIR__ . '/../../includes/db.php';
} catch (Throwable $e) {
    // Error crítico si no se pueden cargar config/db
    error_log("Error cargando config.php o db.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Error interno del servidor (configuración).']);
    exit;
}

// --- Solo permitir método POST ---
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['error' => 'Método no permitido. Se requiere POST.']);
    exit;
}

// --- Obtener y Validar Access Token ---
$input = json_decode(file_get_contents('php://input'), true);
$accessToken = $input['accessToken'] ?? null;

if (empty($accessToken)) {
    http_response_code(400); // Bad Request
    echo json_encode(['error' => 'Falta el access token de Google.']);
    exit;
}

// --- Verificar el Access Token llamando a Google UserInfo API ---
$userInfo = null;
$googleUserId = null;
$email = null;
$emailVerified = false;

try {
    $userInfoUrl = 'https://www.googleapis.com/oauth2/v2/userinfo';
    $options = [
        'http' => [
            'header' => "Authorization: Bearer " . $accessToken . "\r\n" .
                        "Accept: application/json\r\n",
            'method' => 'GET',
            'ignore_errors' => true 
        ]
    ];
    $context = stream_context_create($options);
    $response = file_get_contents($userInfoUrl, false, $context);
    $status_line = $http_response_header[0]; 

    preg_match('{HTTP/\d\.\d\s+(\d+)\s+}', $status_line, $match);
    $statusCode = isset($match[1]) ? (int)$match[1] : 0;

    if ($statusCode !== 200) {
        error_log("Google UserInfo API Error: Status " . $statusCode . " - Response: " . $response);
        http_response_code(401); 
        echo json_encode(['error' => 'Token de Google inválido o error al obtener userinfo.', 'details' => 'Status: ' . $statusCode]);
        exit;
    }

    $userInfo = json_decode($response, true);
    $googleUserId = $userInfo['id'] ?? null; 
    $email = $userInfo['email'] ?? null;
    $emailVerified = $userInfo['verified_email'] ?? false;

} catch (Exception $e) {
    error_log("Error llamando a Google UserInfo API: " . $e->getMessage());
    http_response_code(500); 
    echo json_encode(['error' => 'Error interno al contactar con Google para verificar el token.']);
    exit;
}

if (!$googleUserId || !$email) {
    http_response_code(400); 
    echo json_encode(['error' => 'La respuesta de Google UserInfo no contiene la información necesaria (ID o email).', 'userInfo_response' => $userInfo]);
    exit;
}

if (!$emailVerified) {
    http_response_code(403); 
    echo json_encode(['error' => 'La cuenta de Google asociada no tiene un email verificado.']);
    exit;
}

$db = getDbConnection();
if (!$db) {
    http_response_code(503); 
    echo json_encode(['error' => 'No se pudo conectar a la base de datos.']);
    exit;
}

try {
    $stmt = $db->prepare("SELECT id, google_id, email, plan, subscription_status, current_period_end, usage_count, usage_limit, last_usage_reset FROM users WHERE google_id = :google_id");
    $stmt->execute([':google_id' => $googleUserId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    $now_date = date('Y-m-d H:i:s');

    if (!$user) {
        $insertStmt = $db->prepare("INSERT INTO users (google_id, email, plan, subscription_status, usage_count, usage_limit, last_usage_reset, created_at, last_login) VALUES (:google_id, :email, 'free', 'active', 0, :usage_limit, :now, NOW(), NOW())");
        // Asumiendo que FREE_PLAN_LIMIT_DB está definido en config.php o es un valor conocido
        $default_free_limit = defined('FREE_PLAN_LIMIT_DB') ? FREE_PLAN_LIMIT_DB : 15; 
        $insertStmt->execute([':google_id' => $googleUserId, ':email' => $email, ':usage_limit' => $default_free_limit, ':now' => $now_date]);
        $userId = $db->lastInsertId();

        $user = [
            'id' => $userId,
            'google_id' => $googleUserId,
            'email' => $email,
            'plan' => 'free',
            'subscription_status' => 'active',
            'usage_count' => 0,
            'usage_limit' => $default_free_limit,
            'last_usage_reset' => $now_date,
            'current_period_end' => null
        ];

        if(!$userId) { 
            throw new Exception("No se pudo crear el usuario después de la inserción.");
        }
        error_log("Nuevo usuario creado: " . $email . " (Google ID: " . $googleUserId . ")");
    } else {
        if ($user['email'] !== $email) {
            $updateEmailStmt = $db->prepare("UPDATE users SET email = :email WHERE google_id = :google_id");
            $updateEmailStmt->execute([':email' => $email, ':google_id' => $googleUserId]);
            $user['email'] = $email; 
            error_log("Email actualizado para usuario: " . $googleUserId . " a " . $email);
        }
         $updateLoginStmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE google_id = :google_id");
         $updateLoginStmt->execute([':google_id' => $googleUserId]);
    }

    $usage_count = isset($user['usage_count']) ? (int)$user['usage_count'] : 0;
    $usage_limit = isset($user['usage_limit']) ? (int)$user['usage_limit'] : null;

    $userData = [
        'google_id' => $user['google_id'],
        'email' => $user['email'],
        'plan' => $user['plan'],
        'status' => $user['subscription_status'],
        'usage_count' => $usage_count,
        'usage_limit' => $usage_limit,
        'current_period_end' => $user['current_period_end']
    ];

    http_response_code(200); 
    echo json_encode($userData);

} catch (PDOException $e) {
    error_log("Database Error en verify.php: " . $e->getMessage());
    http_response_code(500); 
    $error_message = 'Error de base de datos al procesar el usuario.';
    if (ini_get('display_errors')) { 
        $error_message .= ' Detalles: ' . $e->getMessage();
    }
    echo json_encode(['error' => $error_message]);
    exit;
} catch (Exception $e) { 
    error_log("General Error en verify.php: " . $e->getMessage());
    http_response_code(500); 
    echo json_encode(['error' => 'Ocurrió un error inesperado.']);
    exit;
}

?>
