RewriteEngine On
# RewriteBase /  # Descomenta si tienes problemas y tu sitio no está en la raíz del dominio

# 1. Ignorar TODAS las reglas si la solicitud es para la carpeta /api/
RewriteRule ^api/ - [L]

# 2. Prohibir acceso directo a la carpeta _partials
RewriteRule ^_partials/ - [F,L]

# --- Reglas para URLs Limpias ---

# Aplica las siguientes condiciones a TODAS las reglas de reescritura de páginas
# Solo reescribir si la URL solicitada NO es un archivo existente...
RewriteCond %{REQUEST_FILENAME} !-f
# ...y NO es un directorio existente.
RewriteCond %{REQUEST_FILENAME} !-d

# 3. Reescribir /panel a /Pages/panel.php
# Si existe el archivo /Pages/panel.php...
RewriteCond %{DOCUMENT_ROOT}/Pages/panel.php -f
# ...reescribe la URL /panel (o /panel/)
RewriteRule ^panel/?$ /Pages/panel.php [L]

# 4. Reescribir /panel/pagina a /Pages/panel/pagina.php
RewriteCond %{DOCUMENT_ROOT}/Pages/panel/$1.php -f
RewriteRule ^panel/([\w-]+)/?$ /Pages/panel/$1.php [L]

# 5. Reescribir /verificar-email a /Pages/verificar-email.php
# Si existe el archivo /Pages/verificar-email.php...
RewriteCond %{DOCUMENT_ROOT}/Pages/verificar-email.php -f
# ...reescribe /verificar-email (NO captura query string, Apache lo pasa automáticamente)
RewriteRule ^verificar-email/?$ /Pages/verificar-email.php [L]

# NUEVA REGLA: Reescribir /gsc-inspector/pagina a /Pages/gsc-inspector/pagina.php
RewriteCond %{DOCUMENT_ROOT}/Pages/gsc-inspector/$1.php -f
RewriteRule ^gsc-inspector/([\w-]+)/?$ /Pages/gsc-inspector/$1.php [L]

# --- REGLAS PARA VERSIÓN EN INGLÉS (/en/) ---

# EN 1: Reescribir /en/ (página de inicio en inglés) a /en/index.php
RewriteCond %{DOCUMENT_ROOT}/en/index.php -f
RewriteRule ^en/?$ /en/index.php [L]

# EN 2: Reescribir /en/gsc-inspector/pagina a /en/Pages/gsc-inspector/pagina.php
RewriteCond %{DOCUMENT_ROOT}/en/Pages/gsc-inspector/$1.php -f
RewriteRule ^en/gsc-inspector/([\w-]+)/?$ /en/Pages/gsc-inspector/$1.php [L]

# EN 3: Reescribir /en/pagina (otras páginas en inglés) a /en/Pages/pagina.php (ej: /en/contact)
RewriteCond %{DOCUMENT_ROOT}/en/Pages/$1.php -f
RewriteRule ^en/([\w-]+)/?$ /en/Pages/$1.php [L]

# --- FIN REGLAS /en/ ---

# 6. Reescribir otras /pagina (en la raíz) a /Pages/pagina.php (ej: /login, /registro)
# Si existe el archivo /Pages/pagina.php...
RewriteCond %{DOCUMENT_ROOT}/Pages/$1.php -f
# ...reescribe /pagina (o /pagina/)
RewriteRule ^([\w-]+)/?$ /Pages/$1.php [L]

# --- Variables de entorno ---
SetEnv STRIPE_SECRET_KEY ***********************************************************************************************************
SetEnv STRIPE_PUBLIC_KEY pk_live_51RGRpoFWcf0HvuxoKMrLXEApLFV4LKL2cBbb8z7zHqyWopuCQxhWZQciEVNWbj1dJlpL0PYEIlJxN8JYziyhXlws00vm12cCVe
SetEnv STRIPE_WEBHOOK_SECRET whsec_mehcgA51VCL51xHRszrBg3Kvzb33nU2s
SetEnv STRIPE_PRICE_ID_PERSONAL price_1RIYhnFWcf0Hvuxo6TDDoswc
SetEnv STRIPE_PRICE_ID_PLUS price_1RIYhnFWcf0HvuxoQBHdz7ha
SetEnv STRIPE_PRICE_ID_AGENCY price_1RIYhnFWcf0Hvuxo5FrXcG1A

# --- Opcional: Redireccionar trailing slash ---
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteRule ^(.*)/$ /$1 [L,R=301]

# --- (Opcional pero recomendado) Regla para index.php ---
# DirectoryIndex index.php

# --- (Aquí irían otras reglas si tuvieras) --- 