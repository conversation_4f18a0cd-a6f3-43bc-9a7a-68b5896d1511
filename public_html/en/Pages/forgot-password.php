<?php 
session_start(); 

$pageTitle = 'Forgot Password | Pietra Mundo Digital'; // English Title
$pageDescription = 'Request to reset your password to access your GSC Inspector dashboard.'; // English Description
$noIndex = true; // Do not index this page
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/header.php'; // Use DOCUMENT_ROOT for partials
?>

<main class="container mx-auto px-4 py-16 flex justify-center items-center">
    <div class="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-700">
        <h1 class="text-3xl font-bold mb-6 text-center text-gray-100">Forgot Password</h1>
        
        <!-- Area to display messages (success/error from JS or backend) -->
        <div id="message-area" class="mb-6"> 
            <?php if (isset($_GET['success'])):
                 $successMessage = htmlspecialchars(urldecode($_GET['success']));
                  // Basic check for English
                 $isEnglishSuccess = preg_match('/[a-zA-Z]/ ', $successMessage) > 0;
                 // If not English, provide a generic one
                 if (!$isEnglishSuccess) {
                     $successMessage = 'If an account exists for this email, instructions have been sent.'; // Default success message
                 }
            ?>
                 <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                     <span class="block sm:inline"><?= $successMessage ?></span>
                 </div>
            <?php elseif (isset($_GET['error'])):
                 $errorMessage = htmlspecialchars(urldecode($_GET['error']));
                  // Basic check to see if the message is likely already in English
                 $isEnglishError = preg_match('/[a-zA-Z]/ ', $errorMessage) > 0; 
                 // If not English, provide a generic English message
                 if (!$isEnglishError) {
                     $errorMessage = 'An error occurred. Please try again.';
                 }
            ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline"><?= $errorMessage ?></span>
                </div>
            <?php else: ?>
                <p class="text-sm text-gray-400 text-center">Enter your email and we will send you instructions to reset your password.</p>
            <?php endif; ?>
        </div>

        <form action="/api/auth/request_password_reset.php" method="POST" class="space-y-6"> <!-- Adjust action to your API endpoint -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                <input type="email" name="email" id="email" required autocomplete="email"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2"
                    placeholder="<EMAIL>">
            </div>

            <div>
                <button type="submit" 
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-black bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition duration-300">
                    Send Instructions
                </button>
            </div>
        </form>

        <div class="mt-8 text-center">
            <p class="text-sm text-gray-400">
                Remembered your password? 
                <a href="/en/login" class="font-medium text-primary hover:underline">
                    Back to Login
                </a>
            </p>
        </div>
    </div>
</main>

<?php 
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/footer.php'; // Use DOCUMENT_ROOT for partials
?> 