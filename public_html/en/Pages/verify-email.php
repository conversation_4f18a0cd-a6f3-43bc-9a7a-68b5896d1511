<?php
// /en/Pages/verify-email.php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Adjust paths for includes based on the new location
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/config.php'; 
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/db.php'; 

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$token = $_GET['token'] ?? null;
$message = '';
$message_type = 'error'; // 'error' or 'success'

if (empty($token)) {
    $message = 'Invalid or missing verification token.';
} else {
    $db = getDbConnection();
    if (!$db) {
        $message = 'Internal server error (DB connection).';
    } else {
        try {
            // Find the token
            $stmt = $db->prepare("SELECT id, email, email_verify_expires_at FROM users WHERE email_verify_token = :token AND email_verified_at IS NULL");
            $stmt->execute([':token' => $token]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                $message = 'Invalid or expired verification token, or the email has already been verified.';
            } else {
                // Check if the token has expired
                $now = new DateTime();
                $expires = new DateTime($user['email_verify_expires_at']);

                if ($now > $expires) {
                    $message = 'The verification token has expired. Please register again or request the email to be resent.';
                    // Optional: Implement resend logic here
                } else {
                    // Valid token! Mark as verified and clear token
                    $updateStmt = $db->prepare("UPDATE users SET email_verified_at = NOW(), email_verify_token = NULL, email_verify_expires_at = NULL WHERE id = :id");
                    if ($updateStmt->execute([':id' => $user['id']])) {
                        $message = 'Your email address (' . htmlspecialchars($user['email']) . ') has been successfully verified! You can now log in.';
                        $message_type = 'success';
                        
                        // Optional: Log in automatically upon verification
                        // session_regenerate_id(true);
                        // $_SESSION['user_id'] = $user['id'];
                        // $_SESSION['user_email'] = $user['email'];
                        // header('Location: /en/panel?success=' . urlencode('Email verified. Welcome!')); // English path
                        // exit;

                    } else {
                        $message = 'Error updating verification status.';
                    }
                }
            }
             $stmt->closeCursor();

        } catch (PDOException $e) {
            error_log("DB Error in verify-email (EN): " . $e->getMessage());
            $message = 'Internal server error (DB) while verifying the token.';
        } catch (Exception $e) {
             error_log("General Error in verify-email (EN): " . $e->getMessage());
             $message = 'An unexpected error occurred.';
        }
    }
}

// Show result to the user
$pageTitle = 'Email Verification | Pietra Mundo Digital'; // English Title
$pageDescription = 'Result of your email address verification.'; // English Description
$noIndex = true;
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/header.php'; // Use DOCUMENT_ROOT for partials

?>
<main class="container mx-auto px-4 py-16 pt-24 flex justify-center items-center">
    <div class="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-700 text-center">
        <h1 class="text-2xl font-bold mb-6 text-gray-100">Email Verification</h1>
        
        <?php if ($message_type === 'success'): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline"><?= htmlspecialchars($message) ?></span>
            </div>
             <a href="/en/login" class="inline-block bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition duration-300"> <!-- English path -->
                 Go to Login
             </a>
        <?php else: ?>
             <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                 <span class="block sm:inline"><?= htmlspecialchars($message) ?></span>
             </div>
              <a href="/en/register" class="inline-block bg-secondary text-white px-6 py-2 rounded-lg hover:bg-secondary-dark transition duration-300"> <!-- English path -->
                 Back to Register
             </a>
        <?php endif; ?>
        
    </div>
</main>
<?php
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/footer.php'; // Use DOCUMENT_ROOT for partials
?> 