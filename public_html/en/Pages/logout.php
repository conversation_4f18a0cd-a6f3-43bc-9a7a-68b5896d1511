<?php
// /en/Pages/logout.php

// Start the existing session
session_start();

// Remove all session variables.
$_SESSION = array();

// If it's desired to kill the session completely, also delete the session cookie.
// Note: This will destroy the session, not just the session data!
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Finally, destroy the session.
session_destroy();

// Redirect to the English login page (clean URL) with a success message
header('Location: /en/login?success=' . urlencode('You have been logged out successfully.'));
exit;
?> 