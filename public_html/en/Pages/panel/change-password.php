<?php
session_start();

// Verify if the user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: /en/login?error=' . urlencode('You must log in to manage your password.')); // English path & message
    exit;
}

// --- Include configuration and DB connection --- 
// Adjust path for includes: up 3 levels from /en/Pages/panel to root, then into includes
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/config.php'; 
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/db.php'; 

$userId = $_SESSION['user_id'];
$userEmail = $_SESSION['user_email'] ?? 'your email'; // Get email for Google message
$successMessage = null;
$errorMessage = null;
$hasPassword = false; // Flag to know if show form or google message

// --- Check if the user has a password hash --- 
$db = getDbConnection();
if ($db) {
    try {
        $stmt = $db->prepare("SELECT password_hash FROM users WHERE id = :id");
        $stmt->execute([':id' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        // If they have a non-empty password hash, they can change it
        if ($user && !empty($user['password_hash'])) {
            $hasPassword = true;
        }
    } catch (PDOException $e) {
        $errorMessage = "Error verifying account type.";
        error_log("DB Error checking password_hash for User ID {$userId} (EN): " . $e->getMessage());
    }
} else {
    $errorMessage = "Internal server error (DB conn).";
    error_log("DB connection error in change-password.php (check hash) (EN)");
}

// --- Process the form if submitted AND if the user has a password --- 
if ($hasPassword && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmNewPassword = $_POST['confirm_new_password'] ?? '';

    if (empty($currentPassword) || empty($newPassword) || empty($confirmNewPassword)) {
        $errorMessage = "All fields are required.";
    } elseif ($newPassword !== $confirmNewPassword) {
        $errorMessage = "The new password and confirmation do not match.";
    } elseif (strlen($newPassword) < 8) {
        $errorMessage = "The new password must be at least 8 characters long.";
    } else {
        // Re-verify connection and hash before changing
        $db = getDbConnection(); // Re-get connection just in case
        if ($db) {
            try {
                $stmt = $db->prepare("SELECT password_hash FROM users WHERE id = :id");
                $stmt->execute([':id' => $userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($user && password_verify($currentPassword, $user['password_hash'])) {
                    $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                    $updateStmt = $db->prepare("UPDATE users SET password_hash = :new_hash WHERE id = :id");
                    if ($updateStmt->execute([':new_hash' => $newPasswordHash, ':id' => $userId])) {
                        $successMessage = "Password updated successfully.";
                    } else {
                        $errorMessage = "Error updating password in the database.";
                        error_log("DB Error updating password_hash for User ID {$userId} (EN)");
                    }
                } else {
                    $errorMessage = "The current password is not correct.";
                }
            } catch (PDOException $e) {
                $errorMessage = "Error connecting to the database.";
                error_log("DB Error changing password for User ID {$userId} (EN): " . $e->getMessage());
            }
        } else {
             $errorMessage = "Internal server error.";
             error_log("DB connection error in change-password.php (POST) (EN)");
         }
    }
}

// --- Prepare data for the view (Header) --- 
$pageTitle = 'Password | User Dashboard'; // English Title
$pageDescription = 'Manage your login password.'; // English Description
$noIndex = true; // Do not index this page
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/header.php'; // Use DOCUMENT_ROOT for partials

?>

<!-- Reuse panel structure: Sidebar + Content -->
<div class="flex flex-col md:flex-row min-h-screen pt-16">

    <!-- Sidebar -->
    <?php require $_SERVER['DOCUMENT_ROOT'] . '/_partials/panel_sidebar.php'; // Use DOCUMENT_ROOT for partials ?>

    <!-- Main Content -->
    <main class="flex-grow p-6 md:p-10 bg-darker text-white">
        <h1 class="text-3xl font-bold mb-6">Password</h1>

        <?php if ($successMessage): ?>
            <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline"><?= htmlspecialchars($successMessage) ?></span>
            </div>
        <?php endif; ?>
        <?php if ($errorMessage && !$successMessage): ?>
             <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
                 <strong class="font-bold">Error:</strong>
                 <span class="block sm:inline"><?= htmlspecialchars($errorMessage) ?></span>
             </div>
        <?php endif; ?>

        <?php 
        if ($hasPassword) {
            // Show form only if the user has a password 
        ?>
            <form action="/en/panel/change-password" method="POST" class="max-w-lg space-y-4"> <!-- English action path -->
                <div>
                    <label for="current_password" class="block text-sm font-medium text-gray-300 mb-1">Current Password</label>
                    <input type="password" name="current_password" id="current_password" required 
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
                <div>
                    <label for="new_password" class="block text-sm font-medium text-gray-300 mb-1">New Password</label>
                    <input type="password" name="new_password" id="new_password" required minlength="8"
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1">Minimum 8 characters.</p>
                </div>
                <div>
                    <label for="confirm_new_password" class="block text-sm font-medium text-gray-300 mb-1">Confirm New Password</label>
                    <input type="password" name="confirm_new_password" id="confirm_new_password" required minlength="8"
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
                <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-3 sm:space-y-0 pt-2">
                    <button type="submit" class="w-full sm:w-auto bg-primary text-black px-6 py-2 rounded-lg font-bold hover:bg-primary-dark transition duration-300 shadow-md">
                        Update Password
                    </button>
                    <a href="/en/forgot-password" class="w-full sm:w-auto text-center bg-gray-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-700 transition duration-300"> <!-- English path -->
                        Forgot Password
                    </a>
                </div>
            </form>
        <?php 
        } else { // User doesn't have a password (probably Google login)
        ?>
            <div class="bg-gray-800 border border-gray-700 text-gray-300 px-6 py-5 rounded-lg max-w-lg">
                <h2 class="text-lg font-semibold text-white mb-3">Google Sign-In</h2>
                <p class="mb-4">You are logged in using your Google account (<?= htmlspecialchars($userEmail) ?>).</p>
                <p class="mb-4">You don't need to manage a separate password in our system. To change your password, please do so directly in your Google account settings.</p>
                <a href="https://myaccount.google.com/security" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-primary hover:underline">
                    Go to Google security settings &rarr;
                </a>
            </div>
         <?php 
        } // End of if ($hasPassword) 
        ?>

    </main>
</div>

<?php 
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/footer.php'; // Use DOCUMENT_ROOT for partials
?> 