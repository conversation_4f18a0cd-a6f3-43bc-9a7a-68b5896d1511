<?php
session_start();

// Verify if the user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: /en/login?error=' . urlencode('You must log in to view your profile.')); // English path & message
    exit;
}

// --- Include configuration and DB connection --- 
// Adjust path for includes: up 3 levels from /en/Pages/panel to root, then into includes
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/config.php'; 
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/db.php'; 

$userId = $_SESSION['user_id'];
$successMessage = null;
$errorMessage = null;
$currentName = '';
$currentEmail = $_SESSION['user_email'] ?? 'Email not available'; // Email from session

// --- Get current user data --- 
$db = getDbConnection();
if ($db) {
    try {
        $stmt = $db->prepare("SELECT name, email FROM users WHERE id = :id"); // Get name and reconfirm email
        $stmt->execute([':id' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user) {
            $currentName = $user['name'] ?? '';
            $currentEmail = $user['email']; // Use email from DB just in case
        }
    } catch (PDOException $e) {
        $errorMessage = "Error loading profile data.";
        error_log("DB Error getting profile data for User ID {$userId} (EN): " . $e->getMessage());
    }
} else {
    $errorMessage = "Internal server error (DB conn).";
    error_log("DB connection error in profile.php (EN)");
}

// --- Process the form if submitted --- 
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $newName = trim($_POST['name'] ?? '');

    // Basic validation (only allow non-empty name)
    if (empty($newName)) {
        $errorMessage = "Name cannot be empty.";
    } else {
        // Try to update the name
        if ($db) { // Reuse connection
            try {
                $updateStmt = $db->prepare("UPDATE users SET name = :name WHERE id = :id");
                if ($updateStmt->execute([':name' => $newName, ':id' => $userId])) {
                    $successMessage = "Name updated successfully.";
                    $currentName = $newName; // Update the name displayed on the page
                } else {
                    $errorMessage = "Error updating name in the database.";
                    error_log("DB Error updating name for User ID {$userId} (EN)");
                }
            } catch (PDOException $e) {
                $errorMessage = "Error connecting to the database to update.";
                error_log("DB Error updating name for User ID {$userId} (EN): " . $e->getMessage());
            }
        } else {
            $errorMessage = "Internal server error (DB conn).";
        }
    }
}

// --- Prepare data for the view (Header) --- 
$pageTitle = 'Profile | User Dashboard'; // English Title
$pageDescription = 'Manage your profile information.'; // English Description
$noIndex = true; // Do not index this page
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/header.php'; // Use DOCUMENT_ROOT for partials

?>

<!-- Reuse panel structure: Sidebar + Content -->
<div class="flex flex-col md:flex-row min-h-screen pt-16">

    <!-- Sidebar -->
    <?php require $_SERVER['DOCUMENT_ROOT'] . '/_partials/panel_sidebar.php'; // Use DOCUMENT_ROOT for partials ?>

    <!-- Main Content -->
    <main class="flex-grow p-6 md:p-10 bg-darker text-white">
        <h1 class="text-3xl font-bold mb-6">Profile</h1>

        <?php if ($successMessage): ?>
            <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline"><?= htmlspecialchars($successMessage) ?></span>
            </div>
        <?php endif; ?>
        <?php if ($errorMessage && !$successMessage): ?>
             <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
                 <strong class="font-bold">Error:</strong>
                 <span class="block sm:inline"><?= htmlspecialchars($errorMessage) ?></span>
             </div>
        <?php endif; ?>

        <div class="max-w-lg bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
            <form action="/en/panel/profile" method="POST" class="space-y-4"> <!-- English action path -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-400 mb-1">Email</label>
                    <input type="email" name="email" id="email" value="<?= htmlspecialchars($currentEmail) ?>" readonly disabled
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-400 cursor-not-allowed">
                    <p class="text-xs text-gray-500 mt-1">Email cannot be changed.</p>
                </div>
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Name</label>
                    <input type="text" name="name" id="name" value="<?= htmlspecialchars($currentName) ?>" required 
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
                
                <div>
                    <button type="submit" class="w-full md:w-auto bg-accent text-black px-6 py-2 rounded-lg font-bold hover:bg-accent-dark transition duration-300 shadow-md">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>

    </main>
</div>

<?php 
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/footer.php'; // Use DOCUMENT_ROOT for partials
?> 