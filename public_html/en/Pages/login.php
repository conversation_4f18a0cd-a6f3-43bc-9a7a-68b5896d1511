<?php 
session_start(); // <-- Moved here, absolute beginning

$pageTitle = 'User Login | Pietra Mundo Digital'; // English Title
$pageDescription = 'Access your GSC Inspector dashboard to manage your subscription, view usage history and metrics.'; // English Description
$noIndex = true; // Add meta robots noindex to this page
$webClientId = '***********-mpciacvvb3mn7tdtfea58b5dmm4sv0vc.apps.googleusercontent.com'; // <-- ADD YOUR WEB CLIENT ID HERE (already set)
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/header.php'; // Use DOCUMENT_ROOT for partials
?>

<!-- ADD: Google Platform Library Script (already included in header.php but good to have redundancy maybe?) -->
<!-- <script src="https://accounts.google.com/gsi/client" async defer></script> --> 
<!-- Removed as header.php includes it -->

<main class="container mx-auto px-4 py-16 flex justify-center items-center">
    <div class="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-700">
        <h1 class="text-3xl font-bold mb-6 text-center text-gray-100">User Login</h1>
        
        <!-- Error/Success Messages -->
        <div id="message-area" class="mb-6"> <!-- Container for JS messages -->
            <?php if (isset($_GET['error'])):
                // Decode the error message from the URL parameter
                $errorMessage = htmlspecialchars(urldecode($_GET['error']));
                // Basic check to see if the message is likely already in English
                $isEnglishError = preg_match('/[a-zA-Z]/ ', $errorMessage) > 0; 
                // If not English, provide a generic English message
                if (!$isEnglishError) {
                    $errorMessage = 'An error occurred. Please try again.';
                }    
            ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline"><?= $errorMessage ?></span>
                </div>
            <?php endif; ?>
            <?php if (isset($_GET['success'])):
                 // Decode the success message from the URL parameter
                 $successMessage = htmlspecialchars(urldecode($_GET['success']));
                 // Basic check for English
                 $isEnglishSuccess = preg_match('/[a-zA-Z]/ ', $successMessage) > 0;
                 // If not English, provide a generic one
                 if (!$isEnglishSuccess) {
                     $successMessage = 'Operation successful.'; // Default success message
                 }
            ?>
                 <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                     <span class="block sm:inline"><?= $successMessage ?></span>
                 </div>
            <?php endif; ?>
        </div>

        <!-- Google Sign-In Button -->
        <div id="google-signin-button-container" class="mb-4">
             <div id="g_id_onload"
                  data-client_id="<?= $webClientId ?>"
                  data-context="signin"
                  data-ux_mode="popup"
                  data-callback="handleGoogleSignIn"
                  data-nonce="" 
                  data-auto_prompt="false">
             </div>
             <div class="g_id_signin"
                  data-type="standard"
                  data-shape="rectangular"
                  data-theme="filled_black" 
                  data-text="signin_with" 
                  data-size="large"
                  data-logo_alignment="left">
            </div>
        </div>

        <div class="relative flex py-5 items-center">
            <div class="flex-grow border-t border-gray-600"></div>
            <span class="flex-shrink mx-4 text-gray-500 text-sm">Or sign in with your email</span>
            <div class="flex-grow border-t border-gray-600"></div>
        </div>

        <form action="/api/auth/login.php" method="POST" class="space-y-6"> <!-- API path should remain the same -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                <input type="email" name="email" id="email" required autocomplete="email"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2"
                    placeholder="<EMAIL>">
            </div>

            <div>
                <div class="flex justify-between items-baseline">
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-1">Password</label>
                    <a href="#" class="text-sm text-primary hover:underline">Forgot your password?</a> <!-- Placeholder link -->
                </div>
                <input type="password" name="password" id="password" required autocomplete="current-password"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2">
            </div>

            <div>
                <button type="submit" 
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-black bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition duration-300">
                    Sign in
                </button>
            </div>
        </form>

        <div class="mt-8 text-center">
            <p class="text-sm text-gray-400">
                Don't have an account yet? 
                <a href="/en/register" class="font-medium text-primary hover:underline"> <!-- Updated link to /en/register -->
                    Sign up here
                </a>
            </p>
        </div>
    </div>
</main>

<!-- ADD: JavaScript to handle Google callback and send to backend -->
<script>
    function handleGoogleSignIn(response) {
        console.log("Google Sign-In Response:", response);
        const idToken = response.credential;
        const messageArea = document.getElementById('message-area');
        messageArea.innerHTML = ''; // Clear previous messages

        // Show loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'text-center text-gray-400'; // Adjusted color
        loadingIndicator.textContent = 'Verifying with server...';
        messageArea.appendChild(loadingIndicator);

        // Send ID Token to backend for verification and login
        fetch('/api/auth/verify_google_web.php', { // API path should remain the same
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ credential: idToken })
        })
        .then(res => {
            if (!res.ok) {
                 // Try to read JSON error, otherwise use statusText
                 return res.json().catch(() => null).then(errData => {
                     throw new Error(errData?.error || `Server error: ${res.statusText}`);
                 });
            }
            return res.json();
        })
        .then(data => {
            messageArea.innerHTML = ''; // Clear loading
            if (data.success) {
                // Success: Redirect to the user panel (clean URL, English version)
                window.location.href = '/en/panel'; // Updated redirect 
            } else {
                // Show error returned by the backend
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative';
                errorDiv.textContent = data.error || 'Unknown error processing Google sign-in.';
                messageArea.appendChild(errorDiv);
            }
        })
        .catch(error => {
            console.error('Error fetching verify_google_web:', error);
            messageArea.innerHTML = ''; // Clear loading
            const errorDiv = document.createElement('div');
            errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative';
            errorDiv.textContent = error.message || 'Could not connect to the server to verify sign-in.';
            messageArea.appendChild(errorDiv);
        });
    }
</script>

<?php 
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/footer.php'; // Use DOCUMENT_ROOT for partials
?> 