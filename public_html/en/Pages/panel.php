<?php
// Mandatory session start at the beginning of EACH protected page
session_start();


// --- Logic for Post-Checkout Welcome Message (Added) ---
$showWelcomeMessage = false;
if (isset($_GET['checkout']) && $_GET['checkout'] === 'success') {
    // Set a session variable to show the message
    $_SESSION['show_checkout_success'] = true;
    // Redirect to the same page WITHOUT the parameter to clean the URL
    header('Location: /en/panel'); // Ensure /en/panel is the correct URL for this page
    exit;
}

// Check if the session variable exists and then remove it
if (isset($_SESSION['show_checkout_success']) && $_SESSION['show_checkout_success'] === true) {
    $showWelcomeMessage = true;
    unset($_SESSION['show_checkout_success']); // Remove so it doesn't show again
}
// --- End Welcome Message Logic ---

// Verify if the user is logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_email'])) {
    // If not logged in, redirect to the login page
    header('Location: /en/login?error=' . urlencode('You must log in to access the dashboard.')); // Use English path and message
    exit;
}

// --- If reached here, the user is logged in --- 

// --- Include configuration and DB connection --- 
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/config.php'; // Use correct path from /en/Pages
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/db.php'; // Use correct path from /en/Pages

$pageTitle = 'User Dashboard | Pietra Mundo Digital'; // English Title
$pageDescription = 'Manage your account, subscription, and tool usage.'; // English Description
$noIndex = true; // Do not index the panel
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/header.php'; // Use correct path for partials

// --- Get user data from session and DB --- 
$userEmail = htmlspecialchars($_SESSION['user_email']);
$userId = $_SESSION['user_id']; 

// Variables for DB data
$userPlan = 'Unknown';
$subscriptionStatus = 'Unknown';
$usageCount = 0;
$usageLimit = null; // Can be null for unlimited or a number
$currentPeriodEnd = null;
$dbError = false;

$db = getDbConnection();
if ($db) {
    try {
        $stmt = $db->prepare("SELECT plan, subscription_status, usage_count, usage_limit, current_period_end FROM users WHERE id = :id");
        $stmt->execute([':id' => $userId]);
        $userDataFromDb = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($userDataFromDb) {
            $userPlan = htmlspecialchars($userDataFromDb['plan']);
            $subscriptionStatus = htmlspecialchars($userDataFromDb['subscription_status']);
            $usageCount = (int)$userDataFromDb['usage_count'];
            // Limit can be null in DB if we define it that way for unlimited
            $usageLimit = $userDataFromDb['usage_limit'] === null ? null : (int)$userDataFromDb['usage_limit'];
            $currentPeriodEnd = $userDataFromDb['current_period_end']; // Format later if needed
        } else {
            // Session user not found in DB, something is wrong
            error_log("Error Panel EN: User ID {$userId} from session not found in DB.");
            $dbError = true;
        }
    } catch (PDOException $e) {
        error_log("DB Error in panel.php (EN) for User ID {$userId}: " . $e->getMessage());
        $dbError = true;
    }
} else {
    error_log("Error Panel EN: Could not connect to the DB.");
    $dbError = true;
}

// Format period end date if it exists
$formattedPeriodEnd = null; // Changed default to null
if ($currentPeriodEnd) {
    try {
        $date = new DateTime($currentPeriodEnd);
        $formattedPeriodEnd = $date->format('d/m/Y'); // Format day/month/year
    } catch (Exception $e) { 
        $formattedPeriodEnd = 'Invalid date';
    }
}

// Status translation mapping (add more if used)
$statusTranslations = [
    'active' => 'Active',
    'trialing' => 'Trialing',
    'past_due' => 'Past Due',
    'canceled' => 'Canceled',
    'unpaid' => 'Unpaid',
    // Add other statuses if relevant
];
$translatedStatus = $statusTranslations[strtolower($subscriptionStatus)] ?? ucfirst($subscriptionStatus); // Fallback to original capitalized

// Format usage limit for display
if (strtolower($userPlan) === 'free') { // Use strtolower for comparison
    $limitDisplay = defined('FREE_PLAN_LIMIT') ? FREE_PLAN_LIMIT : 30; // Use constant, fallback if undefined
} else {
    $limitDisplay = $usageLimit === null ? 'Unlimited' : $usageLimit;
}

?>

<!-- Main container with Flexbox for Sidebar + Content -->
<div class="flex flex-col md:flex-row min-h-screen pt-16"> <?php // pt-16 to offset fixed nav, adjust if needed ?>

    <!-- Sidebar (visible on md and up, hidden on mobile) -->
    <?php require $_SERVER['DOCUMENT_ROOT'] . '/_partials/panel_sidebar.php'; // Include the partial, assuming path is correct from root ?>

    <!-- Main Content -->
    <main class="flex-grow p-6 md:p-10 bg-darker text-white"> <?php // Changed background to darker, adjust if needed ?>
        
        <!-- Button to show Sidebar on Mobile (FUTURE FUNCTIONALITY) -->
        <!-- 
        <button id="mobile-menu-button" class="md:hidden mb-4 text-white p-2 bg-gray-700 rounded">
             Menu
        </button> 
        -->
        
        <h1 class="text-3xl font-bold mb-6">Account Overview</h1>
        
        <p class="mb-6 text-lg">Welcome back, <strong class="font-semibold"><?= $userEmail ?></strong>.</p>

        <?php // --- Show Welcome Message if applicable (Added) --- 
        if ($showWelcomeMessage): ?>
        <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Subscription activated!</strong>
            <span class="block sm:inline">Thank you for subscribing. Your plan has been updated.</span>
        </div>
        <?php endif; 
        // --- End Welcome Message ---
        ?>

        <?php if ($dbError): ?>
        <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Error:</strong>
            <span class="block sm:inline">Could not load your complete account information. Try again later or contact support.</span>
        </div>
        <?php endif; ?>

        <!-- General Information Section (Now with real data) -->
        <section class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-100">Your Account Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3 text-gray-300"> <?php // Adjusted gap ?>
                <div>
                    <strong>Email:</strong> <?= $userEmail ?>
                </div>
                <div>
                    <strong>Current Plan:</strong> 
                    <a href="/en/gsc-inspector#pricing-plans" class="font-medium text-primary capitalize hover:underline"><?= $userPlan ?></a> <?php // Link added and path updated ?>
                </div>
                <div>
                    <strong>Subscription Status:</strong> 
                    <?php 
                    // Colors based on status ... (existing code)
                    $statusColor = 'text-gray-400'; // ...
                    if (strtolower($subscriptionStatus) === 'active' || strtolower($subscriptionStatus) === 'trialing') {
                        $statusColor = 'text-green-400';
                    } elseif (strtolower($subscriptionStatus) === 'past_due' || strtolower($subscriptionStatus) === 'unpaid') {
                        $statusColor = 'text-yellow-400';
                    } elseif (strtolower($subscriptionStatus) === 'canceled') {
                         $statusColor = 'text-red-400';
                    }
                    ?>
                    <span class="<?= $statusColor ?> font-medium"><?= $translatedStatus ?></span> <?php // Use translated ?>
                </div>
                 <div>
                     <strong>GSC Inspector Usage (this month):</strong> 
                     <span><?= $usageCount ?> / <?= $limitDisplay ?></span>
                 </div>
                 <?php if ($formattedPeriodEnd && (strtolower($subscriptionStatus) === 'active' || strtolower($subscriptionStatus) === 'trialing')): ?>
                 <div class="md:col-span-2"> <?php // Spans both columns on md if date exists ?>
                     <strong>Current period ends:</strong> <?= $formattedPeriodEnd ?>
                 </div>
                <?php endif; ?>
            </div>
        </section>

        <!-- Manage Account Section (Removed, now in sidebar or portal) -->
        
        <!-- New Section: Available Plans (Replicated from gsc-inspector.php) -->
        <section id="available-plans" class="mb-8">
            <h2 class="text-2xl font-bold text-center mb-10">Manage Plan</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                
                <!-- Free Plan -->
                <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                    <h3 class="text-xl font-semibold mb-2 text-gray-100">Free</h3>
                    <p class="text-gray-400 mb-4">Ideal for getting started</p>
                    <p class="text-3xl font-bold mb-4 text-gray-100">Free</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> <?= defined('FREE_PLAN_LIMIT') ? FREE_PLAN_LIMIT : 30 ?> Inspections/month</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Basic data</li>
                        </ul>
                    <?php if (strtolower($userPlan) === 'free'): ?>
                         <button class="mt-auto w-full bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-not-allowed" disabled>Current Plan</button>
                    <?php else: ?>
                         <button class="mt-auto w-full bg-gray-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-gray-600 transition duration-300" disabled title="Downgrade not available directly">Select Free</button> <?php // Or handle downgrade if implemented ?>
                    <?php endif; ?>
                </div>

                <!-- Personal Plan -->
                <div class="bg-gray-800 p-6 rounded-lg shadow-lg border <?= (strtolower($userPlan) === 'personal') ? 'border-primary ring-2 ring-primary' : 'border-gray-700' ?> flex flex-col relative overflow-hidden">
                    <?php if (strtolower($userPlan) !== 'personal'): // Only show if not the current plan ?>
                    <span class="absolute top-0 right-0 w-32 h-8 bg-primary flex items-center justify-center text-white text-xs font-bold transform rotate-45 translate-x-8 translate-y-4 border-2 border-white">Most Popular</span>
                    <?php endif; ?>
                    <h3 class="text-xl font-bold text-accent mb-2">Personal</h3>
                    <p class="text-3xl font-bold text-white mb-1">9.99 €<span class="text-lg font-normal text-gray-500">/month</span></p>
                    <p class="text-xs text-gray-500 mb-4">VAT not included</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 120 Inspections/month</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Full data</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Date comparison</li>
                    </ul>
                    <?php if (strtolower($userPlan) === 'personal'): ?>
                        <button class="mt-auto w-full bg-accent text-black px-6 py-2 rounded-lg text-lg font-semibold cursor-not-allowed" disabled>Current Plan</button>
                    <?php else: ?>
                        <button data-price-id="<?= defined('STRIPE_PRICE_ID_PERSONAL') ? STRIPE_PRICE_ID_PERSONAL : '' ?>" class="stripe-checkout-button mt-auto bg-accent text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-accent-dark transition duration-300">Choose Plan</button>
                    <?php endif; ?>
                </div>

                <!-- Plus Plan -->
                 <div class="bg-gray-800 p-6 rounded-lg shadow-lg border <?= (strtolower($userPlan) === 'plus') ? 'border-yellow-500 ring-2 ring-yellow-500' : 'border-gray-700' ?> flex flex-col">
                    <h3 class="text-xl font-bold text-yellow-500 mb-2">Plus</h3>
                    <p class="text-3xl font-bold text-white mb-1">19.99 €<span class="text-lg font-normal text-gray-500">/month</span></p>
                    <p class="text-xs text-gray-500 mb-4">VAT not included</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 360 Inspections/month</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Everything in Personal</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Data export (Soon)</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Priority support</li>
                    </ul>
                    <?php if (strtolower($userPlan) === 'plus'): ?>
                        <button class="mt-auto w-full bg-yellow-500 text-black px-6 py-2 rounded-lg text-lg font-semibold cursor-not-allowed" disabled>Current Plan</button>
                    <?php else: ?>
                        <button data-price-id="<?= defined('STRIPE_PRICE_ID_PLUS') ? STRIPE_PRICE_ID_PLUS : '' ?>" class="stripe-checkout-button mt-auto bg-yellow-500 text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-yellow-600 transition duration-300">Choose Plan</button>
                    <?php endif; ?>
                </div>

                 <!-- Agency Plan -->
                 <div class="bg-gray-800 p-6 rounded-lg shadow-lg border <?= (strtolower($userPlan) === 'agency') ? 'border-blue-400 ring-2 ring-blue-400' : 'border-gray-700' ?> flex flex-col">
                    <h3 class="text-xl font-bold text-blue-400 mb-2">Agency</h3>
                    <p class="text-3xl font-bold text-white mb-1">49.99 €<span class="text-lg font-normal text-gray-500">/month</span></p>
                    <p class="text-xs text-gray-500 mb-4">VAT not included</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 1200 Inspections/month</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Everything in Plus</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> API Access (Soon)</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Multiple users (Soon)</li>
                    </ul>
                     <?php if (strtolower($userPlan) === 'agency'): ?>
                         <button class="mt-auto w-full bg-black text-white px-6 py-2 rounded-lg text-lg font-semibold cursor-not-allowed border border-gray-600" disabled>Current Plan</button>
                     <?php else: ?>
                         <button data-price-id="<?= defined('STRIPE_PRICE_ID_AGENCY') ? STRIPE_PRICE_ID_AGENCY : '' ?>" class="stripe-checkout-button mt-auto bg-black text-white px-6 py-2 rounded-lg text-lg font-semibold hover:bg-gray-900 transition duration-300 border border-gray-600">Choose Plan</button>
                     <?php endif; ?>
                 </div>
             </div>
             <p class="text-center mt-8 text-gray-400">Plan changes will take effect at the start of the next billing cycle.</p>
         </section>

    </main>
</div>

<?php 
// Add Stripe Checkout script if not already in global footer
// Ensure this script is loaded ONLY ONCE per page.
// If already in footer.php, it's NOT needed here.
// For precaution, added conditionally here (better if in footer)
if (isset($_SESSION['user_id'])) : 
?>
<script>
document.addEventListener('DOMContentLoaded', () => {
    const checkoutButtons = document.querySelectorAll('.stripe-checkout-button');

    checkoutButtons.forEach(button => {
        button.addEventListener('click', async (event) => {
            const priceId = button.getAttribute('data-price-id');
            if (!priceId) {
                console.error('Stripe Price ID not found on the button.');
                alert('Error: Could not get the plan ID. Contact support.');
                return;
            }
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = 'Processing...';

            try {
                const response = await fetch('/api/create_checkout_session.php', { // API path remains the same
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ priceId: priceId })
                });
                const restoreButton = () => { button.disabled = false; button.textContent = originalText; };

                if (!response.ok) {
                    if (response.status === 401) { // Assuming 401 for unauthenticated
                         alert('Your session has expired or you are not authenticated. Please log in again.');
                         window.location.href = '/en/login?redirect=/en/panel'; // Redirect to English login
                         throw new Error('User not authenticated');
                    }
                    const errorData = await response.json().catch(() => ({ error: `Server error (${response.status})` }));
                    alert(`Error initiating payment: ${errorData.error || response.statusText}`);
                    restoreButton();
                    throw new Error(`Server error: ${response.status}`);
                }

                const session = await response.json();
                if (session.url) {
                    window.location.href = session.url;
                } else {
                     console.error('Stripe URL not received.');
                     alert('Error: Could not redirect to the payment page.');
                     restoreButton();
                }
            } catch (error) {
                console.error('Error creating Stripe Checkout session:', error);
                if (error.message !== 'User not authenticated' && !error.message.startsWith('Server error')) {
                    alert('An unexpected error occurred.');
                   button.disabled = false;
                   button.textContent = originalText;
                }
            }
        });
    });
});
</script>
<?php 
endif;
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/footer.php'; // Use correct path for partials
?> 