<?php 
session_start(); // Start session to get user email if logged in
// Include config to access Stripe Price IDs
require_once $_SERVER['DOCUMENT_ROOT'] . '/../includes/config.php'; // Correct path: Go up one level from DOC_ROOT
$pageTitle = 'GSC Inspector - Chrome Extension for Google Search Console | Pietra Mundo Digital';
$pageDescription = 'Analyze index status, performance, and key metrics for any URL directly from Google Search Console with GSC Inspector.';
$canonicalUrl = 'https://www.pietramundodigital.com/en/gsc-inspector'; // Update canonical URL to English path
require $_SERVER['DOCUMENT_ROOT'] . '/_partials/header.php'; // This path should be correct as _partials is inside public_html
?>

<main class="container mx-auto px-4 py-12 pt-24">
    <!-- Hero Section -->
    <section class="text-center mb-20">
        <h1 class="text-4xl md:text-5xl font-bold mb-4 leading-tight">GSC Inspector: Your Shortcut to Key Search Console Data</h1>
        <p class="text-xl text-gray-400 mb-8 max-w-3xl mx-auto">Stop wasting time navigating GSC. Get index status, canonicals, performance, and more, directly on the URL you're visiting.</p>
        <div class="flex justify-center items-center space-x-4 mb-10">
            <a href="https://chromewebstore.google.com/detail/gsc-inspector-seo-tool/llglmegjjadpomhgnoofhpboooadegga?authuser=0&hl=en" target="_blank" rel="noopener noreferrer" class="bg-primary text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-primary-dark transition duration-300 shadow-md">
                Install Chrome Extension
            </a>
            <a href="#pricing-plans" class="text-primary hover:underline">
                View Plans & Pricing
            </a>
        </div>
        <!-- YouTube video embed -->
        <iframe
            id="video-demostracion"
            class="w-full block border-none"
            style="aspect-ratio: 16 / 9;"
            src="https://www.youtube.com/embed/KOK-ukGI9ac?mute=1&controls=01" title="Extension demo video"
            frameborder="0"
            allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerpolicy="strict-origin-when-cross-origin"
            allowfullscreen>
        </iframe>
        <div id="video-overlay" class="absolute top-0 left-0 cursor-pointer z-10"></div>
        <script src="https://www.youtube.com/iframe_api"></script>
        <script>
        // YouTube player script (no changes needed, no user-facing text)
        var player;

        function onYouTubeIframeAPIReady() {
            player = new YT.Player('video-demostracion', {
            events: {
                'onReady': onPlayerReady,
                'onStateChange': onPlayerStateChange
            }
            });
        }

        function onPlayerReady(event) {
            var overlay = document.getElementById('video-overlay');
            if (overlay) {
            overlay.addEventListener('click', function() {
                togglePlayPause();
            });
            }
            setTimeout(function() {
            if (player && typeof player.playVideo === 'function') {
                player.playVideo();
            }
            }, 2000);
        }

        function togglePlayPause() {
            if (!player || typeof player.getPlayerState !== 'function') return;
            var playerState = player.getPlayerState();
            if (playerState === YT.PlayerState.PLAYING) {
            player.pauseVideo();
            } else {
            player.playVideo();
            }
        }

        function onPlayerStateChange(event) {
            if (event.data === YT.PlayerState.ENDED) {
            setTimeout(function() {
                if (player && typeof player.seekTo === 'function') {
                player.seekTo(0);
                player.pauseVideo();
                }
            }, 100);
         }
        }
        </script>
    </section>

    <!-- Key Benefits -->
    <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-12">Why GSC Inspector?</h2>
        <div class="grid md:grid-cols-3 gap-10">
            <div class="text-center px-4">
                <div class="text-4xl text-primary mb-4">⏱️</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-100">Save Precious Time</h3>
                <p class="text-gray-400">Access vital GSC data (indexing, performance, canonicals) with a single click, without leaving the page you're analyzing.</p>
            </div>
            <div class="text-center px-4">
                 <div class="text-4xl text-primary mb-4">📊</div>
                 <h3 class="text-xl font-semibold mb-2 text-gray-100">Actionable Data Instantly</h3>
                 <p class="text-gray-400">Visualize clicks, impressions, CTR, and average position directly in the extension. Identify opportunities and issues quickly.</p>
            </div>
            <div class="text-center px-4">
                 <div class="text-4xl text-primary mb-4">🔗</div>
                 <h3 class="text-xl font-semibold mb-2 text-gray-100">Quick Indexing Diagnosis</h3>
                 <p class="text-gray-400">Check if a URL is indexed, see Google's canonical vs. declared, and find the last crawl date.</p>
            </div>
        </div>
    </section>

    <!-- Pricing Plans -->
    <section id="pricing-plans" class="mb-20 scroll-mt-20">
        <h2 class="text-3xl font-bold text-center mb-12">Flexible Plans for Every Need</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Free Plan -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                <h3 class="text-xl font-semibold mb-2 text-gray-100">Free</h3>
                <p class="text-gray-400 mb-4">Ideal for getting started</p>
                <p class="text-3xl font-bold mb-4 text-gray-100">Free</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 30 Inspections/month</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Basic indexing data</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Basic performance data</li>
                </ul>
                <button class="mt-auto w-full bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-not-allowed" disabled>Current Plan (Free)</button>
            </div>

            <!-- Personal Plan -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-primary flex flex-col ring-2 ring-primary relative overflow-hidden">
                <span class="absolute top-0 right-0 w-32 h-8 bg-primary flex items-center justify-center text-white text-xs font-bold transform rotate-45 translate-x-8 translate-y-4 border-2 border-white">Most Popular</span>
                <h3 class="text-xl font-bold text-accent mb-2">Personal</h3>
                <p class="text-3xl font-bold text-white mb-1">9.99 €<span class="text-lg font-normal text-gray-500">/month</span></p>
                <p class="text-xs text-gray-500 mb-4">VAT not included</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 120 Inspections/month</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Full indexing data</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Full performance data</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Date comparison</li>
                </ul>
                <?php if (isset($_SESSION['user_id'])) : ?>
                    <button data-price-id="<?= defined('STRIPE_PRICE_ID_PERSONAL') ? STRIPE_PRICE_ID_PERSONAL : '' ?>" class="stripe-checkout-button mt-auto bg-accent text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-accent-dark transition duration-300">Choose Plan</button>
                <?php else : ?>
                    <a href="/en/login?redirect=/en/gsc-inspector%23pricing-plans" class="mt-auto text-center bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-pointer hover:bg-gray-500 transition duration-300">Login to choose</a>
                <?php endif; ?>
            </div>

            <!-- Plus Plan -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                <h3 class="text-xl font-bold text-yellow-500 mb-2">Plus</h3>
                <p class="text-3xl font-bold text-white mb-1">19.99 €<span class="text-lg font-normal text-gray-500">/month</span></p>
                <p class="text-xs text-gray-500 mb-4">VAT not included</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 360 Inspections/month</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Everything in Personal</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Data export (Coming Soon)</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Priority support</li>
                </ul>
                 <?php if (isset($_SESSION['user_id'])) : ?>
                    <button data-price-id="<?= defined('STRIPE_PRICE_ID_PLUS') ? STRIPE_PRICE_ID_PLUS : '' ?>" class="stripe-checkout-button mt-auto bg-yellow-500 text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-yellow-600 transition duration-300">Choose Plan</button>
                 <?php else : ?>
                    <a href="/en/login?redirect=/en/gsc-inspector%23pricing-plans" class="mt-auto text-center bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-pointer hover:bg-gray-500 transition duration-300">Login to choose</a>
                 <?php endif; ?>
            </div>

            <!-- Agency Plan -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                <h3 class="text-xl font-bold text-blue-400 mb-2">Agency</h3>
                <p class="text-3xl font-bold text-white mb-1">49.99 €<span class="text-lg font-normal text-gray-500">/month</span></p>
                <p class="text-xs text-gray-500 mb-4">VAT not included</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 1200 Inspections/month</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Everything in Plus</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> API Access (Coming Soon)</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Multiple users (Coming Soon)</li>
                </ul>
                 <?php if (isset($_SESSION['user_id'])) : ?>
                    <button data-price-id="<?= defined('STRIPE_PRICE_ID_AGENCY') ? STRIPE_PRICE_ID_AGENCY : '' ?>" class="stripe-checkout-button mt-auto bg-black text-white px-6 py-2 rounded-lg text-lg font-semibold hover:bg-gray-900 transition duration-300 border border-gray-600">Choose Plan</button>
                 <?php else : ?>
                    <a href="/en/login?redirect=/en/gsc-inspector%23pricing-plans" class="mt-auto text-center bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-pointer hover:bg-gray-500 transition duration-300">Login to choose</a>
                 <?php endif; ?>
            </div>
        </div>
         <p class="text-center mt-8 text-gray-400">Need more than 1200 inspections? <a href="/en/contact" class="text-primary hover:underline">Contact us</a> for Enterprise solutions.</p>
    </section>

    <!-- Final CTA Section -->
    <section class="text-center bg-gradient-to-r from-primary to-secondary text-white py-16 px-8 rounded-lg shadow-xl">
        <h2 class="text-3xl font-bold mb-4">Install GSC Inspector Today</h2>
        <p class="text-xl mb-8">Start saving time and making faster, more informed SEO decisions.</p>
        <a href="https://chromewebstore.google.com/detail/gsc-page-inspector-by-pie/llglmegjjadpomhgnoofhpboooadegga?authuser=0&hl=en" target="_blank" rel="noopener noreferrer" class="bg-accent text-black px-8 py-3 rounded-lg text-lg font-semibold hover:bg-accent-dark transition duration-300 shadow-md">
            Install Extension for Free
        </a>
    </section>

    <!-- "Ready to improve" Section -->
    <section class="text-center mb-16">
        <h2 class="text-2xl font-bold mb-6">Ready to improve your SEO?</h2>
        <a href="/en/login" class="bg-secondary text-white px-8 py-3 rounded-lg text-lg hover:bg-secondary-dark inline-block">
            Access Dashboard
        </a>
    </section>
</main>

<?php
// --- ADD NEW SCRIPT BLOCK FOR STRIPE CHECKOUT ---
if (isset($_SESSION['user_id'])) : // Only add this script if the user is logged in
?>
<script>
document.addEventListener('DOMContentLoaded', () => {
    const checkoutButtons = document.querySelectorAll('.stripe-checkout-button');

    checkoutButtons.forEach(button => {
        button.addEventListener('click', async (event) => {
            // We don't need preventDefault() if using <button type="button">, but keep it just in case

            const priceId = button.getAttribute('data-price-id');
            if (!priceId) {
                console.error('Stripe Price ID not found on the button.');
                alert('Error: Could not get the plan ID. Contact support.');
                return;
            }

            // Show loading state
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = 'Processing...';

            try {
                const response = await fetch('/api/create_checkout_session.php', { // Ensure the path to your API is correct
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // No 'Authorization' header needed here, PHP session is sent via cookie
                    },
                    body: JSON.stringify({ priceId: priceId }) // Send the correct Price ID
                });

                // Restore button in case of error before redirection
                const restoreButton = () => {
                   button.disabled = false;
                   button.textContent = originalText;
                };

                if (!response.ok) {
                    if (response.status === 401) {
                         alert('Your session has expired. Please log in again to subscribe.');
                         // Redirect to login
                         window.location.href = '/en/login?redirect=/en/gsc-inspector%23pricing-plans'; // Updated redirect path
                         // Don't restore the button because we redirect
                         throw new Error('User not authenticated'); // Exit try block
                    }
                    // Try to get error message from backend
                    const errorData = await response.json().catch(() => ({ error: `Server error (${response.status})` }));
                    alert(`Error initiating payment: ${errorData.error || response.statusText}`);
                    restoreButton(); // Restore button
                    throw new Error(`Server error: ${response.status}`); // Exit try block
                }

                const session = await response.json();

                // Redirect to Stripe Checkout
                if (session.url) {
                    window.location.href = session.url;
                    // Don't restore button here because redirection is imminent
                } else {
                     console.error('Stripe URL not received in the response.');
                     alert('Error: Could not redirect to the payment page. Contact support.');
                     restoreButton(); // Restore button
                }

            } catch (error) {
                console.error('Error creating Stripe Checkout session:', error);
                // If the error wasn't an HTTP error handled above, restore button and show generic alert
                if (error.message !== 'User not authenticated' && !error.message.startsWith('Server error')) {
                    alert('An unexpected error occurred. Please try again or contact support.');
                   button.disabled = false; // Ensure restore if another type of error occurred
                   button.textContent = originalText;
                }
            }
        });
    });
});
</script>
<?php
endif; // End of if (isset($_SESSION['user_id']))

require $_SERVER['DOCUMENT_ROOT'] . '/_partials/footer.php'; // This path should be correct as _partials is inside public_html
?>