<?php
// --- Configuración ---
// Correo electrónico donde recibirás las solicitudes de la calculadora
$recipient_email = "<EMAIL>"; // ¡TU EMAIL!
$email_subject_prefix = "[Calculadora SEO] ";

// Clave secreta de reCAPTCHA v3 (¡Mantenla segura!)
$recaptcha_secret_key = "6LcJ_QsrAAAAAMh3dsrvv1IhzRSPD3NAVRbTeAkx"; // ¡TU CLAVE SECRETA!

// Configuración SMTP (¡Reemplaza con tus datos!)
$smtp_host = 'smtp.gmail.com'; // O el de tu proveedor
$smtp_port = 465;                 // 465 para SSL, 587 para TLS
$smtp_security = 'ssl';           // 'ssl' o 'tls'
$smtp_username = '<EMAIL>'; // Tu dirección de correo SMTP
$smtp_password = 'cncw upcr pohw lejy'; // Tu contraseña de aplicación SMTP

// --- Incluir PHPMailer ---
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Si usas Composer:
// require 'vendor/autoload.php'; // Comentado porque usamos instalación manual
// Si descargaste manualmente (ajusta la ruta si es necesario):

require 'libs/PHPMailer/src/Exception.php';
require 'libs/PHPMailer/src/PHPMailer.php';
require 'libs/PHPMailer/src/SMTP.php';

// --- Funciones Auxiliares ---
function clean_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// --- Validaciones ---
$errors = [];
$response_status = 'calc_error'; // Estado por defecto

// Verificar método POST
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // 1. Validar reCAPTCHA
    if (!isset($_POST['recaptcha_response'])) {
        $errors[] = 'No se recibió el token reCAPTCHA.';
    } else {
        $recaptcha_token = $_POST['recaptcha_response'];
        $recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
        $recaptcha_data = [
            'secret'   => $recaptcha_secret_key,
            'response' => $recaptcha_token,
            'remoteip' => $_SERVER['REMOTE_ADDR'], // Opcional pero recomendado
        ];

        $options = [
            'http' => [
                'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
                'method'  => 'POST',
                'content' => http_build_query($recaptcha_data),
            ],
        ];
        $context = stream_context_create($options);
        $recaptcha_result_json = @file_get_contents($recaptcha_url, false, $context); // Usar @ para suprimir errores si falla la conexión

        if ($recaptcha_result_json === FALSE) {
            $errors[] = 'No se pudo conectar con el servicio reCAPTCHA.';
        } else {
            $recaptcha_result = json_decode($recaptcha_result_json);
            // Ajusta el score mínimo según necesites (0.5 es un buen punto de partida)
            // También verifica la acción ('submit_calculadora')
            if (!$recaptcha_result || !$recaptcha_result->success || $recaptcha_result->score < 0.5 || $recaptcha_result->action != 'submit_calculadora') {
                $errors[] = 'Verificación reCAPTCHA fallida. Por favor, inténtalo de nuevo.';
                 // Log detallado para depuración (¡Comenta esto en producción!)
                 // error_log("reCAPTCHA failed. Result: " . print_r($recaptcha_result, true));
            }
        }
    }

    // 2. Validar datos del formulario (si reCAPTCHA pasó)
    if (empty($errors)) {
        $nombre = clean_input($_POST['nombre'] ?? '');
        $email = clean_input($_POST['email'] ?? '');
        $telefono = clean_input($_POST['telefono'] ?? '');
        $web = clean_input($_POST['web'] ?? '');
        $mensaje = clean_input($_POST['mensaje'] ?? '');
        $servicios = isset($_POST['servicios']) && is_array($_POST['servicios']) ? $_POST['servicios'] : [];
        $privacy_policy = isset($_POST['privacy_policy']) && $_POST['privacy_policy'] === 'accepted';

        // Campos obligatorios
        if (empty($nombre)) $errors[] = 'El nombre es obligatorio.';
        if (empty($email)) $errors[] = 'El email es obligatorio.';
        elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'El formato del email no es válido.';
        if (!$privacy_policy) $errors[] = 'Debes aceptar la política de privacidad.';

        // Limpiar servicios
        $servicios_limpios = [];
        foreach ($servicios as $servicio) {
            $servicios_limpios[] = clean_input($servicio);
        }
        $servicios_str = !empty($servicios_limpios) ? implode(", ", $servicios_limpios) : 'Ninguno seleccionado';

        // Si no hay errores de validación, intentar enviar email
        if (empty($errors)) {
            $mail = new PHPMailer(true); // Habilita excepciones

            try {
                // Configuración del servidor SMTP
                $mail->isSMTP();
                $mail->Host       = $smtp_host;
                $mail->SMTPAuth   = true;
                $mail->Username   = $smtp_username;
                $mail->Password   = $smtp_password;
                $mail->SMTPSecure = ($smtp_security === 'ssl') ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
                $mail->Port       = $smtp_port;
                $mail->CharSet    = 'UTF-8'; // Para caracteres especiales

                // Remitente y Destinatario(s)
                // Es buena práctica usar el mismo email SMTP como 'From' y añadir el email del usuario en 'Reply-To'
                $mail->setFrom($smtp_username, 'Calculadora Web PietraMundo'); // Nombre del remitente
                $mail->addAddress($recipient_email); // Email donde recibirás la info
                $mail->addReplyTo($email, $nombre); // Para responder directamente al usuario

                // Contenido del Email
                $mail->isHTML(true); // Establecer formato HTML
                $mail->Subject = $email_subject_prefix . $nombre;

                // Cuerpo del email (HTML)
                $body = "<h2>Nueva solicitud desde la Calculadora SEO:</h2>";
                $body .= "<p><strong>Nombre:</strong> " . $nombre . "</p>";
                $body .= "<p><strong>Email:</strong> " . $email . "</p>";
                if (!empty($telefono)) $body .= "<p><strong>Teléfono:</strong> " . $telefono . "</p>";
                if (!empty($web)) $body .= "<p><strong>Web:</strong> <a href='" . $web . "'>" . $web . "</a></p>";
                $body .= "<p><strong>Servicios Seleccionados:</strong> " . $servicios_str . "</p>";
                if (!empty($mensaje)) $body .= "<p><strong>Mensaje Adicional:</strong><br>" . nl2br($mensaje) . "</p>"; // nl2br para saltos de línea
                $body .= "<hr><p><em>Enviado desde el formulario de calculadora de pietramundodigital.com</em></p>";

                $mail->Body = $body;

                // Cuerpo alternativo para clientes de email que no soportan HTML
                $altBody = "Nueva solicitud desde la Calculadora SEO:\n";
                $altBody .= "Nombre: " . $nombre . "\n";
                $altBody .= "Email: " . $email . "\n";
                if (!empty($telefono)) $altBody .= "Teléfono: " . $telefono . "\n";
                if (!empty($web)) $altBody .= "Web: " . $web . "\n";
                $altBody .= "Servicios Seleccionados: " . $servicios_str . "\n";
                if (!empty($mensaje)) $altBody .= "Mensaje Adicional:\n" . $mensaje . "\n";
                $altBody .= "\n---\nEnviado desde el formulario de calculadora de pietramundodigital.com";

                $mail->AltBody = $altBody;

                $mail->send();
                $response_status = 'calc_success'; // Éxito

            } catch (Exception $e) {
                $errors[] = "Error al enviar el mensaje: {$mail->ErrorInfo}";
                // Log detallado para depuración (¡Comenta esto en producción!)
                 // error_log("PHPMailer Error (Calculadora): " . $e->getMessage());
            }
        }
    }

} else {
    $errors[] = 'Método de solicitud no válido.';
}

// --- Respuesta JSON --- 
header('Content-Type: application/json'); // Indicar que la respuesta es JSON

$response = [];

if ($response_status === 'calc_success') {
    $response['status'] = 'success';
    $response['message'] = '¡Solicitud enviada con éxito! En breve recibirás el presupuesto.'; // Mensaje específico
} else {
    $response['status'] = 'error';
    // Construir mensaje de error detallado
    $error_detail = implode(" ", $errors); // Concatenar todos los errores
    if (!empty($error_message)) { // Añadir error SMTP si existe
        $error_detail .= " (Detalle SMTP: " . $error_message . ")";
    }
    $response['message'] = $error_detail ?: 'Error desconocido al procesar la solicitud.';
}

echo json_encode($response);
exit; // Detener el script después de enviar la respuesta JSON
?> 