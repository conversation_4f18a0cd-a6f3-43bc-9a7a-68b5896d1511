/**
 * Scripts personalizados del tema Pietra Mundo Digital
 */
document.addEventListener('DOMContentLoaded', function() {

    // Mobile menu toggle
    const menuBtn = document.getElementById('menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    if (menuBtn && mobileMenu) {
        menuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // SEO Calculator Form
    const seoForm = document.getElementById('seo-calculator-form');
    if (seoForm) {
        seoForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get selected services
            const services = [];
            const checkboxes = document.querySelectorAll('#calculadora input[type="checkbox"]:checked');
            
            checkboxes.forEach(checkbox => {
                const label = checkbox.closest('.flex.items-start').querySelector('label');
                if (label) {
                    services.push(label.textContent.trim()); // Usar trim() para limpiar espacios
                }
            });
            
            // Display selected services
            const servicesList = document.getElementById('services-list');
            const calculatorResult = document.getElementById('calculator-result');
            
            if (servicesList && calculatorResult) {
                servicesList.innerHTML = ''; // Limpiar lista anterior
                
                if (services.length > 0) {
                    services.forEach(service => {
                        const li = document.createElement('li');
                        li.className = 'flex items-center text-sm'; // Ajustar clases si es necesario
                        // Intenta añadir el icono si Font Awesome está cargado
                        li.innerHTML = `<i class="fas fa-check text-accent mr-2 text-xs"></i> ${service}`; 
                        servicesList.appendChild(li);
                    });
                } else {
                    // Mensaje si no se selecciona nada
                    const li = document.createElement('li');
                    li.textContent = 'No has seleccionado ningún servicio.';
                    servicesList.appendChild(li);
                }
                
                // Show result
                calculatorResult.classList.remove('hidden');
                
                // Scroll to result
                calculatorResult.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            
            // Reset form (en un escenario real, enviarías esto a un servidor)
            // No resetear aquí para que el usuario vea lo que seleccionó en el mensaje.
            // Podrías resetear después de un tiempo o al cerrar el mensaje.
            // this.reset(); 
        });
    }

    // Contact Form
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // En un escenario real, enviarías esto a un servidor (e.g., usando WP REST API o admin-ajax.php)
            // y mostrarías el mensaje según la respuesta del servidor.
            
            const formSuccess = document.getElementById('form-success');
            if (formSuccess) {
                // Simular envío exitoso
                formSuccess.classList.remove('hidden');
                
                // Reset form
                this.reset();
                
                // Hide success message after 5 seconds
                setTimeout(() => {
                    formSuccess.classList.add('hidden');
                }, 5000);
            }
        });
    }

    // Smooth scrolling for navigation links (anchor links)
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');
            
            // Solo actuar sobre enlaces internos a IDs (ej: #contacto)
            if (href && href.startsWith('#') && href.length > 1) {
                let targetElement = null;
                try {
                    // Intentar seleccionar el elemento por ID
                    targetElement = document.querySelector(href);
                } catch (error) {
                    // Si el selector es inválido (ej: href="#" o href="#~invalid~"), no hacer nada.
                    console.warn(`Selector inválido para smooth scroll: ${href}`);
                    return; 
                }

                if (targetElement) {
                    e.preventDefault(); // Prevenir el salto por defecto solo si el elemento existe
                    
                    // Calcular el offset por el header fijo si existe
                    const header = document.querySelector('nav.fixed'); // Ajusta el selector si es necesario
                    const headerOffset = header ? header.offsetHeight : 0;
                    const elementPosition = targetElement.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
            
                    window.scrollTo({
                        top: offsetPosition,
                        behavior: "smooth"
                    });
                    
                    // Close mobile menu if open and exists
                    if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                } else {
                    // Opcional: Si el enlace apunta a un ID que no existe en la página actual, 
                    // podrías dejar que el navegador lo maneje (podría ir a otra página si es un enlace completo)
                    // o mostrar un mensaje. Por ahora, simplemente no hacemos nada.
                    console.warn(`Elemento no encontrado para smooth scroll: ${href}`);
                }
            }
            // Si no es un enlace ancla (no empieza con # o es solo #), dejar que el navegador siga el enlace.
        });
    });

}); // Fin de DOMContentLoaded 