document.addEventListener('DOMContentLoaded', function() {
    // ... tu JS existente (menú, formularios) ...

    // Mobile menu toggle
    const menuBtn = document.getElementById('menu-btn');
    const mobileMenu = document.getElementById('mobile-menu');
    console.log("Script cargado. Elementos globales seleccionados."); // Log inicial
    if (menuBtn && mobileMenu) {
        menuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // Función reutilizable para manejar reCAPTCHA y envío
    function handleFormSubmit(formElement, recaptchaAction, recaptchaInputId) {
        // Comprobar si la acción apunta a un script PHP real
        const formAction = formElement.getAttribute('action');
        if (!formAction || !formAction.endsWith('.php')) {
            console.warn(`Formulario #${formElement.id} no tiene un action PHP válido. Cancelando envío real.`);
            // Aquí podrías mantener la simulación JS si quieres, pero la quitaremos por ahora
            // para centrarnos en la lógica real.
            return; // No hacer nada más si no es envío real
        }

        // Referencias a los divs de mensajes y botón dentro del formulario específico
        const successDiv = formElement.querySelector('.bg-green-900'); // Busca div éxito DENTRO del form
        const errorDiv = formElement.querySelector('.bg-red-900');     // Busca div error DENTRO del form
        const submitButton = formElement.querySelector('button[type="submit"]');
        const originalButtonText = submitButton ? submitButton.textContent : 'Enviar';

        // Ocultar mensajes previos y deshabilitar botón
        if (successDiv) successDiv.classList.add('hidden');
        if (errorDiv) errorDiv.classList.add('hidden');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'Enviando...'; // Feedback visual
        }

        grecaptcha.ready(function() {
            grecaptcha.execute('6LcJ_QsrAAAAAIPElRrOTj0xVm_iuntmM-kUOP4E', {action: recaptchaAction}).then(function(token) {
                const recaptchaInput = document.getElementById(recaptchaInputId);
                if (!recaptchaInput) {
                    console.error(`Campo oculto reCAPTCHA con ID ${recaptchaInputId} no encontrado.`);
                    if (errorDiv) {
                        errorDiv.textContent = 'Error interno del formulario (reCAPTCHA). Recarga la página.';
                        errorDiv.classList.remove('hidden');
                    }
                    if (submitButton) submitButton.disabled = false; // Reactivar botón
                    submitButton.textContent = originalButtonText;
                    return; // Detener si falta el input
                }
                recaptchaInput.value = token;
                console.log(`reCAPTCHA token para ${recaptchaAction} obtenido.`);

                // Crear FormData para enviar
                const formData = new FormData(formElement);

                // Enviar datos con Fetch API
                fetch(formAction, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json()) // Esperar respuesta JSON del PHP
                .then(data => {
                    console.log("Respuesta del servidor:", data);
                    if (data.status === 'success') {
                        if (successDiv) {
                            successDiv.textContent = data.message || '¡Mensaje enviado con éxito! En breve nos pondremos en contacto.'; // Usar mensaje del servidor o default
                            successDiv.classList.remove('hidden');
                        }
                        formElement.reset(); // Limpiar formulario en éxito
                        // Scroll al formulario tras éxito
                        setTimeout(() => smoothScrollTo(formElement.closest('section')), 100); // Aumentado a 100ms
                    } else {
                        // Mostrar error
                        if (errorDiv) {
                            errorDiv.textContent = data.message || 'Error desconocido al procesar el formulario.';
                            errorDiv.classList.remove('hidden');
                        }
                        // Scroll al formulario tras error
                        setTimeout(() => smoothScrollTo(formElement.closest('section')), 100); // Aumentado a 100ms
                    }
                })
                .catch(error => {
                    console.error('Error en fetch o procesando JSON:', error);
                    if (errorDiv) {
                        errorDiv.textContent = 'Error de conexión con el servidor. Inténtalo más tarde.';
                        errorDiv.classList.remove('hidden');
                    }
                })
                .finally(() => {
                    // Reactivar botón y restaurar texto, independientemente del resultado
                    if (submitButton) {
                         submitButton.disabled = false;
                         submitButton.textContent = originalButtonText;
                    }
                });

            }).catch(function(error) {
                console.error('Error obteniendo token reCAPTCHA:', error);
                if (errorDiv) {
                    errorDiv.textContent = 'Error de verificación reCAPTCHA. Refresca la página e inténtalo de nuevo.';
                    errorDiv.classList.remove('hidden');
                }
                 if (submitButton) { // Reactivar botón también si reCAPTCHA falla
                     submitButton.disabled = false;
                     submitButton.textContent = originalButtonText;
                 }
            });
        });
    }

    // --- Formularios (Listeners) ---
    const seoForm = document.getElementById('seo-calculator-form');
    if (seoForm) {
        seoForm.addEventListener('submit', function(e) {
            e.preventDefault(); // PREVENIR envío y recarga SIEMPRE
            console.log("Calculadora submit interceptado para AJAX.");
            handleFormSubmit(this, 'submit_calculadora', 'recaptchaResponse_calc');
        });
    }
    
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault(); // PREVENIR envío y recarga SIEMPRE
            console.log("Contacto submit interceptado para AJAX.");
            handleFormSubmit(this, 'submit_contacto', 'recaptchaResponse_contact');
        });
         
        // ELIMINAR toda la lógica anterior que leía parámetros de URL
        // Ya no es necesaria con AJAX
    }

    // --- Smooth scrolling ---

    function smoothScrollTo(targetElement) {
        if (!targetElement) {
             console.warn("smoothScrollTo: targetElement es null o undefined."); // Log si el objetivo no existe
             return;
        }
        console.log("smoothScrollTo: Intentando scroll hacia:", targetElement.id); // Log: A qué ID se intenta hacer scroll

        const header = document.querySelector('nav.fixed'); // Selecciona tu barra de navegación fija
        const headerOffset = header ? header.offsetHeight : 0; // Obtiene su altura
        // Calcula la posición correcta teniendo en cuenta el scroll actual y el offset del header
        const elementPosition = targetElement.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
        console.log(`smoothScrollTo: headerOffset=${headerOffset}, elementPosition=${elementPosition}, calculated offsetPosition=${offsetPosition}`); // Log: Valores calculados

        // Realiza el scroll suave
        window.scrollTo({
            top: offsetPosition,
            behavior: "smooth"
        });
        console.log("smoothScrollTo: window.scrollTo ejecutado."); // Log: Confirmación de ejecución

        // Cierra menú móvil si está abierto
        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
        }
    }

    // Listener general para TODOS los enlaces que empiezan con '#'
    // Esto incluye el menú principal, los botones de servicio, y cualquier otro enlace interno.
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    console.log(`Smooth Scroll: Encontrados ${anchorLinks.length} enlaces ancla.`); // Log: Cuántos enlaces se encontraron
    anchorLinks.forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const href = this.getAttribute('href');
            console.log(`Smooth Scroll: Clic detectado en enlace con href="${href}"`); // Log: Qué enlace se clickeó
            if (href && href.startsWith('#') && href.length > 1) {
                let targetElement = null;
                try {
                    // Usar CSS.escape para manejar IDs que podrían tener caracteres especiales, aunque no debería ser el caso aquí.
                    // targetElement = document.querySelector('#' + CSS.escape(href.substring(1)));
                    targetElement = document.querySelector(href); 
                } catch (error) {
                    console.error(`Smooth Scroll: Error seleccionando elemento para href="${href}"`, error); // Log: Error en selector
                    return; 
                }
                
                // Si el elemento existe en esta página...
                if (targetElement) {
                    console.log(`Smooth Scroll: Elemento objetivo encontrado:`, targetElement); // Log: Elemento encontrado
                    e.preventDefault(); // Previene el salto brusco por defecto del navegador
                    smoothScrollTo(targetElement); // Llama a la función de scroll suave
                } else {
                    // Si el ID no existe en la página, no hace nada y deja que el navegador lo maneje
                    // (útil si tuvieras enlaces a otras páginas que también usan '#')
                    console.warn(`Smooth Scroll: Elemento objetivo NO encontrado para href="${href}"`); // Log: Elemento no encontrado
                }
            }
            // Si no es un enlace ancla (ej: href="pagina.html"), el navegador lo seguirá normalmente.
        });
    });

     // --- Preselección del formulario de contacto desde botones de servicio ---
     const serviceButtons = document.querySelectorAll('.service-button'); // Usamos la clase añadida
     const contactSelect = document.getElementById('contact-service');

     if (contactSelect) { // Solo añadir listeners si el select existe
        serviceButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // No prevenir default aquí, dejar que el listener de scroll lo haga si es necesario
                const serviceValue = this.getAttribute('data-service');
                
                if (serviceValue) {
                    const optionExists = Array.from(contactSelect.options).some(option => option.value === serviceValue);
                    if (optionExists) {
                        contactSelect.value = serviceValue;
                        // console.log(`Servicio preseleccionado: ${serviceValue}`);
                    } else {
                        console.warn(`Opción con valor "${serviceValue}" no encontrada en el select.`);
                    }
                }
                // El scroll lo maneja el listener general 'a[href^="#"]'
            });
        });
    } else {
         // console.warn("Select 'contact-service' no encontrado, la preselección no funcionará.");
    }


    // --- Lógica del Modal de Cookies Mejorada ---
    const cookieModal = document.getElementById('cookie-consent-modal');
    const acceptButton = document.getElementById('accept-cookies-btn');
    const rejectButton = document.getElementById('reject-cookies-btn');
    const manageButton = document.getElementById('manage-cookies-btn');
    const managePanel = document.getElementById('cookie-manage-panel');
    const savePrefsButton = document.getElementById('save-cookie-prefs-btn');
    
    const consentCookieName = 'user_cookie_consent_prefs'; // Nombre de cookie único

    // Función para establecer la cookie local con preferencias
    function setConsentCookie(prefs) {
        const expiresDate = new Date();
        expiresDate.setFullYear(expiresDate.getFullYear() + 1); 
        const cookieString = `${consentCookieName}=${JSON.stringify(prefs)}; path=/; expires=${expiresDate.toUTCString()}; SameSite=Lax; Secure`; // Añadir Secure si usas HTTPS
        document.cookie = cookieString;
        console.log("Cookie local set:", prefs); 
    }

    // Función para obtener las preferencias de la cookie local
    function getCookiePrefs() {
        const cookieValue = document.cookie
            .split('; ')
            .find(row => row.startsWith(`${consentCookieName}=`))
            ?.split('=')[1];
        try {
            return cookieValue ? JSON.parse(decodeURIComponent(cookieValue)) : null; 
        } catch (e) {
             console.error("Error parsing cookie prefs:", e);
            return null; 
        }
    }

    // Inicializar estado de checkboxes del panel según la cookie
    function initManagePanel() {
        if (!managePanel) return;
        const currentPrefs = getCookiePrefs();
        const analyticsCheckbox = document.getElementById('cookies-analytics');
        const marketingCheckbox = document.getElementById('cookies-marketing');
        const functionalityCheckbox = document.getElementById('cookies-functionality'); // Asumiendo que lo añades
        const personalizationCheckbox = document.getElementById('cookies-personalization'); // Asumiendo que lo añades

        const necessaryCheckbox = document.getElementById('cookies-necessary');
        if(necessaryCheckbox) necessaryCheckbox.checked = true; 

        if (currentPrefs && currentPrefs.status) { 
             if (analyticsCheckbox) analyticsCheckbox.checked = currentPrefs.analytics;
             if (marketingCheckbox) marketingCheckbox.checked = currentPrefs.marketing;
             if (functionalityCheckbox) functionalityCheckbox.checked = currentPrefs.functionality !== undefined ? currentPrefs.functionality : false; // Valor por defecto false
             if (personalizationCheckbox) personalizationCheckbox.checked = currentPrefs.personalization !== undefined ? currentPrefs.personalization : false; // Valor por defecto false
        } else {
             // Valores por defecto si no hay cookie 
             if (analyticsCheckbox) analyticsCheckbox.checked = false; // Default denied
             if (marketingCheckbox) marketingCheckbox.checked = false; // Default denied
             if (functionalityCheckbox) functionalityCheckbox.checked = false; // Default denied
             if (personalizationCheckbox) personalizationCheckbox.checked = false; // Default denied
        }
    }


    // --- Event Listeners ---
    
    // Botón Aceptar Todo
    if (acceptButton && cookieModal) {
        acceptButton.addEventListener('click', function() {
            const prefs = {
                necessary: true,
                analytics: true,
                marketing: true,
                functionality: true, // Conceder si aceptas todo
                personalization: true, // Conceder si aceptas todo
                status: 'accepted'
            };
            setConsentCookie(prefs);
            cookieModal.classList.add('hidden');
            managePanel?.classList.add('hidden'); // Ocultar panel si estaba abierto

            // === Google Consent Mode v2 Update ===
            gtag('consent', 'update', {
                'analytics_storage': 'granted',
                'ad_storage': 'granted',
                'ad_user_data': 'granted',
                'ad_personalization': 'granted',
                'functionality_storage': 'granted',
                'personalization_storage': 'granted'
                // 'security_storage' generalmente no se modifica aquí
            });
            dataLayer.push({'event': 'consent_update_accepted_all'}); // Evento específico
            console.log("Consent Updated: Accepted All");
            // ------------------------------------
        });
    }

    // Botón Rechazar Todo
    if (rejectButton && cookieModal) {
        rejectButton.addEventListener('click', function() {
            const prefs = {
                necessary: true, // Necesarias no se pueden rechazar
                analytics: false,
                marketing: false,
                functionality: false,
                personalization: false,
                status: 'rejected'
            };
            setConsentCookie(prefs);
            cookieModal.classList.add('hidden');
            managePanel?.classList.add('hidden');

            // === Google Consent Mode v2 Update ===
            gtag('consent', 'update', {
                'analytics_storage': 'denied',
                'ad_storage': 'denied',
                'ad_user_data': 'denied',
                'ad_personalization': 'denied',
                'functionality_storage': 'denied',
                'personalization_storage': 'denied'
            });
             dataLayer.push({'event': 'consent_update_rejected_all'}); // Evento específico
            console.log("Consent Updated: Rejected All");
            // ------------------------------------
        });
    }

    // Botón Gestionar Cookies (solo muestra/oculta el panel)
    if (manageButton && managePanel) {
        manageButton.addEventListener('click', function() {
            managePanel.classList.toggle('hidden');
            if (!managePanel.classList.contains('hidden')) {
                initManagePanel(); // Asegura que los checkboxes reflejen el estado actual al abrir
            }
        });
    }

    // Botón Guardar Preferencias
    if (savePrefsButton && managePanel && cookieModal) {
        savePrefsButton.addEventListener('click', function() {
            const analyticsCheckbox = document.getElementById('cookies-analytics');
            const marketingCheckbox = document.getElementById('cookies-marketing');
            const functionalityCheckbox = document.getElementById('cookies-functionality'); // Si existe
            const personalizationCheckbox = document.getElementById('cookies-personalization'); // Si existe

            const prefs = {
                necessary: true,
                analytics: analyticsCheckbox ? analyticsCheckbox.checked : false,
                marketing: marketingCheckbox ? marketingCheckbox.checked : false,
                functionality: functionalityCheckbox ? functionalityCheckbox.checked : false,
                personalization: personalizationCheckbox ? personalizationCheckbox.checked : false,
                status: 'managed'
            };
            setConsentCookie(prefs);
            cookieModal.classList.add('hidden');
            managePanel.classList.add('hidden');

            // === Google Consent Mode v2 Update ===
            const consentUpdate = {
                'analytics_storage': prefs.analytics ? 'granted' : 'denied',
                // 'ad_storage' suele ir ligado a 'marketing'
                'ad_storage': prefs.marketing ? 'granted' : 'denied', 
                // 'ad_user_data' y 'ad_personalization' también suelen ir ligados a 'marketing'
                'ad_user_data': prefs.marketing ? 'granted' : 'denied',
                'ad_personalization': prefs.marketing ? 'granted' : 'denied',
                'functionality_storage': prefs.functionality ? 'granted' : 'denied',
                'personalization_storage': prefs.personalization ? 'granted' : 'denied'
            };
            gtag('consent', 'update', consentUpdate);
            dataLayer.push({'event': 'consent_update_managed'}); // Evento específico
            console.log("Consent Updated: Managed Preferences", consentUpdate);
            // ------------------------------------
        });
    }

    // Mostrar modal si no hay cookie o si el status no está definido
    const currentConsent = getCookiePrefs();
    if (cookieModal && (!currentConsent || !currentConsent.status)) {
        cookieModal.classList.remove('hidden');
        // console.log("Mostrando modal de cookies (no hay preferencias guardadas).");
    } else if (cookieModal) {
         // console.log("Modal de cookies no mostrado (preferencias ya guardadas):", currentConsent);
    }

    initManagePanel(); // Llama para inicializar los checkboxes la primera vez

    // --- Fin Lógica del Modal de Cookies ---

}); // Fin de DOMContentLoaded 