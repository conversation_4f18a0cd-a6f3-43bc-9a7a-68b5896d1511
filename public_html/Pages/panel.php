<?php
// Iniciar sesión OBLIGATORIO al principio de CADA página protegida
session_start();


// --- Lógica para Mensaje de Bienvenida Post-Checkout (Añadido) ---
$showWelcomeMessage = false;
if (isset($_GET['checkout']) && $_GET['checkout'] === 'success') {
    // Establecer una variable de sesión para mostrar el mensaje
    $_SESSION['show_checkout_success'] = true;
    // Redirigir a la misma página SIN el parámetro para limpiar la URL
    header('Location: /panel'); // Asegúrate que /panel es la URL correcta de esta página
    exit;
}

// Comprobar si la variable de sesión existe y luego eliminarla
if (isset($_SESSION['show_checkout_success']) && $_SESSION['show_checkout_success'] === true) {
    $showWelcomeMessage = true;
    unset($_SESSION['show_checkout_success']); // Eliminar para que no se muestre de nuevo
}
// --- Fin Lógica Mensaje Bienvenida ---

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_email'])) {
    // Si no está logueado, redirigir a la página de login
    header('Location: login.php?error=' . urlencode('Debes iniciar sesión para acceder al panel.'));
    exit;
}

// --- Si llegó hasta aquí, está logueado --- 

// --- Incluir configuración y conexión a BD --- 
require_once __DIR__ . '/../../includes/config.php'; 
require_once __DIR__ . '/../../includes/db.php'; 

$pageTitle = 'Panel de usuario | Pietra Mundo Digital';
$pageDescription = 'Gestiona tu cuenta, suscripción y uso de herramientas.';
$noIndex = true; // No indexar el panel
require __DIR__ . '/../_partials/header.php';

// --- Obtener datos del usuario desde la sesión y BD --- 
$userEmail = htmlspecialchars($_SESSION['user_email']);
$userId = $_SESSION['user_id']; 

// Variables para los datos de la BD
$userPlan = 'Desconocido';
$subscriptionStatus = 'Desconocido';
$usageCount = 0;
$usageLimit = null; // Puede ser null para ilimitado o un número
$currentPeriodEnd = null;
$dbError = false;

$db = getDbConnection();
if ($db) {
    try {
        $stmt = $db->prepare("SELECT plan, subscription_status, usage_count, usage_limit, current_period_end FROM users WHERE id = :id");
        $stmt->execute([':id' => $userId]);
        $userDataFromDb = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($userDataFromDb) {
            $userPlan = htmlspecialchars($userDataFromDb['plan']);
            $subscriptionStatus = htmlspecialchars($userDataFromDb['subscription_status']);
            $usageCount = (int)$userDataFromDb['usage_count'];
            // El límite puede ser null en la BD si lo definimos así para ilimitado
            $usageLimit = $userDataFromDb['usage_limit'] === null ? null : (int)$userDataFromDb['usage_limit'];
            $currentPeriodEnd = $userDataFromDb['current_period_end']; // Lo formatearemos después si es necesario
        } else {
            // Usuario de sesión no encontrado en BD, algo raro
            error_log("Error Panel: Usuario ID {$userId} de sesión no encontrado en BD.");
            $dbError = true;
        }
    } catch (PDOException $e) {
        error_log("Error BD en panel.php para User ID {$userId}: " . $e->getMessage());
        $dbError = true;
    }
} else {
    error_log("Error Panel: No se pudo conectar a la BD.");
    $dbError = true;
}

// Formatear la fecha de fin de periodo si existe
$formattedPeriodEnd = null; // Cambiado default a null
if ($currentPeriodEnd) {
    try {
        $date = new DateTime($currentPeriodEnd);
        $formattedPeriodEnd = $date->format('d/m/Y'); // Formato día/mes/año
    } catch (Exception $e) { 
        $formattedPeriodEnd = 'Fecha inválida';
    }
}

// Mapeo de traducción de estados (puedes añadir más si los usas)
$statusTranslations = [
    'active' => 'Activo',
    'trialing' => 'En prueba',
    'past_due' => 'Pago pendiente',
    'canceled' => 'Cancelado',
    'unpaid' => 'Impagado',
    // Añade otros estados si son relevantes
];
$translatedStatus = $statusTranslations[strtolower($subscriptionStatus)] ?? ucfirst($subscriptionStatus); // Fallback al original capitalizado

// Formatear el límite de uso para mostrar
if ($userPlan === 'free') {
    $limitDisplay = defined('FREE_PLAN_LIMIT') ? FREE_PLAN_LIMIT : 0; // Usar constante, fallback a 0 si no está definida
} else {
    $limitDisplay = $usageLimit === null ? 'Ilimitado' : $usageLimit;
}

?>

<!-- Contenedor principal con Flexbox para Sidebar + Contenido -->
<div class="flex flex-col md:flex-row min-h-screen pt-16"> <?php // pt-16 para compensar nav fijo, ajustar si es necesario ?>

    <!-- Sidebar (visible en md y superior, oculto en móvil) -->
    <?php require __DIR__ . '/../_partials/panel_sidebar.php'; // Incluir el parcial ?>

    <!-- Contenido Principal -->
    <main class="flex-grow p-6 md:p-10 bg-darker text-white"> <?php // Cambiado fondo a darker, ajustar si es necesario ?>
        
        <!-- Botón para mostrar Sidebar en Móvil (FUNCIONALIDAD FUTURA) -->
        <!-- 
        <button id="mobile-menu-button" class="md:hidden mb-4 text-white p-2 bg-gray-700 rounded">
             Menú
        </button> 
        -->
        
        <h1 class="text-3xl font-bold mb-6">Resumen de la cuenta</h1>
        
        <p class="mb-6 text-lg">Bienvenido de nuevo, <strong class="font-semibold"><?= $userEmail ?></strong>.</p>

        <?php // --- Mostrar Mensaje de Bienvenida si aplica (Añadido) --- 
        if ($showWelcomeMessage): ?>
        <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">¡Suscripción activada!</strong>
            <span class="block sm:inline">Gracias por suscribirte. Tu plan ha sido actualizado.</span>
        </div>
        <?php endif; 
        // --- Fin Mensaje Bienvenida ---
        ?>

        <?php if ($dbError): ?>
        <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Error:</strong>
            <span class="block sm:inline">No se pudo cargar la información completa de tu cuenta. Inténtalo más tarde o contacta con soporte.</span>
        </div>
        <?php endif; ?>

        <!-- Sección de Información General (Ahora con datos reales) -->
        <section class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-100">Información de tu cuenta</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3 text-gray-300"> <?php // Ajustado gap ?>
                <div>
                    <strong>Email:</strong> <?= $userEmail ?>
                </div>
                <div>
                    <strong>Plan Actual:</strong> 
                    <a href="/gsc-inspector#planes-precios" class="font-medium text-primary capitalize hover:underline"><?= $userPlan ?></a> <?php // Enlace añadido ?>
                </div>
                <div>
                    <strong>Estado Suscripción:</strong> 
                    <?php 
                    // Colores según estado ... (código existente)
                    $statusColor = 'text-gray-400'; // ...
                    if (strtolower($subscriptionStatus) === 'active' || strtolower($subscriptionStatus) === 'trialing') {
                        $statusColor = 'text-green-400';
                    } elseif (strtolower($subscriptionStatus) === 'past_due' || strtolower($subscriptionStatus) === 'unpaid') {
                        $statusColor = 'text-yellow-400';
                    } elseif (strtolower($subscriptionStatus) === 'canceled') {
                         $statusColor = 'text-red-400';
                    }
                    ?>
                    <span class="<?= $statusColor ?> font-medium"><?= $translatedStatus ?></span> <?php // Usar traducido ?>
                </div>
                 <div>
                     <strong>Uso GSC Inspector (este mes):</strong> 
                     <span><?= $usageCount ?> / <?= $limitDisplay ?></span>
                 </div>
                 <?php if ($formattedPeriodEnd && (strtolower($subscriptionStatus) === 'active' || strtolower($subscriptionStatus) === 'trialing')): ?>
                 <div class="md:col-span-2"> <?php // Ocupa ambas columnas en md si hay fecha ?>
                     <strong>Periodo actual hasta:</strong> <?= $formattedPeriodEnd ?>
                 </div>
                <?php endif; ?>
            </div>
        </section>

        <!-- Sección Gestionar Cuenta (Eliminada, ahora en sidebar o portal) -->
        
        <!-- Nueva Sección: Planes Disponibles (Replicado de gsc-inspector.php) -->
        <section id="planes-disponibles" class="mb-8">
            <h2 class="text-2xl font-bold text-center mb-10">Gestionar Plan</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                
                <!-- Plan Free -->
                <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                    <h3 class="text-xl font-semibold mb-2 text-gray-100">Free</h3>
                    <p class="text-gray-400 mb-4">Ideal para empezar</p>
                    <p class="text-3xl font-bold mb-4 text-gray-100">Gratis</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> <?= defined('FREE_PLAN_LIMIT') ? FREE_PLAN_LIMIT : 30 ?> Inspecciones/mes</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Datos básicos</li>
                        </ul>
                    <?php if (strtolower($userPlan) === 'free'): ?>
                         <button class="mt-auto w-full bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-not-allowed" disabled>Plan Actual</button>
                    <?php else: ?>
                         <button class="mt-auto w-full bg-gray-500 text-white px-6 py-2 rounded-lg font-semibold hover:bg-gray-600 transition duration-300" disabled title="Downgrade no disponible directamente">Seleccionar Free</button> <?php // O manejar downgrade si se implementa ?>
                    <?php endif; ?>
                </div>

                <!-- Plan Personal -->
                <div class="bg-gray-800 p-6 rounded-lg shadow-lg border <?= (strtolower($userPlan) === 'personal') ? 'border-primary ring-2 ring-primary' : 'border-gray-700' ?> flex flex-col relative overflow-hidden">
                    <?php if (strtolower($userPlan) !== 'personal'): // Solo mostrar si no es el plan actual ?>
                    <span class="absolute top-0 right-0 w-32 h-8 bg-primary flex items-center justify-center text-white text-xs font-bold transform rotate-45 translate-x-8 translate-y-4 border-2 border-white">Más Popular</span>
                    <?php endif; ?>
                    <h3 class="text-xl font-bold text-accent mb-2">Personal</h3>
                    <p class="text-3xl font-bold text-white mb-1">9,99 €<span class="text-lg font-normal text-gray-500">/mes</span></p>
                    <p class="text-xs text-gray-500 mb-4">IVA no incluido</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 120 Inspecciones/mes</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Datos completos</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Comparación de fechas</li>
                    </ul>
                    <?php if (strtolower($userPlan) === 'personal'): ?>
                        <button class="mt-auto w-full bg-accent text-black px-6 py-2 rounded-lg text-lg font-semibold cursor-not-allowed" disabled>Plan Actual</button>
                    <?php else: ?>
                        <button data-price-id="<?= defined('STRIPE_PRICE_ID_PERSONAL') ? STRIPE_PRICE_ID_PERSONAL : '' ?>" class="stripe-checkout-button mt-auto bg-accent text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-accent-dark transition duration-300">Elegir Plan</button>
                    <?php endif; ?>
                </div>

                <!-- Plan Plus -->
                 <div class="bg-gray-800 p-6 rounded-lg shadow-lg border <?= (strtolower($userPlan) === 'plus') ? 'border-yellow-500 ring-2 ring-yellow-500' : 'border-gray-700' ?> flex flex-col">
                    <h3 class="text-xl font-bold text-yellow-500 mb-2">Plus</h3>
                    <p class="text-3xl font-bold text-white mb-1">19,99 €<span class="text-lg font-normal text-gray-500">/mes</span></p>
                    <p class="text-xs text-gray-500 mb-4">IVA no incluido</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 360 Inspecciones/mes</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Todo en Personal</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Exportación datos (Próx.)</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Soporte prioritario</li>
                    </ul>
                    <?php if (strtolower($userPlan) === 'plus'): ?>
                        <button class="mt-auto w-full bg-yellow-500 text-black px-6 py-2 rounded-lg text-lg font-semibold cursor-not-allowed" disabled>Plan Actual</button>
                    <?php else: ?>
                        <button data-price-id="<?= defined('STRIPE_PRICE_ID_PLUS') ? STRIPE_PRICE_ID_PLUS : '' ?>" class="stripe-checkout-button mt-auto bg-yellow-500 text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-yellow-600 transition duration-300">Elegir Plan</button>
                    <?php endif; ?>
                </div>

                 <!-- Plan Agency -->
                 <div class="bg-gray-800 p-6 rounded-lg shadow-lg border <?= (strtolower($userPlan) === 'agency') ? 'border-blue-400 ring-2 ring-blue-400' : 'border-gray-700' ?> flex flex-col">
                    <h3 class="text-xl font-bold text-blue-400 mb-2">Agency</h3>
                    <p class="text-3xl font-bold text-white mb-1">49,99 €<span class="text-lg font-normal text-gray-500">/mes</span></p>
                    <p class="text-xs text-gray-500 mb-4">IVA no incluido</p>
                    <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 1200 Inspecciones/mes</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Todo en Plus</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Acceso API (Próx.)</li>
                        <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Múltiples usuarios (Próx.)</li>
                    </ul>
                     <?php if (strtolower($userPlan) === 'agency'): ?>
                         <button class="mt-auto w-full bg-black text-white px-6 py-2 rounded-lg text-lg font-semibold cursor-not-allowed border border-gray-600" disabled>Plan Actual</button>
                     <?php else: ?>
                         <button data-price-id="<?= defined('STRIPE_PRICE_ID_AGENCY') ? STRIPE_PRICE_ID_AGENCY : '' ?>" class="stripe-checkout-button mt-auto bg-black text-white px-6 py-2 rounded-lg text-lg font-semibold hover:bg-gray-900 transition duration-300 border border-gray-600">Elegir Plan</button>
                     <?php endif; ?>
                 </div>
             </div>
             <p class="text-center mt-8 text-gray-400">Los cambios de plan se aplicarán al inicio del próximo ciclo de facturación.</p>
         </section>

    </main>
</div>

<?php 
// Añadir script de Stripe Checkout si no estaba ya en el footer global
// Asegúrate de que este script se carga SOLO UNA VEZ por página.
// Si ya está en footer.php, NO es necesario añadirlo aquí.
// Por precaución, lo añadimos aquí condicionalmente (mejor si está en footer)
if (isset($_SESSION['user_id'])) : 
?>
<script>
document.addEventListener('DOMContentLoaded', () => {
    const checkoutButtons = document.querySelectorAll('.stripe-checkout-button');

    checkoutButtons.forEach(button => {
        button.addEventListener('click', async (event) => {
            const priceId = button.getAttribute('data-price-id');
            if (!priceId) {
                console.error('Stripe Price ID no encontrado en el botón.');
                alert('Error: No se pudo obtener el ID del plan. Contacta con soporte.');
                return;
            }
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = 'Procesando...';

            try {
                const response = await fetch('/api/create_checkout_session.php', { 
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ priceId: priceId })
                });
                const restoreButton = () => { button.disabled = false; button.textContent = originalText; };

                if (!response.ok) {
                    if (response.status === 401) { // Asumiendo 401 para no autenticado
                         alert('Tu sesión ha caducado o no estás autenticado. Inicia sesión de nuevo.');
                         window.location.href = '/login?redirect=/panel'; // Redirigir
                         throw new Error('Usuario no autenticado');
                    }
                    const errorData = await response.json().catch(() => ({ error: `Error del servidor (${response.status})` }));
                    alert(`Error al iniciar el pago: ${errorData.error || response.statusText}`);
                    restoreButton();
                    throw new Error(`Error del servidor: ${response.status}`);
                }

                const session = await response.json();
                if (session.url) {
                    window.location.href = session.url;
                } else {
                     console.error('No se recibió URL de Stripe.');
                     alert('Error: No se pudo redirigir a la página de pago.');
                     restoreButton();
                }
            } catch (error) {
                console.error('Error al crear la sesión de Stripe Checkout:', error);
                if (error.message !== 'Usuario no autenticado' && !error.message.startsWith('Error del servidor')) {
                    alert('Ocurrió un error inesperado.');
                   button.disabled = false;
                   button.textContent = originalText;
                }
            }
        });
    });
});
</script>
<?php 
endif;
require __DIR__ . '/../_partials/footer.php';
?>