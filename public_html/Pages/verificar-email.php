<?php
// /Pages/verificar-email.php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../includes/config.php'; 
require_once __DIR__ . '/../includes/db.php'; 

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$token = $_GET['token'] ?? null;
$message = '';
$message_type = 'error'; // 'error' o 'success'

if (empty($token)) {
    $message = 'Token de verificación inválido o faltante.';
} else {
    $db = getDbConnection();
    if (!$db) {
        $message = 'Error interno del servidor (BD).';
    } else {
        try {
            // Buscar el token
            $stmt = $db->prepare("SELECT id, email, email_verify_expires_at FROM users WHERE email_verify_token = :token AND email_verified_at IS NULL");
            $stmt->execute([':token' => $token]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                $message = 'Token de verificación inválido, expirado o el email ya ha sido verificado.';
            } else {
                // Verificar si el token ha expirado
                $now = new DateTime();
                $expires = new DateTime($user['email_verify_expires_at']);

                if ($now > $expires) {
                    $message = 'El token de verificación ha expirado. Por favor, regístrate de nuevo o solicita reenviar el email.';
                    // Opcional: Podrías implementar lógica para reenviar aquí
                } else {
                    // ¡Token válido! Marcar como verificado y limpiar token
                    $updateStmt = $db->prepare("UPDATE users SET email_verified_at = NOW(), email_verify_token = NULL, email_verify_expires_at = NULL WHERE id = :id");
                    if ($updateStmt->execute([':id' => $user['id']])) {
                        $message = '¡Tu dirección de email (' . htmlspecialchars($user['email']) . ') ha sido verificada con éxito! Ya puedes iniciar sesión.';
                        $message_type = 'success';
                        
                        // Opcional: Iniciar sesión automáticamente al verificar
                        // session_regenerate_id(true);
                        // $_SESSION['user_id'] = $user['id'];
                        // $_SESSION['user_email'] = $user['email'];
                        // header('Location: /panel?success=' . urlencode('Email verificado. Bienvenido!'));
                        // exit;

                    } else {
                        $message = 'Error al actualizar el estado de verificación.';
                    }
                }
            }
             $stmt->closeCursor();

        } catch (PDOException $e) {
            error_log("Error de BD en verificar-email: " . $e->getMessage());
            $message = 'Error interno del servidor (BD) al verificar el token.';
        } catch (Exception $e) {
             error_log("Error general en verificar-email: " . $e->getMessage());
             $message = 'Ocurrió un error inesperado.';
        }
    }
}

// Mostrar resultado al usuario
$pageTitle = 'Verificación de Email | Pietra Mundo Digital';
$pageDescription = 'Resultado de la verificación de tu dirección de email.';
$noIndex = true;
require __DIR__ . '/../_partials/header.php'; 

?>
<main class="container mx-auto px-4 py-16 pt-24 flex justify-center items-center">
    <div class="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-700 text-center">
        <h1 class="text-2xl font-bold mb-6 text-gray-100">Verificación de Email</h1>
        
        <?php if ($message_type === 'success'): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                <span class="block sm:inline"><?= htmlspecialchars($message) ?></span>
            </div>
             <a href="/login" class="inline-block bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition duration-300">
                 Ir a Iniciar Sesión
             </a>
        <?php else: ?>
             <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
                 <span class="block sm:inline"><?= htmlspecialchars($message) ?></span>
             </div>
              <a href="/registro" class="inline-block bg-secondary text-white px-6 py-2 rounded-lg hover:bg-secondary-dark transition duration-300">
                 Volver a Registro
             </a>
        <?php endif; ?>
        
    </div>
</main>
<?php
require __DIR__ . '/../_partials/footer.php'; 
?> 