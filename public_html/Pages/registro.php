<?php
session_start();

// Si el usuario ya está logueado, redirigir al panel
if (isset($_SESSION['user_id'])) {
    header('Location: /panel');
    exit;
}

$pageTitle = 'Registro de nuevo usuario | Pietra Mundo Digital';
$pageDescription = 'Crea tu cuenta en GSC Inspector para empezar a usar la herramienta.';
$noIndex = true; // No indexar la página de registro
require __DIR__ . '/../_partials/header.php';

?>

<main class="container mx-auto px-4 py-16 pt-24 flex justify-center items-center">
    <div class="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-700">
        <h1 class="text-3xl font-bold mb-6 text-center text-gray-100">Crear nueva cuenta</h1>
        
        <!-- Mensajes de error/éxito -->
        <div id="message-area" class="mb-6"></div> 

        <form id="register-form" action="/api/auth/register.php" method="POST" class="space-y-6">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-300 mb-1">Nombre (Opcional)</label>
                <input type="text" name="name" id="name" autocomplete="name"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2"
                    placeholder="Tu nombre">
            </div>
            <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email <span class="text-red-500">*</span></label>
                <input type="email" name="email" id="email" required autocomplete="email"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2"
                    placeholder="<EMAIL>">
            </div>
            <div>
                <label for="password" class="block text-sm font-medium text-gray-300 mb-1">Contraseña <span class="text-red-500">*</span></label>
                <input type="password" name="password" id="password" required autocomplete="new-password"
                       pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*\W).{8,}" 
                       title="Mínimo 8 caracteres, incluyendo mayúscula, minúscula, número y símbolo."
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2">
                <p class="text-xs text-gray-500 mt-1">Mínimo 8 caracteres, con mayúscula, minúscula, número y símbolo.</p>
            </div>
            <div>
                <label for="confirm_password" class="block text-sm font-medium text-gray-300 mb-1">Confirmar contraseña <span class="text-red-500">*</span></label>
                <input type="password" name="confirm_password" id="confirm_password" required autocomplete="new-password"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2">
                <p id="password-match-error" class="text-xs text-red-500 mt-1 hidden">Las contraseñas no coinciden.</p>
            </div>
            
            <!-- Aceptación de Términos (Ejemplo - Ajustar a tus necesidades) -->
            <div class="flex items-center">
                 <input id="terms" name="terms" type="checkbox" required class="h-4 w-4 text-primary focus:ring-primary border-gray-500 rounded bg-gray-700">
                 <label for="terms" class="ml-2 block text-sm text-gray-400">He leído y acepto los <a href="/terminos-servicio" target="_blank" class="text-primary hover:underline">Términos de Servicio</a> y la <a href="/politica-privacidad" target="_blank" class="text-primary hover:underline">Política de Privacidad</a> <span class="text-red-500">*</span></label>
            </div>

            <div>
                <button type="submit" 
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-black bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition duration-300">
                    Crear Cuenta
                </button>
            </div>
        </form>

        <div class="relative flex py-5 items-center">
            <div class="flex-grow border-t border-gray-600"></div>
            <span class="flex-shrink mx-4 text-gray-500 text-sm">O regístrate con</span>
            <div class="flex-grow border-t border-gray-600"></div>
        </div>

        <!-- Botón de Google Sign-In (Opcional en registro, pero puede simplificar) -->
         <div class="flex justify-center">
             <div id="g_id_onload" data-client_id="<?= $webClientIdHeader ?>" data-context="signup" data-ux_mode="popup" data-callback="handleGoogleSignIn" data-auto_prompt="false"></div>
             <div class="g_id_signin" data-type="standard" data-shape="rectangular" data-theme="filled_black" data-text="signup_with" data-size="large" data-logo_alignment="left"></div>
         </div>
         
         <div class="mt-8 text-center">
            <p class="text-sm text-gray-400">
                ¿Ya tienes cuenta? 
                <a href="/login" class="font-medium text-primary hover:underline">
                    Inicia sesión
                </a>
            </p>
        </div>

    </div>
</main>

<script>
    // Validación de contraseñas coincidentes en frontend
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const errorMsg = document.getElementById('password-match-error');
    const registerForm = document.getElementById('register-form');

    function validatePasswords() {
        if (passwordInput.value !== confirmPasswordInput.value && confirmPasswordInput.value !== '') {
            errorMsg.classList.remove('hidden');
            confirmPasswordInput.setCustomValidity('Las contraseñas no coinciden');
        } else {
            errorMsg.classList.add('hidden');
            confirmPasswordInput.setCustomValidity('');
        }
    }

    passwordInput.addEventListener('change', validatePasswords);
    confirmPasswordInput.addEventListener('keyup', validatePasswords);

    // Manejar el envío del formulario (opcional, para mejor UX con fetch)
    // registerForm.addEventListener('submit', function(event) {
    //     event.preventDefault(); // Prevenir envío normal
    //     if (passwordInput.value !== confirmPasswordInput.value) {
    //         validatePasswords();
    //         return; // Detener si no coinciden
    //     }
    //     // Aquí podríamos hacer fetch a /api/auth/register.php
    //     // y manejar la respuesta con JS mostrando mensajes en #message-area
    //     // Por ahora, dejamos el envío normal del form
    // });

    // Función para manejar el registro con Google (similar a login.php)
    function handleGoogleSignIn(response) {
        console.log("Google Sign-Up Response:", response);
        const idToken = response.credential;
        const messageArea = document.getElementById('message-area');
        messageArea.innerHTML = '<div class="text-center text-gray-600">Verificando con Google...</div>';

        fetch('/api/auth/verify_google_web.php', { // Reutilizamos el endpoint de verificación
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({ credential: idToken })
        })
        .then(res => res.ok ? res.json() : res.json().then(err => Promise.reject(err)))
        .then(data => {
            if (data.success) {
                window.location.href = '/panel'; // Redirigir al panel
            } else {
                messageArea.innerHTML = `<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">${data.error || 'Error desconocido al registrar con Google.'}</div>`;
            }
        })
        .catch(error => {
            console.error('Error en fetch (Google Sign-Up):', error);
            messageArea.innerHTML = `<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">${error.error || 'No se pudo conectar con el servidor.'}</div>`;
        });
    }

</script>

<?php 
require __DIR__ . '/../_partials/footer.php';
?> 