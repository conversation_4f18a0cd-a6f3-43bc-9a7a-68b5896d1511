<?php
session_start();

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_id'])) {
    // Adjust redirect based on language detection (might be needed if header isn't loaded yet)
    $langPrefix = (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/en/') === 0) ? '/en' : '';
    header('Location: ' . $langPrefix . '/login?error=' . urlencode('Debes iniciar sesión para ver tu perfil.')); // Needs translation
    exit;
}

// --- Incluir configuración y conexión a BD --- 
require_once __DIR__ . '/../../../includes/config.php'; 
require_once __DIR__ . '/../../../includes/db.php'; 

// --- Include Header which sets $currentLang --- START
// Make sure header path is correct
require __DIR__ . '/../../_partials/header.php'; 
// --- Include Header --- END

// --- Language specific messages --- START ---
$msg_must_login = ($currentLang === 'en' ? 'You must log in to view your profile.' : 'Debes iniciar sesión para ver tu perfil.');
$msg_profile_data_error = ($currentLang === 'en' ? 'Error loading profile data.' : 'Error al cargar los datos del perfil.');
$msg_db_conn_error = ($currentLang === 'en' ? 'Internal server error (DB conn).' : 'Error interno del servidor (DB conn).');
$msg_name_empty_error = ($currentLang === 'en' ? 'Name cannot be empty.' : 'El nombre no puede estar vacío.');
$msg_update_name_db_error = ($currentLang === 'en' ? 'Error updating name in the database.' : 'Error al actualizar el nombre en la base de datos.');
$msg_update_db_conn_error = ($currentLang === 'en' ? 'Error connecting to the database to update.' : 'Error al conectar con la base de datos para actualizar.');
$msg_update_success = ($currentLang === 'en' ? 'Name updated successfully.' : 'Nombre actualizado correctamente.');
// --- Language specific messages --- END ---


// Correct redirect after login check using the already set $currentLang
if (!isset($_SESSION['user_id'])) {
    $loginUrl = ($currentLang === 'en' ? '/en/login' : '/login');
    header('Location: ' . $loginUrl . '?error=' . urlencode($msg_must_login));
    exit;
}


$userId = $_SESSION['user_id'];
$successMessage = null;
$errorMessage = null;
$currentName = '';
$currentEmail = $_SESSION['user_email'] ?? ($currentLang === 'en' ? 'Email not available' : 'Email no disponible'); // Email de la sesión

// --- Obtener datos actuales del usuario --- 
$db = getDbConnection();
if ($db) {
    try {
        $stmt = $db->prepare("SELECT name, email FROM users WHERE id = :id"); // Obtener nombre y reconfirmar email
        $stmt->execute([':id' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user) {
            $currentName = $user['name'] ?? '';
            $currentEmail = $user['email']; // Usar email de BD por si acaso
        }
    } catch (PDOException $e) {
        $errorMessage = $msg_profile_data_error; // Use translated message
        error_log("Error BD obteniendo datos perfil para User ID {$userId}: " . $e->getMessage());
    }
} else {
    $errorMessage = $msg_db_conn_error; // Use translated message
    error_log("Error conexión BD en perfil.php");
}

// --- Procesar el formulario si se envió --- 
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $newName = trim($_POST['name'] ?? '');

    // Validación básica (solo permitir nombre no vacío)
    if (empty($newName)) {
        $errorMessage = $msg_name_empty_error; // Use translated message
    } else {
        // Intentar actualizar el nombre
        if ($db) { // Reutilizar la conexión
            try {
                $updateStmt = $db->prepare("UPDATE users SET name = :name WHERE id = :id");
                if ($updateStmt->execute([':name' => $newName, ':id' => $userId])) {
                    $successMessage = $msg_update_success; // Use translated message
                    $currentName = $newName; // Actualizar el nombre mostrado en la página
                } else {
                    $errorMessage = $msg_update_name_db_error; // Use translated message
                    error_log("Error DB actualizando nombre para User ID {$userId}");
                }
            } catch (PDOException $e) {
                $errorMessage = $msg_update_db_conn_error; // Use translated message
                error_log("Error BD actualizando nombre para User ID {$userId}: " . $e->getMessage());
            }
        } else {
            $errorMessage = $msg_db_conn_error; // Use translated message
        }
    }
}

// --- Preparar datos para la vista (Header is already included) --- 
// $pageTitle, $pageDescription, $noIndex will be set by header based on $currentLang if logic exists there,
// or we can override them here if needed.
// $pageTitle = ($currentLang === 'en' ? 'Profile | User Dashboard' : 'Perfil | Panel de usuario');
// $pageDescription = ($currentLang === 'en' ? 'Manage your profile information.' : 'Gestiona tu información de perfil.');
// $noIndex = true; // No indexar esta página
// Since header.php is now included earlier, these might already be handled.

?>

<!-- Reutilizar la estructura del panel: Sidebar + Contenido -->
<div class="flex flex-col md:flex-row min-h-screen pt-16">

    <!-- Sidebar -->
    <?php require __DIR__ . '/../../_partials/panel_sidebar.php'; // Path to sidebar from Pages/panel/ ?>

    <!-- Contenido Principal -->
    <main class="flex-grow p-6 md:p-10 bg-darker text-white">
        <h1 class="text-3xl font-bold mb-6"><?php echo ($currentLang === 'en' ? 'Profile' : 'Perfil'); ?></h1>

        <?php if ($successMessage): ?>
            <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold"><?php echo ($currentLang === 'en' ? 'Success!' : '¡Éxito!'); ?></strong>
                <span class="block sm:inline"><?= htmlspecialchars($successMessage) ?></span>
            </div>
        <?php endif; ?>
        <?php if ($errorMessage && !$successMessage): ?>
             <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
                 <strong class="font-bold"><?php echo ($currentLang === 'en' ? 'Error:' : 'Error:'); ?></strong>
                 <span class="block sm:inline"><?= htmlspecialchars($errorMessage) ?></span>
             </div>
        <?php endif; ?>

        <div class="max-w-lg bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700">
            <form action="/panel/perfil" method="POST" class="space-y-4"> <?php // Action remains the same ?>
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-400 mb-1"><?php echo ($currentLang === 'en' ? 'Email' : 'Email'); ?></label>
                    <input type="email" name="email" id="email" value="<?= htmlspecialchars($currentEmail) ?>" readonly disabled
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-400 cursor-not-allowed">
                    <p class="text-xs text-gray-500 mt-1"><?php echo ($currentLang === 'en' ? 'Email cannot be changed.' : 'El email no se puede cambiar.'); ?></p>
                </div>
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-300 mb-1"><?php echo ($currentLang === 'en' ? 'Name' : 'Nombre'); ?></label>
                    <input type="text" name="name" id="name" value="<?= htmlspecialchars($currentName) ?>" required 
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
                
                <div>
                    <button type="submit" class="w-full md:w-auto bg-accent text-black px-6 py-2 rounded-lg font-bold hover:bg-accent-dark transition duration-300 shadow-md">
                        <?php echo ($currentLang === 'en' ? 'Save Changes' : 'Guardar Cambios'); ?>
                    </button>
                </div>
            </form>
        </div>

    </main>
</div>

<?php 
require __DIR__ . '/../../_partials/footer.php'; // Path to footer from Pages/panel/
?> 