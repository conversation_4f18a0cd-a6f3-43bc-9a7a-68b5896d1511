<?php
session_start();

// Include Header first to get $currentLang
require __DIR__ . '/../../_partials/header.php';

// --- Language specific messages & variables --- START ---
$msg_must_login_pwd = ($currentLang === 'en' ? 'You must log in to manage your password.' : 'Debes iniciar sesión para gestionar tu contraseña.');
$msg_check_account_error = ($currentLang === 'en' ? 'Error verifying account type.' : 'Error al verificar el tipo de cuenta.');
$msg_db_conn_error_pwd = ($currentLang === 'en' ? 'Internal server error (DB conn).' : 'Error interno del servidor (DB conn).');
$msg_all_fields_required = ($currentLang === 'en' ? 'All fields are required.' : 'Todos los campos son obligatorios.');
$msg_passwords_no_match = ($currentLang === 'en' ? 'The new password and confirmation do not match.' : 'La nueva contraseña y la confirmación no coinciden.');
$msg_password_too_short = ($currentLang === 'en' ? 'The new password must be at least 8 characters long.' : 'La nueva contraseña debe tener al menos 8 caracteres.');
$msg_update_pwd_db_error = ($currentLang === 'en' ? 'Error updating password in the database.' : 'Error al actualizar la contraseña en la base de datos.');
$msg_current_pwd_incorrect = ($currentLang === 'en' ? 'The current password is not correct.' : 'La contraseña actual no es correcta.');
$msg_db_conn_error_on_update = ($currentLang === 'en' ? 'Error connecting to the database.' : 'Error al conectar con la base de datos.');
$msg_update_pwd_success = ($currentLang === 'en' ? 'Password updated successfully.' : 'Contraseña actualizada correctamente.');
$google_login_message1 = ($currentLang === 'en' ? 'You are logged in using your Google account' : 'Has iniciado sesión utilizando tu cuenta de Google');
$google_login_message2 = ($currentLang === 'en' ? 'You don\'t need to manage a separate password in our system. To change your password, please do so directly in your Google account settings.' : 'No necesitas gestionar una contraseña separada en nuestro sistema. Para cambiar la contraseña, por favor hazlo directamente en tu cuenta de Google.');
$google_link_text = ($currentLang === 'en' ? 'Go to Google security settings' : 'Ir a la configuración de seguridad de Google');
// --- Language specific messages & variables --- END ---


// Verificar si el usuario está logueado (using translated message)
if (!isset($_SESSION['user_id'])) {
    $loginUrlPwd = ($currentLang === 'en' ? '/en/login' : '/login');
    header('Location: ' . $loginUrlPwd . '?error=' . urlencode($msg_must_login_pwd));
    exit;
}

// --- Incluir config y db (ya no necesario incluir header aquí) --- 
require_once __DIR__ . '/../../../includes/config.php'; 
require_once __DIR__ . '/../../../includes/db.php'; 

$userId = $_SESSION['user_id'];
$userEmail = $_SESSION['user_email'] ?? ($currentLang === 'en' ? 'your email' : 'tu email'); // Obtener email para mensaje Google
$successMessage = null;
$errorMessage = null;
$hasPassword = false; // Flag para saber si mostrar form o mensaje google

// --- Comprobar si el usuario tiene contraseña hash --- 
$db = getDbConnection();
if ($db) {
    try {
        $stmt = $db->prepare("SELECT password_hash FROM users WHERE id = :id");
        $stmt->execute([':id' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        // Si tiene un hash de contraseña no vacío, puede cambiarla
        if ($user && !empty($user['password_hash'])) {
            $hasPassword = true;
        }
    } catch (PDOException $e) {
        $errorMessage = $msg_check_account_error; // Use translated message
        error_log("Error BD comprobando password_hash para User ID {$userId}: " . $e->getMessage());
    }
} else {
    $errorMessage = $msg_db_conn_error_pwd; // Use translated message
    error_log("Error conexión BD en cambiar-contrasena.php (check hash)");
}

// --- Procesar el formulario si se envió Y si el usuario tiene contraseña --- 
if ($hasPassword && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmNewPassword = $_POST['confirm_new_password'] ?? '';

    if (empty($currentPassword) || empty($newPassword) || empty($confirmNewPassword)) {
        $errorMessage = $msg_all_fields_required; // Use translated message
    } elseif ($newPassword !== $confirmNewPassword) {
        $errorMessage = $msg_passwords_no_match; // Use translated message
    } elseif (strlen($newPassword) < 8) {
        $errorMessage = $msg_password_too_short; // Use translated message
    } else {
        // Re-verificar conexión y hash antes de cambiar
        $db = getDbConnection(); // Re-obtener conexión por si acaso
        if ($db) {
            try {
                $stmt = $db->prepare("SELECT password_hash FROM users WHERE id = :id");
                $stmt->execute([':id' => $userId]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($user && password_verify($currentPassword, $user['password_hash'])) {
                    $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
                    $updateStmt = $db->prepare("UPDATE users SET password_hash = :new_hash WHERE id = :id");
                    if ($updateStmt->execute([':new_hash' => $newPasswordHash, ':id' => $userId])) {
                        $successMessage = $msg_update_pwd_success; // Use translated message
                    } else {
                        $errorMessage = $msg_update_pwd_db_error; // Use translated message
                        error_log("Error DB actualizando password_hash para User ID {$userId}");
                    }
                } else {
                    $errorMessage = $msg_current_pwd_incorrect; // Use translated message
                }
            } catch (PDOException $e) {
                $errorMessage = $msg_db_conn_error_on_update; // Use translated message
                error_log("Error BD cambiando contraseña para User ID {$userId}: " . $e->getMessage());
            }
        } else {
             $errorMessage = $msg_db_conn_error_pwd; // Use translated message
             error_log("Error conexión BD en cambiar-contrasena.php (POST)");
         }
    }
}

// Header already included

?>

<!-- Reutilizar la estructura del panel: Sidebar + Contenido -->
<div class="flex flex-col md:flex-row min-h-screen pt-16">

    <!-- Sidebar -->
    <?php require __DIR__ . '/../../_partials/panel_sidebar.php'; // Path to sidebar from Pages/panel/ ?>

    <!-- Contenido Principal -->
    <main class="flex-grow p-6 md:p-10 bg-darker text-white">
        <h1 class="text-3xl font-bold mb-6"><?php echo ($currentLang === 'en' ? 'Password' : 'Contraseña'); ?></h1>

        <?php if ($successMessage): ?>
            <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold"><?php echo ($currentLang === 'en' ? 'Success!' : '¡Éxito!'); ?></strong>
                <span class="block sm:inline"><?= htmlspecialchars($successMessage) ?></span>
            </div>
        <?php endif; ?>
        <?php if ($errorMessage && !$successMessage): ?>
             <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
                 <strong class="font-bold"><?php echo ($currentLang === 'en' ? 'Error:' : 'Error:'); ?></strong>
                 <span class="block sm:inline"><?= htmlspecialchars($errorMessage) ?></span>
             </div>
        <?php endif; ?>

        <?php 
        if ($hasPassword) {
            // Mostrar formulario solo si el usuario tiene contraseña 
            $formAction = "/panel/cambiar-contrasena"; // Action always points to the Spanish base path
            $forgotPasswordLink = ($currentLang === 'en' ? '/en/forgot-password' : '/recuperar-contrasena'); // Link depends on language
        ?>
            <form action="<?= $formAction ?>" method="POST" class="max-w-lg space-y-4">
                <div>
                    <label for="current_password" class="block text-sm font-medium text-gray-300 mb-1"><?php echo ($currentLang === 'en' ? 'Current Password' : 'Contraseña Actual'); ?></label>
                    <input type="password" name="current_password" id="current_password" required 
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
                <div>
                    <label for="new_password" class="block text-sm font-medium text-gray-300 mb-1"><?php echo ($currentLang === 'en' ? 'New Password' : 'Nueva Contraseña'); ?></label>
                    <input type="password" name="new_password" id="new_password" required minlength="8"
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <p class="text-xs text-gray-500 mt-1"><?php echo ($currentLang === 'en' ? 'Minimum 8 characters.' : 'Mínimo 8 caracteres.'); ?></p>
                </div>
                <div>
                    <label for="confirm_new_password" class="block text-sm font-medium text-gray-300 mb-1"><?php echo ($currentLang === 'en' ? 'Confirm New Password' : 'Confirmar Nueva Contraseña'); ?></label>
                    <input type="password" name="confirm_new_password" id="confirm_new_password" required minlength="8"
                           class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
                <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-3 sm:space-y-0 pt-2">
                    <button type="submit" class="w-full sm:w-auto bg-primary text-black px-6 py-2 rounded-lg font-bold hover:bg-primary-dark transition duration-300 shadow-md">
                        <?php echo ($currentLang === 'en' ? 'Update Password' : 'Actualizar Contraseña'); ?>
                    </button>
                    <a href="<?= $forgotPasswordLink ?>" class="w-full sm:w-auto text-center bg-gray-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-700 transition duration-300">
                        <?php echo ($currentLang === 'en' ? 'Forgot Password' : 'Recuperar Contraseña'); ?>
                    </a>
                </div>
            </form>
        <?php 
        } else { // El usuario no tiene contraseña (probablemente login con Google)
        ?>
            <div class="bg-gray-800 border border-gray-700 text-gray-300 px-6 py-5 rounded-lg max-w-lg">
                <h2 class="text-lg font-semibold text-white mb-3"><?php echo ($currentLang === 'en' ? 'Google Sign-In' : 'Inicio de sesión con Google'); ?></h2>
                <p class="mb-4"><?= htmlspecialchars($google_login_message1) ?> (<?= htmlspecialchars($userEmail) ?>).</p>
                <p class="mb-4"><?= htmlspecialchars($google_login_message2) ?></p>
                <a href="https://myaccount.google.com/security" target="_blank" rel="noopener noreferrer" class="inline-flex items-center text-primary hover:underline">
                    <?= htmlspecialchars($google_link_text) ?> &rarr;
                </a>
            </div>
         <?php 
        } // Fin del if ($hasPassword) 
        ?>

    </main>
</div>

<?php 
require __DIR__ . '/../../_partials/footer.php'; // Path to footer from Pages/panel/
?> 