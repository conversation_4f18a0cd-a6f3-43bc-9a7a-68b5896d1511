<?php
session_start();

// Proteger página
if (!isset($_SESSION['user_id'])) {
    header('Location: /login?error=' . urlencode('Debes iniciar sesión para cambiar tu contraseña.'));
    exit;
}

$pageTitle = 'Cambiar contraseña | Panel de Usuario';
$pageDescription = 'Modifica la contraseña de tu cuenta.';
$noIndex = true;
require __DIR__ . '/../_partials/header.php';

$userEmail = htmlspecialchars($_SESSION['user_email'] ?? 'Usuario');
// Podríamos verificar si el usuario se logueó con google para mostrar un mensaje diferente
$isGoogleLogin = isset($_SESSION['google_id']); // Asume que guardamos google_id en la sesión tras el login de Google

?>

<main class="container mx-auto px-4 py-12 pt-24">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold">Cambiar contraseña</h1>
        <a href="/panel" class="text-primary hover:underline">&larr; Volver al panel</a>
    </div>

    <!-- Contenido específico -->
    <section class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 mb-8">
        
        <?php if ($isGoogleLogin): ?>
            <h2 class="text-xl font-semibold mb-4 text-gray-100">Inicio de sesión con Google</h2>
            <p class="text-gray-300">Has iniciado sesión utilizando tu cuenta de Google (<strong class="font-semibold"><?= $userEmail ?></strong>).</p>
            <p class="text-gray-400 mt-2">No necesitas gestionar una contraseña separada en nuestro sistema. Para cambiar la contraseña, por favor hazlo directamente en tu cuenta de Google.</p>
            <a href="https://myaccount.google.com/security" target="_blank" rel="noopener noreferrer" class="inline-block mt-4 text-primary hover:underline">Ir a la configuración de seguridad de Google &rarr;</a>
        <?php else: ?>
            <h2 class="text-xl font-semibold mb-4 text-gray-100">Establecer nueva contraseña</h2>
            <!-- TODO: Implementar formulario de cambio de contraseña -->
            <form action="/api/auth/change_password.php" method="POST" class="space-y-4">
                 <div>
                    <label for="current_password" class="block text-sm font-medium text-gray-300 mb-1">Contraseña actual</label>
                    <input type="password" name="current_password" id="current_password" required autocomplete="current-password"
                        class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2">
                </div>
                 <div>
                    <label for="new_password" class="block text-sm font-medium text-gray-300 mb-1">Nueva contraseña</label>
                    <input type="password" name="new_password" id="new_password" required autocomplete="new-password"
                        class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2">
                </div>
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-300 mb-1">Confirmar nueva contraseña</label>
                    <input type="password" name="confirm_password" id="confirm_password" required autocomplete="new-password"
                        class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2">
                </div>
                 <div>
                    <button type="submit" 
                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition duration-300">
                        Guardar nueva contraseña
                    </button>
                </div>
            </form>
             <p class="text-xs text-gray-500 mt-4">Asegúrate de usar una contraseña segura y única.</p>
        <?php endif; ?>
        
    </section>

</main>

<?php 
require __DIR__ . '/../_partials/footer.php';
?> 