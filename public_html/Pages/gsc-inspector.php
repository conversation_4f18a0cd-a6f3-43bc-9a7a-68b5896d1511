<?php
session_start(); // Iniciar sesión para poder obtener el email del usuario si está logueado
// Incluir config para tener acceso a los Price IDs de Stripe
require_once __DIR__ . '/../../includes/config.php'; // Asegúrate que la ruta es correcta
$pageTitle = 'Gsc inspector - Extensión de Chrome para Google Search Console | Pietra Mundo Digital';
$pageDescription = 'Analiza el estado de indexación, rendimiento y métricas clave de cualquier URL directamente desde Google Search Console con GSC Inspector.';
$canonicalUrl = 'https://www.pietramundodigital.com/gsc-inspector'; // URL canónica de esta página
require __DIR__ . '/../_partials/header.php';
?>

<main class="container mx-auto px-4 py-12 pt-24">
    <!-- Hero Section (Sin cambios) -->
    <section class="text-center mb-20">
        <h1 class="text-4xl md:text-5xl font-bold mb-4 leading-tight">Gsc inspector: Tu atajo a los datos clave de search console</h1>
        <p class="text-xl text-gray-400 mb-8 max-w-3xl mx-auto">Deja de perder tiempo navegando por GSC. Obtén el estado de indexación, canónicas, rendimiento y más, directamente en la URL que estás visitando.</p>
        <div class="flex justify-center items-center space-x-4 mb-10">
            <a href="https://chromewebstore.google.com/detail/gsc-page-inspector-by-pie/llglmegjjadpomhgnoofhpboooadegga?authuser=0&hl=en" target="_blank" rel="noopener noreferrer" class="bg-primary text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-primary-dark transition duration-300 shadow-md">
                Instalar Extensión para Chrome
            </a>
            <a href="#planes-precios" class="text-primary hover:underline">
                Ver Planes y Precios
            </a>
        </div>
        <!-- Placeholder para una imagen o video demostrativo (Sin cambios) -->
        <iframe
            id="video-demostracion"
            class="w-full block border-none"
            style="aspect-ratio: 16 / 9;"
            src="https://www.youtube.com/embed/KOK-ukGI9ac?mute=1&controls=01" title="Video demostrativo de la extensión"
            frameborder="0"
            allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerpolicy="strict-origin-when-cross-origin"
            allowfullscreen>
        </iframe>
        <div id="video-overlay" class="absolute top-0 left-0 w-full h-full cursor-pointer z-10"></div>
        <script src="https://www.youtube.com/iframe_api"></script>
        <script>
        // Script del reproductor de YouTube (Sin cambios)
        var player;

        function onYouTubeIframeAPIReady() {
            player = new YT.Player('video-demostracion', {
            events: {
                'onReady': onPlayerReady,
                'onStateChange': onPlayerStateChange
            }
            });
        }

        function onPlayerReady(event) {
            var overlay = document.getElementById('video-overlay');
            if (overlay) {
            overlay.addEventListener('click', function() {
                togglePlayPause();
            });
            }
            setTimeout(function() {
            if (player && typeof player.playVideo === 'function') {
                player.playVideo();
            }
            }, 2000);
        }

        function togglePlayPause() {
            if (!player || typeof player.getPlayerState !== 'function') return;
            var playerState = player.getPlayerState();
            if (playerState === YT.PlayerState.PLAYING) {
            player.pauseVideo();
            } else {
            player.playVideo();
            }
        }

        function onPlayerStateChange(event) {
            if (event.data === YT.PlayerState.ENDED) {
            setTimeout(function() {
                if (player && typeof player.seekTo === 'function') {
                player.seekTo(0);
                player.pauseVideo();
                }
            }, 100);
         }
        }
        </script>
    </section>

    <!-- Beneficios Clave (Sin cambios) -->
    <section class="mb-20">
        <h2 class="text-3xl font-bold text-center mb-12">¿Por qué GSC Inspector?</h2>
        <div class="grid md:grid-cols-3 gap-10">
            <div class="text-center px-4">
                <div class="text-4xl text-primary mb-4">⏱️</div>
                <h3 class="text-xl font-semibold mb-2 text-gray-100">Ahorra tiempo precioso</h3>
                <p class="text-gray-400">Accede a los datos vitales de GSC (indexación, rendimiento, canónicas) con un solo clic, sin salir de la página que analizas.</p>
            </div>
            <div class="text-center px-4">
                 <div class="text-4xl text-primary mb-4">📊</div>
                 <h3 class="text-xl font-semibold mb-2 text-gray-100">Datos accionables al instante</h3>
                 <p class="text-gray-400">Visualiza clics, impresiones, CTR y posición media directamente en la extensión. Identifica oportunidades y problemas rápidamente.</p>
            </div>
            <div class="text-center px-4">
                 <div class="text-4xl text-primary mb-4">🔗</div>
                 <h3 class="text-xl font-semibold mb-2 text-gray-100">Diagnóstico de indexación rápido</h3>
                 <p class="text-gray-400">Verifica si una URL está indexada, cuál es la canónica de Google vs. la declarada, y cuándo fue el último rastreo.</p>
            </div>
        </div>
    </section>

    <!-- Planes y Precios -->
    <section id="planes-precios" class="mb-20 scroll-mt-20">
        <h2 class="text-3xl font-bold text-center mb-12">Planes flexibles para cada necesidad</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Plan Free (Sin cambios en el botón) -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                <h3 class="text-xl font-semibold mb-2 text-gray-100">Free</h3>
                <p class="text-gray-400 mb-4">Ideal para empezar</p>
                <p class="text-3xl font-bold mb-4 text-gray-100">Gratis</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 30 Inspecciones/mes</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Datos básicos de indexación</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Datos básicos de rendimiento</li>
                </ul>
                <button class="mt-auto w-full bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-not-allowed" disabled>Plan Actual (Gratuito)</button>
            </div>

            <!-- Plan Personal -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-primary flex flex-col ring-2 ring-primary relative overflow-hidden">
                <span class="absolute top-0 right-0 w-32 h-8 bg-primary flex items-center justify-center text-white text-xs font-bold transform rotate-45 translate-x-8 translate-y-4 border-2 border-white">Más Popular</span>
                <h3 class="text-xl font-bold text-accent mb-2">Personal</h3>
                <p class="text-3xl font-bold text-white mb-1">9,99 €<span class="text-lg font-normal text-gray-500">/mes</span></p>
                <p class="text-xs text-gray-500 mb-4">IVA no incluido</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 120 Inspecciones/mes</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Datos completos de indexación</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Datos completos de rendimiento</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Comparación de fechas</li>
                </ul>
                <?php if (isset($_SESSION['user_id'])) : ?>
                    <button data-price-id="<?= defined('STRIPE_PRICE_ID_PERSONAL') ? STRIPE_PRICE_ID_PERSONAL : '' ?>" class="stripe-checkout-button mt-auto bg-accent text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-accent-dark transition duration-300">Elegir Plan</button>
                <?php else : ?>
                    <a href="/login?redirect=/gsc-inspector%23planes-precios" class="mt-auto text-center bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-pointer hover:bg-gray-500 transition duration-300">Inicia sesión para elegir</a>
                <?php endif; ?>
            </div>

            <!-- Plan Plus -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                <h3 class="text-xl font-bold text-yellow-500 mb-2">Plus</h3>
                <p class="text-3xl font-bold text-white mb-1">19,99 €<span class="text-lg font-normal text-gray-500">/mes</span></p>
                <p class="text-xs text-gray-500 mb-4">IVA no incluido</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 360 Inspecciones/mes</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Todo en Personal</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Exportación de datos (Próximamente)</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Soporte prioritario</li>
                </ul>
                 <?php if (isset($_SESSION['user_id'])) : ?>
                    <button data-price-id="<?= defined('STRIPE_PRICE_ID_PLUS') ? STRIPE_PRICE_ID_PLUS : '' ?>" class="stripe-checkout-button mt-auto bg-yellow-500 text-black px-6 py-2 rounded-lg text-lg font-semibold hover:bg-yellow-600 transition duration-300">Elegir Plan</button>
                 <?php else : ?>
                    <a href="/login?redirect=/gsc-inspector%23planes-precios" class="mt-auto text-center bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-pointer hover:bg-gray-500 transition duration-300">Inicia sesión para elegir</a>
                 <?php endif; ?>
            </div>

            <!-- Plan Agency -->
            <div class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 flex flex-col">
                <h3 class="text-xl font-bold text-blue-400 mb-2">Agency</h3>
                <p class="text-3xl font-bold text-white mb-1">49,99 €<span class="text-lg font-normal text-gray-500">/mes</span></p>
                <p class="text-xs text-gray-500 mb-4">IVA no incluido</p>
                <ul class="space-y-2 text-gray-300 mb-6 flex-grow">
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> 1200 Inspecciones/mes</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Todo en Plus</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Acceso API (Próximamente)</li>
                    <li class="flex items-center"><span class="text-green-500 mr-2">✓</span> Múltiples usuarios (Próximamente)</li>
                </ul>
                 <?php if (isset($_SESSION['user_id'])) : ?>
                    <button data-price-id="<?= defined('STRIPE_PRICE_ID_AGENCY') ? STRIPE_PRICE_ID_AGENCY : '' ?>" class="stripe-checkout-button mt-auto bg-black text-white px-6 py-2 rounded-lg text-lg font-semibold hover:bg-gray-900 transition duration-300 border border-gray-600">Elegir Plan</button>
                 <?php else : ?>
                    <a href="/login?redirect=/gsc-inspector%23planes-precios" class="mt-auto text-center bg-gray-600 text-gray-300 px-6 py-2 rounded-lg font-semibold cursor-pointer hover:bg-gray-500 transition duration-300">Inicia sesión para elegir</a>
                 <?php endif; ?>
            </div>
        </div>
         <p class="text-center mt-8 text-gray-400">¿Necesitas más de 1200 inspecciones? <a href="/contacto" class="text-primary hover:underline">Contacta con nosotros</a> para soluciones Enterprise.</p>
    </section>

    <!-- Sección CTA Final (Sin cambios) -->
    <section class="text-center bg-gradient-to-r from-primary to-secondary text-white py-16 px-8 rounded-lg shadow-xl">
        <h2 class="text-3xl font-bold mb-4">Instala GSC Inspector hoy mismo</h2>
        <p class="text-xl mb-8">Empieza a ahorrar tiempo y a tomar decisiones SEO más rápidas e informadas.</p>
        <a href="https://chromewebstore.google.com/detail/gsc-page-inspector-by-pie/llglmegjjadpomhgnoofhpboooadegga?authuser=0&hl=en" target="_blank" rel="noopener noreferrer" class="bg-accent text-black px-8 py-3 rounded-lg text-lg font-semibold hover:bg-accent-dark transition duration-300 shadow-md">
            Instalar Extensión Gratis
        </a>
    </section>

    <!-- Sección "Listo para mejorar" (Sin cambios) -->
    <section class="text-center mb-16">
        <h2 class="text-2xl font-bold mb-6">¿Listo para mejorar tu SEO?</h2>
        <a href="/login" class="bg-secondary text-white px-8 py-3 rounded-lg text-lg hover:bg-secondary-dark inline-block">
            Acceder al Panel
        </a>
    </section>
</main>

<?php
// --- AÑADIR NUEVO BLOQUE DE SCRIPT PARA STRIPE CHECKOUT ---
if (isset($_SESSION['user_id'])) : // Solo añadir este script si el usuario está logueado
?>
<script>
document.addEventListener('DOMContentLoaded', () => {
    const checkoutButtons = document.querySelectorAll('.stripe-checkout-button');

    checkoutButtons.forEach(button => {
        button.addEventListener('click', async (event) => {
            // No necesitamos preventDefault() si usamos <button type="button">, pero lo dejamos por si acaso

            const priceId = button.getAttribute('data-price-id');
            if (!priceId) {
                console.error('Stripe Price ID no encontrado en el botón.');
                alert('Error: No se pudo obtener el ID del plan. Contacta con soporte.');
                return;
            }

            // Mostrar estado de carga
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = 'Procesando...';

            try {
                const response = await fetch('/api/create_checkout_session.php', { // Asegúrate que la ruta a tu API es correcta
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // No necesitamos cabecera 'Authorization' aquí, la sesión PHP se envía via cookie
                    },
                    body: JSON.stringify({ priceId: priceId }) // Enviar el Price ID correcto
                });

                // Restaurar botón en caso de error antes de la redirección
                const restoreButton = () => {
                   button.disabled = false;
                   button.textContent = originalText;
                };

                if (!response.ok) {
                    if (response.status === 401) {
                         alert('Tu sesión ha caducado. Por favor, inicia sesión de nuevo para suscribirte.');
                         // Redirigir a login
                         window.location.href = '/login?redirect=/gsc-inspector%23planes-precios';
                         // No restauramos el botón porque redirigimos
                         throw new Error('Usuario no autenticado'); // Salir del try
                    }
                    // Intentar obtener mensaje de error del backend
                    const errorData = await response.json().catch(() => ({ error: `Error del servidor (${response.status})` }));
                    alert(`Error al iniciar el pago: ${errorData.error || response.statusText}`);
                    restoreButton(); // Restaurar botón
                    throw new Error(`Error del servidor: ${response.status}`); // Salir del try
                }

                const session = await response.json();

                // Redirigir a Stripe Checkout
                if (session.url) {
                    window.location.href = session.url;
                    // No restauramos el botón aquí porque la redirección es inminente
                } else {
                     console.error('No se recibió URL de Stripe en la respuesta.');
                     alert('Error: No se pudo redirigir a la página de pago. Contacta con soporte.');
                     restoreButton(); // Restaurar botón
                }

            } catch (error) {
                console.error('Error al crear la sesión de Stripe Checkout:', error);
                // Si el error no fue un error HTTP manejado arriba, restauramos botón y mostramos alerta genérica
                if (error.message !== 'Usuario no autenticado' && !error.message.startsWith('Error del servidor')) {
                    alert('Ocurrió un error inesperado. Por favor, inténtalo de nuevo o contacta con soporte.');
                   button.disabled = false; // Asegurarse de restaurar si hubo otro tipo de error
                   button.textContent = originalText;
                }
            }
        });
    });
});
</script>
<?php
endif; // Fin de if (isset($_SESSION['user_id']))

require __DIR__ . '/../_partials/footer.php';
?>