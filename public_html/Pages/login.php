<?php 
session_start(); // <-- MOVER AQUÍ, AL INICIO ABSOLUTO

$pageTitle = 'Acceso de usuarios | Pietra Mundo Digital';
$pageDescription = 'Accede a tu panel de control de GSC Inspector para gestionar tu suscripción, ver el historial de uso y métricas.';
$noIndex = true; // Añadir meta robots noindex a esta página
$webClientId = '***********-mpciacvvb3mn7tdtfea58b5dmm4sv0vc.apps.googleusercontent.com'; // <-- AÑADIR TU WEB CLIENT ID AQUÍ
require __DIR__ . '/../_partials/header.php';
?>

<!-- AÑADIR: Script de Google Platform Library -->
<script src="https://accounts.google.com/gsi/client" async defer></script>

<main class="container mx-auto px-4 py-16 flex justify-center items-center">
    <div class="w-full max-w-md bg-gray-800 p-8 rounded-lg shadow-lg border border-gray-700">
        <h1 class="text-3xl font-bold mb-6 text-center text-gray-100">Acceso de usuario</h1>
        
        <!-- Mensajes de error/éxito -->
        <div id="message-area" class="mb-6"> <!-- Contenedor para mensajes JS -->
            <?php if (isset($_GET['error'])): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline"><?= htmlspecialchars(urldecode($_GET['error'])) ?></span>
                </div>
            <?php endif; ?>
            <?php if (isset($_GET['success'])): ?>
                 <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                     <span class="block sm:inline"><?= htmlspecialchars(urldecode($_GET['success'])) ?></span>
                 </div>
            <?php endif; ?>
        </div>

        <!-- Botón de Google Sign-In -->
        <div id="google-signin-button-container" class="mb-4">
             <div id="g_id_onload"
                  data-client_id="<?= $webClientId ?>"
                  data-context="signin"
                  data-ux_mode="popup"
                  data-callback="handleGoogleSignIn"
                  data-nonce="" 
                  data-auto_prompt="false">
             </div>
             <div class="g_id_signin"
                  data-type="standard"
                  data-shape="rectangular"
                  data-theme="filled_black"
                  data-text="signin_with"
                  data-size="large"
                  data-logo_alignment="left">
            </div>
        </div>

        <div class="relative flex py-5 items-center">
            <div class="flex-grow border-t border-gray-600"></div>
            <span class="flex-shrink mx-4 text-gray-500 text-sm">O inicia sesión con tu email</span>
            <div class="flex-grow border-t border-gray-600"></div>
        </div>

        <form action="/api/auth/login.php" method="POST" class="space-y-6">
            <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                <input type="email" name="email" id="email" required autocomplete="email"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2"
                    placeholder="<EMAIL>">
            </div>

            <div>
                <div class="flex justify-between items-baseline">
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-1">Contraseña</label>
                    <a href="#" class="text-sm text-primary hover:underline">¿Olvidaste tu contraseña?</a>
                </div>
                <input type="password" name="password" id="password" required autocomplete="current-password"
                    class="mt-1 block w-full rounded-md border-gray-600 bg-gray-700 text-gray-100 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50 text-lg px-4 py-2">
            </div>

            <div>
                <button type="submit" 
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-lg font-medium text-black bg-accent hover:bg-accent-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-accent transition duration-300">
                    Iniciar sesión
                </button>
            </div>
        </form>

        <div class="mt-8 text-center">
            <p class="text-sm text-gray-400">
                ¿Aún no tienes cuenta? 
                <a href="/registro" class="font-medium text-primary hover:underline">
                    Regístrate aquí
                </a>
            </p>
        </div>
    </div>
</main>

<!-- AÑADIR: JavaScript para manejar el callback de Google y enviar al backend -->
<script>
    function handleGoogleSignIn(response) {
        console.log("Google Sign-In Response:", response);
        const idToken = response.credential;
        const messageArea = document.getElementById('message-area');
        messageArea.innerHTML = ''; // Limpiar mensajes previos

        // Mostrar un indicador de carga
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'text-center text-gray-600';
        loadingIndicator.textContent = 'Verificando con el servidor...';
        messageArea.appendChild(loadingIndicator);

        // Enviar el ID Token al backend para verificación y login
        fetch('/api/auth/verify_google_web.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ credential: idToken })
        })
        .then(res => {
            if (!res.ok) {
                 // Intentar leer el error del JSON, si no, usar el statusText
                 return res.json().catch(() => null).then(errData => {
                     throw new Error(errData?.error || `Error del servidor: ${res.statusText}`);
                 });
            }
            return res.json();
        })
        .then(data => {
            messageArea.innerHTML = ''; // Limpiar loading
            if (data.success) {
                // Éxito: Redirigir al panel de usuario (URL limpia)
                window.location.href = '/panel'; 
            } else {
                // Mostrar error devuelto por el backend
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative';
                errorDiv.textContent = data.error || 'Error desconocido al procesar inicio de sesión con Google.';
                messageArea.appendChild(errorDiv);
            }
        })
        .catch(error => {
            console.error('Error en fetch a verify_google_web:', error);
            messageArea.innerHTML = ''; // Limpiar loading
            const errorDiv = document.createElement('div');
            errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative';
            errorDiv.textContent = error.message || 'No se pudo conectar con el servidor para verificar el inicio de sesión.';
            messageArea.appendChild(errorDiv);
        });
    }
</script>

<?php 
require __DIR__ . '/../_partials/footer.php';
?>