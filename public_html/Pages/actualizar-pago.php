<?php
session_start();

// Proteger página
if (!isset($_SESSION['user_id'])) {
    header('Location: /login?error=' . urlencode('Debes iniciar sesión para actualizar tu información de pago.'));
    exit;
}

$pageTitle = 'Actualizar información de pago | Panel de Usuario';
$pageDescription = 'Modifica tu método de pago para la suscripción de GSC Inspector.';
$noIndex = true;
require __DIR__ . '/../_partials/header.php';

$userEmail = htmlspecialchars($_SESSION['user_email'] ?? 'Usuario');

?>

<main class="container mx-auto px-4 py-12 pt-24">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold">Actualizar información de pago</h1>
        <a href="/panel" class="text-primary hover:underline">&larr; Volver al panel</a>
    </div>

    <p class="mb-6 text-lg"><PERSON><PERSON>, <strong class="font-semibold"><?= $userEmail ?></strong>. Puedes actualizar tu método de pago a través del portal seguro de nuestro proveedor.</p>

    <!-- Contenido específico -->
    <section class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 mb-8">
        <h2 class="text-xl font-semibold mb-4 text-gray-100">Gestionar método de pago</h2>
        
        <!-- TODO: Mostrar método de pago actual (parcialmente oculto) si Paddle API lo permite -->
        <p class="text-gray-300 mb-4">Método de pago actual: <span class="font-mono">**** **** **** 1234 (Visa - Ejemplo)</span></p>
        
        <!-- TODO: Enlace al Portal de Cliente de Paddle para actualizar -->
        <p class="text-gray-400 mb-4">Para actualizar tu tarjeta de crédito, débito o cuenta de PayPal, utiliza el portal seguro de nuestro procesador de pagos, Paddle.</p>
        <a href="#" target="_blank" class="inline-block bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition duration-300">
            Actualizar en el portal de cliente
        </a>
         <p class="text-xs text-gray-500 mt-2">Serás redirigido a Paddle, nuestro socio de pagos seguro.</p>
        
    </section>

</main>

<?php 
require __DIR__ . '/../_partials/footer.php';
?> 