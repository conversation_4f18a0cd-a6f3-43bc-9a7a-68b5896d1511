<?php
// Lógica de PHP para procesar el archivo y generar el sitemap


// Configuración
$sitemap_filename = 'sitemap.xml';
$changefreq = 'daily'; // Puedes cambiarlo: always, hourly, daily, weekly, monthly, yearly, never
$priority = '0.8';   // Prioridad de 0.0 a 1.0
$url_column_index = 0; // Índice de la columna que contiene las URLs en el CSV (0 es la primera)


$upload_message = '';
$sitemap_content = '';
$error_message = '';
$urls_processed_count = 0;


// Verificar si se ha enviado un archivo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['csvfile'])) {
    if ($_FILES['csvfile']['error'] == UPLOAD_ERR_OK) {
        $csv_temp_path = $_FILES['csvfile']['tmp_name'];
        $csv_original_name = basename($_FILES['csvfile']['name']);


        // Validar extensión del archivo (opcional pero recomendado)
        $file_extension = strtolower(pathinfo($csv_original_name, PATHINFO_EXTENSION));
        if ($file_extension != 'csv') {
            $error_message = "Error: Por favor, sube un archivo CSV válido.";
        } else {
            $urls = [];
            if (($handle = fopen($csv_temp_path, "r")) !== FALSE) {
                // Intentar detectar encabezado
                $first_row = fgetcsv($handle, 1000, ",");
                if ($first_row !== FALSE) {
                    if (isset($first_row[$url_column_index]) &&
                        (stripos($first_row[$url_column_index], 'http') === 0 || stripos($first_row[$url_column_index], 'https') === 0)) {
                        // No parece ser un encabezado, procesar esta fila
                        $url = trim($first_row[$url_column_index]);
                        if (!empty($url)) {
                            $urls[] = htmlspecialchars($url, ENT_XML1, 'UTF-8');
                        }
                    } else {
                        // Es un encabezado o la columna no es una URL, omitir
                        $upload_message .= "Encabezado detectado/primera fila omitida: " . htmlspecialchars(implode(", ", $first_row)) . "<br>";
                    }
                }


                // Leer el resto de las URLs
                while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    if (isset($data[$url_column_index])) {
                        $url = trim($data[$url_column_index]);
                        if (!empty($url)) {
                            $urls[] = htmlspecialchars($url, ENT_XML1, 'UTF-8');
                        }
                    }
                }
                fclose($handle);
            } else {
                $error_message = "Error: No se pudo abrir el archivo CSV subido.";
            }


            if (!empty($urls)) {
                $urls_processed_count = count($urls);
                $xml_output = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
                $xml_output .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n"; // Corregido xmlns


                $current_date = date('Y-m-d');


                foreach ($urls as $url) {
                    $xml_output .= '  <url>' . "\n";
                    $xml_output .= '    <loc>' . $url . '</loc>' . "\n";
                    $xml_output .= '    <lastmod>' . $current_date . '</lastmod>' . "\n";
                    $xml_output .= '    <changefreq>' . $changefreq . '</changefreq>' . "\n";
                    $xml_output .= '    <priority>' . $priority . '</priority>' . "\n";
                    $xml_output .= '  </url>' . "\n";
                }
                $xml_output .= '</urlset>';
                $sitemap_content = $xml_output;
                $upload_message .= "Archivo CSV procesado. Se encontraron $urls_processed_count URLs.<br>";


                // Forzar descarga del archivo sitemap.xml
                if (isset($_POST['download_sitemap'])) {
                    header('Content-Type: application/xml; charset=utf-8');
                    header('Content-Disposition: attachment; filename="' . $sitemap_filename . '"');
                    echo $sitemap_content;
                    exit;
                }


            } elseif (empty($error_message)) {
                $error_message = "No se encontraron URLs válidas en el archivo CSV.";
            }
        }
    } elseif ($_FILES['csvfile']['error'] != UPLOAD_ERR_NO_FILE) {
        // Hubo un error diferente a "no se subió archivo"
        $php_upload_errors = [
            UPLOAD_ERR_INI_SIZE   => "El archivo excede la directiva upload_max_filesize en php.ini.",
            UPLOAD_ERR_FORM_SIZE  => "El archivo excede la directiva MAX_FILE_SIZE especificada en el formulario HTML.",
            UPLOAD_ERR_PARTIAL    => "El archivo se subió solo parcialmente.",
            UPLOAD_ERR_NO_TMP_DIR => "Falta una carpeta temporal.",
            UPLOAD_ERR_CANT_WRITE => "No se pudo escribir el archivo en el disco.",
            UPLOAD_ERR_EXTENSION  => "Una extensión de PHP detuvo la subida del archivo.",
        ];
        $error_code = $_FILES['csvfile']['error'];
        $error_message = "Error al subir el archivo: " . ($php_upload_errors[$error_code] ?? "Error desconocido.");
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generador de Sitemap XML desde CSV - Optimiza tu SEO | Pietra Mundo Digital</title> // Título SEO optimizado
    <meta name="description" content="Crea fácilmente sitemaps XML para tu web subiendo un archivo CSV. Mejora el rastreo y la indexación por motores de búsqueda como Google."> // Meta descripción SEO
    <link rel="stylesheet" href="../style.css">
    <style>
        /* Estilos específicos mínimos si son necesarios o para overrides rápidos */
        /* La mayoría de los estilos ahora vendrán de style.css */
        body {
            /* Puedes mantener un color de fondo base aquí si quieres que sea diferente al de style.css */
            /* background-color: #f0f2f5; */ 
            font-family: 'Inter', sans-serif; /* Default font for body */
        }
        .container {
            max-width: 900px; /* Ajusta según necesites */
            margin: 40px auto;
            padding: 25px;
            background-color: #1a1a1a; /* Usando un color de fondo oscuro del tema */
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        h1, h3 {
            color: #34eb95; /* Color de acento del tema */
        }
        label {
            color: #FFFFFF; /* Texto blanco para contraste */
            margin-bottom: 0.5rem;
            /* display: block; */ /* Modified for grid layout */
        }
        input[type="text"],
        input[type="number"],
        select,
        textarea {
            background-color: #2d2d2d; /* Fondo oscuro para inputs */
            color: #FFFFFF; /* Texto blanco para inputs */
            border: 1px solid #4b5563; /* Borde sutil */
            border-radius: 6px; /* rounded-custom */
            padding: 10px;
            /* margin-bottom: 15px; */ /* Handled by grid gap */
            width: 100%; /* Inputs take full width of their grid cell */
            box-sizing: border-box;
        }
        /* Custom file input styling */
        .custom-file-input-container {
            position: relative;
            overflow: hidden;
            display: inline-block;
            padding: 10px;
            border: 1px solid #4b5563;
            border-radius: 6px;
            background-color: #2d2d2d;
            color: #FFFFFF;
            cursor: pointer;
            margin-bottom: 15px;
            width: 100%;
            box-sizing: border-box;
        }
        .custom-file-input-container input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .custom-file-input-label {
            color: #ccc; /* Lighter text for placeholder */
        }

        input[type="submit"], button {
            background-color: #34eb95; /* bg-accent */
            color: #111111; /* Texto oscuro para contraste en botón de acento */
            padding: 12px 20px;
            border: none;
            border-radius: 6px; /* rounded-custom */
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.3s ease;
            display: block; /* For centering with margin auto */
            margin-left: auto;
            margin-right: auto;
        }
        .form-buttons-container {
            display: flex;
            justify-content: center; /* Center buttons */
            gap: 10px; /* Space between buttons if multiple */
            margin-top: 20px; /* Add some space above buttons */
        }

        input[type="submit"]:hover, button:hover {
            background-color: #2aa770; /* Un tono más oscuro del acento para hover */
        }
        .message {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px; /* rounded-custom */
            border: 1px solid transparent;
        }
        .success {
            background-color: rgba(52, 235, 149, 0.1); /* Fondo de acento con transparencia */
            color: #34eb95; /* text-accent */
            border-color: #34eb95; /* border-accent */
        }
        .error {
            background-color: rgba(239, 68, 68, 0.1); /* Rojo con transparencia para errores */
            color: #ef4444; /* Rojo para texto de error */
            border-color: #ef4444;
        }
        textarea {
            min-height: 250px;
            font-family: monospace;
            font-size: 14px;
        }
        .options-container {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #4b5563; /* Borde sutil */
            background-color: #222222; /* Un poco más claro que el bg-darker */
            border-radius: 6px; /* rounded-custom */
            display: grid; /* Use grid for layout */
            grid-template-columns: auto 1fr auto; /* Label, Input, Help Link */
            gap: 10px 15px; /* Row gap, Column gap */
            align-items: center; /* Vertically align items in each row */
        }

        .options-container label {
            font-weight: normal; /* Ensure labels are not bold */
            white-space: nowrap; /* Keep label on one line */
            margin-bottom: 0; /* Remove bottom margin as grid handles spacing */
            text-align: right; /* Align label text to the right for neatness */
            padding-right: 10px; /* Space between label and input */
        }

        .options-container input[type="text"],
        .options-container select,
        .options-container input[type="number"] {
            margin-bottom: 0; /* Espacio menor dentro de las opciones */
            min-width: 150px; /* Ancho mínimo para mejor apariencia */
        }

        .help-link {
            font-size: 0.9em;
            color: #34eb95;
            text-decoration: none;
            white-space: nowrap;
        }
        .help-link:hover {
            text-decoration: underline;
        }

        .content-section p,
        .content-section ul li,
        .content-section ol li,
        .faq-answer p {
            font-family: 'Inter', sans-serif;
            font-style: normal;
            font-weight: 400;
            color: rgb(209, 213, 219);
            font-size: 20px; /* User specified size */
            line-height: 28px; /* User specified line height */
        }
        /* Ensure headers in content sections are not affected by paragraph styles */
        .content-section h2, .content-section h3, .content-section h4 {
            font-family: inherit; /* Or your specific header font */
            font-weight: bold; /* Or your specific header weight */
            color: #34eb95; /* Accent color for headers */
            font-size: revert; /* Revert to browser/stylesheet default for headers */
            line-height: revert;
        }

        .faq-item {
            margin-bottom: 15px;
            border: 1px solid #374151; /* Overall border for the item */
            border-radius: 6px;
        }
        .faq-question {
            background-color: #2d2d2d; 
            color: #e5e7eb; 
            padding: 12px 15px;
            cursor: pointer;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 6px; /* Rounded corners by default */
        }

        .faq-item.open .faq-question {
            border-bottom-left-radius: 0; /* Flatten bottom corners when open */
            border-bottom-right-radius: 0;
            border-bottom: 1px solid #374151; /* Separator line when open */
        }

        .faq-question::after { /* Arrow indicator */
            content: '\25BC'; /* Down arrow */
            font-size: 0.8em;
            transition: transform 0.2s ease-in-out;
        }
        .faq-item.open .faq-question::after {
            transform: rotate(180deg); /* Up arrow when open */
        }
        .faq-answer {
            padding: 15px;
            background-color: #1f2937; 
            /* border-top: 1px solid #374151; */ /* Removed to avoid double border */
            display: none; 
            border-bottom-left-radius: 6px; /* Rounded bottom corners for the answer part */
            border-bottom-right-radius: 6px;
        }
        .faq-item.open .faq-answer {
            display: block;
        }

    </style>
</head>
<body>
    <div class="container mx-auto p-4 md:p-8 legal-content"> 
        <h1 class="text-3xl font-bold mb-6 text-accent">Generador de Sitemap desde Archivo CSV</h1>

        <?php if ($error_message): ?>
            <div class="message error"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <?php if ($upload_message && !$error_message): ?>
            <div class="message success"><?php echo $upload_message; ?></div>
        <?php endif; ?>

        <form action="" method="post" enctype="multipart/form-data">
            <div class="options-container">
                <h3 class="text-xl font-semibold mb-4" style="grid-column: 1 / -1;">Opciones de Configuración</h3> 
                
                <label for="sitemap_filename">Nombre del archivo Sitemap:</label>
                <input type="text" id="sitemap_filename" name="sitemap_filename_config" value="<?php echo htmlspecialchars($sitemap_filename); ?>">
                <a href="#faq-sitemap-filename-def" class="help-link">¿Para qué sirve?</a>

                <label for="changefreq">Frecuencia de Cambio (changefreq):</label>
                <select id="changefreq" name="changefreq_config">
                    <option value="always" <?php echo ($changefreq == 'always' ? 'selected' : ''); ?>>Always</option>
                    <option value="hourly" <?php echo ($changefreq == 'hourly' ? 'selected' : ''); ?>>Hourly</option>
                    <option value="daily" <?php echo ($changefreq == 'daily' ? 'selected' : ''); ?>>Daily</option>
                    <option value="weekly" <?php echo ($changefreq == 'weekly' ? 'selected' : ''); ?>>Weekly</option>
                    <option value="monthly" <?php echo ($changefreq == 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                    <option value="yearly" <?php echo ($changefreq == 'yearly' ? 'selected' : ''); ?>>Yearly</option>
                    <option value="never" <?php echo ($changefreq == 'never' ? 'selected' : ''); ?>>Never</option>
                </select>
                <a href="#faq-changefreq-priority-def" class="help-link">¿Qué es esto?</a> 

                <label for="priority">Prioridad (0.0 - 1.0):</label>
                <input type="number" id="priority" name="priority_config" step="0.1" min="0" max="1" value="<?php echo htmlspecialchars($priority); ?>">
                <a href="#faq-changefreq-priority-def" class="help-link">¿Qué es esto?</a>

                <label for="url_column_index">Índice Columna URL (0 = primera):</label>
                <input type="number" id="url_column_index" name="url_column_index_config" min="0" value="<?php echo htmlspecialchars($url_column_index); ?>">
                <a href="#faq-csv-format-def" class="help-link">¿Para qué sirve?</a>
            </div>

            <div class="mb-6">
                <label for="csvfile_input" class="block mb-2 text-sm font-medium">Sube tu archivo CSV:</label>
                <div class="custom-file-input-container">
                    <span id="file-chosen-text" class="custom-file-input-label">Seleccionar archivo...</span>
                    <input type="file" name="csvfile" id="csvfile_input" accept=".csv" required>
                </div>
            </div>
            
            <div class="form-buttons-container">
                <input type="submit" value="Procesar CSV y Mostrar Sitemap" class="bg-accent hover:bg-green-500 text-dark font-bold py-2 px-4 rounded-custom focus:outline-none focus:shadow-outline">
                <?php if ($sitemap_content): ?>
                    <button type="submit" name="download_sitemap" value="1" class="bg-gray-600 hover:bg-gray-500 text-white font-bold py-2 px-4 rounded-custom focus:outline-none focus:shadow-outline">Descargar Sitemap.xml</button>
                <?php endif; ?>
            </div>
        </form>

        <?php if ($sitemap_content): ?>
            <div class="mt-8">
                <h2 class="text-2xl font-semibold mb-3 text-accent">Contenido del Sitemap Generado (<?php echo $urls_processed_count; ?> URLs):</h2>
                <textarea readonly class="w-full h-64 p-2 border border-gray-600 rounded-custom bg-gray-700 text-gray-300 focus:outline-none focus:border-accent"><?php echo htmlspecialchars($sitemap_content); ?></textarea>
            </div>
        <?php endif; ?>

        <!-- Informational Sections Restored -->
        <section class="content-section">
            <h2>¿Qué es un Sitemap y por qué es Importante?</h2>
            <p>Un sitemap es un archivo XML que actúa como un mapa de tu sitio web, listando todas las URLs importantes que deseas que los motores de búsqueda rastreen e indexen. Aunque los buscadores pueden descubrir páginas a través de enlaces internos y externos, un sitemap proporciona una guía directa y completa, asegurando que no se pase por alto ningún contenido relevante.</p>
            <p>Beneficios clave de tener un sitemap:</p>
            <ul>
                <li><strong>Mejora la Cobertura del Rastreo:</strong> Ayuda a los bots de los motores de búsqueda a encontrar todas las páginas de tu sitio, incluidas aquellas que podrían ser difíciles de descubrir a través de la navegación normal (por ejemplo, páginas nuevas, contenido profundo o páginas huérfanas).</li>
                <li><strong>Indexación Más Rápida:</strong> Al informar a los buscadores sobre nuevo contenido o actualizaciones a través del sitemap, puedes acelerar el proceso de indexación.</li>
                <li><strong>Priorización de Contenido:</strong> Puedes usar atributos como <code>&lt;priority&gt;</code> y <code>&lt;changefreq&gt;</code> para indicar a los buscadores la importancia relativa de tus páginas y con qué frecuencia se actualizan.</li>
                <li><strong>Información Adicional:</strong> Los sitemaps pueden incluir metadatos adicionales sobre cada URL, como la fecha de última modificación (<code>&lt;lastmod&gt;</code>), lo que ayuda a los buscadores a entender mejor tu contenido.</li>
                <li><strong>Esencial para Sitios Grandes o Nuevos:</strong> Para sitios web con muchas páginas o sitios nuevos con pocos enlaces externos, un sitemap es fundamental para garantizar una buena visibilidad en los resultados de búsqueda.</li>
            </ul>
            <p>Nuestra herramienta simplifica la creación de este archivo vital. Solo necesitas un listado de tus URLs en formato CSV, y nosotros generamos el sitemap.xml formateado correctamente según el <a href="http://www.sitemaps.org/protocol.html" target="_blank" rel="noopener noreferrer">protocolo estándar de sitemaps</a>.</p>
        </section>

        <section class="content-section">
            <h2>Cómo Usar el Generador de Sitemaps</h2>
            <p>Utilizar nuestro generador de sitemaps es muy sencillo. Sigue estos pasos:</p>
            <ol style="list-style: decimal; padding-left: 1.5rem;">
                <li><strong>Prepara tu archivo CSV:</strong> Crea un archivo CSV (Valores Separados por Comas). La forma más simple es tener una única columna con todas las URLs de tu sitio web, una URL por fila. Asegúrate de que las URLs sean completas (ej: <code>https://www.tusitio.com/pagina</code>).</li>
                <li><strong>Configura las Opciones (Opcional):</strong>
                    <ul>
                        <li><strong>Nombre del archivo Sitemap:</strong> Por defecto es `sitemap.xml`. Puedes cambiarlo si lo necesitas. <a href="#faq-sitemap-filename-def" class="help-link" style="font-size: smaller;">Más info</a></li>
                        <li><strong>Frecuencia de Cambio:</strong> Selecciona con qué frecuencia esperas que cambie el contenido de tus URLs. <a href="#faq-changefreq-priority-def" class="help-link" style="font-size: smaller;">Más info</a></li>
                        <li><strong>Prioridad:</strong> Define la prioridad de las URLs (de 0.0 a 1.0). <a href="#faq-changefreq-priority-def" class="help-link" style="font-size: smaller;">Más info</a></li>
                        <li><strong>Índice Columna URL:</strong> Si tu CSV tiene varias columnas, indica el número de la columna (empezando por 0) que contiene las URLs. <a href="#faq-csv-format-def" class="help-link" style="font-size: smaller;">Más info</a></li>
                    </ul>
                </li>
                <li><strong>Sube tu archivo CSV:</strong> Haz clic en "Seleccionar archivo" y elige tu archivo CSV.</li>
                <li><strong>Procesa y Visualiza:</strong> Haz clic en "Procesar CSV y Mostrar Sitemap". La herramienta leerá tu archivo, generará el contenido del sitemap y te mostrará una vista previa.</li>
                <li><strong>Descarga tu Sitemap:</strong> Si todo es correcto, haz clic en "Descargar Sitemap.xml".</li>
                <li><strong>Sube el Sitemap a tu Servidor:</strong> Coloca el archivo `sitemap.xml` descargado en el directorio raíz de tu sitio web (ej: `https://www.tusitio.com/sitemap.xml`).</li>
                <li><strong>Envía tu Sitemap a los Motores de Búsqueda:</strong> Informa a Google (a través de Google Search Console) y a otros motores de búsqueda sobre la ubicación de tu sitemap.</li>
            </ol>
        </section>
        <!-- End of Restored Sections -->

        <section class="content-section">
            <h2 id="faq-general-anchor">Preguntas Frecuentes (FAQ)</h2> 
            <div id="faq-container">
                <div class="faq-item"> 
                    <div class="faq-question">¿Para qué sirve el nombre del archivo Sitemap?</div>
                    <div class="faq-answer">
                        <p>Por convención, el archivo de sitemap principal se llama `sitemap.xml` y se coloca en la raíz de tu sitio web (ej: `https://www.tusitio.com/sitemap.xml`). Esto facilita que los motores de búsqueda lo encuentren automáticamente. Puedes cambiar el nombre si tienes una razón específica, pero `sitemap.xml` es el estándar recomendado.</p>
                    </div>
                </div>
                <div class="faq-item" id="faq-url-limit"> 
                    <div class="faq-question">¿Cuántas URLs puedo incluir en un sitemap?</div>
                    <div class="faq-answer">
                        <p>Un solo archivo sitemap puede contener hasta 50,000 URLs y no debe exceder los 50MB sin comprimir. Si tienes más URLs o tu archivo es más grande, necesitarás crear múltiples sitemaps y referenciarlos desde un archivo de índice de sitemaps (un sitemap de sitemaps). Nuestra herramienta actualmente genera un único sitemap; para sitios muy grandes, considera herramientas especializadas o dividir tu listado de URLs.</p>
                    </div>
                <div class="faq-item">
                    <div class="faq-question">¿Qué formato debe tener mi archivo CSV y cómo indico la columna de URLs?</div>
                    <div class="faq-answer">
                        <p>El formato más simple es un archivo de texto plano con extensión `.csv`, donde cada línea contiene una URL. Si tu CSV tiene múltiples columnas (por ejemplo, URL en la primera columna, fecha de modificación en la segunda, etc.), debes usar la opción "Índice Columna URL" para especificar qué columna contiene las URLs. El índice comienza en 0 (cero) para la primera columna, 1 para la segunda, y así sucesivamente. La herramienta intentará omitir la primera fila si parece ser un encabezado.</p>
                        <p>Ejemplo de CSV simple (una columna):</p>
                        <pre style="background-color: #2d2d2d; padding: 10px; border-radius: 4px; color: #e5e7eb; overflow-x: auto;">https://www.ejemplo.com/
https://www.ejemplo.com/pagina1
https://www.ejemplo.com/categoria/pagina2</pre>
                    </div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">¿Qué son `changefreq` y `priority`?</div>
                    <div class="faq-answer">
                        <p><strong>`changefreq` (Frecuencia de Cambio):</strong> Es una pista para los motores de búsqueda sobre la frecuencia con la que es probable que cambie el contenido de una página. Los valores pueden ser: `always`, `hourly`, `daily`, `weekly`, `monthly`, `yearly`, `never`. Es importante notar que los motores de búsqueda pueden usar esta información como una guía, pero no están obligados a rastrear la página con esa frecuencia exacta.</p>
                        <p><strong>`priority` (Prioridad):</strong> Permite indicar la importancia de URLs específicas en relación con otras URLs de tu sitio. El valor va de 0.0 a 1.0 (siendo 1.0 la más alta). La prioridad por defecto es 0.5. Esto ayuda a los buscadores a entender qué páginas consideras más importantes. No afecta tu ranking en comparación con otros sitios, solo la importancia relativa dentro de tu propio sitio.</p>
                    </div>
                </div>
            </div>
        </section>

    </div>
    <?php
    // Incluir el footer común si es necesario, ajusta la ruta según tu estructura
    // if (file_exists('../_partials/footer.php')) {
    //     include '../_partials/footer.php';
    // }
    ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                const answer = item.querySelector('.faq-answer');

                if (question && answer) { // Ensure both elements exist
                    question.addEventListener('click', () => {
                        const isOpen = item.classList.contains('open');

                        // Optional: Close other open FAQs for accordion behavior
                        // faqItems.forEach(otherItem => {
                        //     if (otherItem !== item && otherItem.classList.contains('open')) {
                        //         otherItem.classList.remove('open');
                        //         otherItem.querySelector('.faq-answer').style.display = 'none';
                        //     }
                        // });

                        if (isOpen) {
                            item.classList.remove('open');
                            // answer.style.display = 'none'; // CSS handles this now
                        } else {
                            item.classList.add('open');
                            // answer.style.display = 'block'; // CSS handles this now
                        }
                    });
                }
            });

            // Script for custom file input
            const fileInput = document.getElementById('csvfile_input');
            const fileChosenText = document.getElementById('file-chosen-text');

            if (fileInput) {
                fileInput.addEventListener('change', function(){
                    fileChosenText.textContent = this.files[0] ? this.files[0].name : 'Seleccionar archivo...';
                    fileChosenText.style.color = this.files[0] ? '#FFFFFF' : '#ccc'; // Change text color if file selected
                });
            }
        });
    </script>
</body>
</html>