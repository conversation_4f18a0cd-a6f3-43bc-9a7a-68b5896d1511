<?php
session_start();

// Proteger página
if (!isset($_SESSION['user_id'])) {
    header('Location: /login?error=' . urlencode('Debes iniciar sesión para ver tu historial de facturación.'));
    exit;
}

$pageTitle = 'Historial de facturación | Panel de Usuario';
$pageDescription = 'Consulta tus facturas y pagos anteriores de GSC Inspector.';
$noIndex = true;
require __DIR__ . '/../_partials/header.php';

$userEmail = htmlspecialchars($_SESSION['user_email'] ?? 'Usuario');

?>

<main class="container mx-auto px-4 py-12 pt-24">
    <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold">Historial de facturación</h1>
        <a href="/panel" class="text-primary hover:underline">&larr; Volver al panel</a>
    </div>

    <p class="mb-6 text-lg"><PERSON><PERSON>, <strong class="font-semibold"><?= $userEmail ?></strong>. Aquí puedes consultar tus facturas anteriores.</p>

    <!-- Contenido específico del historial -->
    <section class="bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-700 mb-8">
        <h2 class="text-xl font-semibold mb-4 text-gray-100">Facturas</h2>
        
        <!-- TODO: Cargar historial de facturas desde Paddle o tu sistema -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-700">
                <thead class="bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Fecha</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Descripción</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Importe</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Acciones</th>
                    </tr>
                </thead>
                <tbody class="bg-gray-800 divide-y divide-gray-700">
                    <!-- Ejemplo de fila - Reemplazar con datos reales -->
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">15 Mayo, 2024</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">Suscripción GSC Inspector - Plan Mensual</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300 text-right">9,99 €</td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="#" class="text-primary hover:underline">Ver/Descargar</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">15 Abril, 2024</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">Suscripción GSC Inspector - Plan Mensual</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300 text-right">9,99 €</td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="#" class="text-primary hover:underline">Ver/Descargar</a>
                        </td>
                    </tr>
                    <!-- Fin ejemplo -->
                    
                    <!-- Mensaje si no hay facturas -->
                    <!-- 
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">No hay facturas disponibles.</td>
                    </tr>
                     -->
                </tbody>
            </table>
        </div>
        
        <!-- TODO: Añadir enlace a facturas en portal Paddle si es más sencillo -->
         <p class="text-gray-400 mt-4 text-sm">También puedes consultar y descargar todas tus facturas directamente desde el <a href="#" target="_blank" class="text-primary hover:underline">portal de cliente de Paddle</a>.</p>

    </section>

</main>

<?php 
require __DIR__ . '/../_partials/footer.php';
?> 