/*
Theme Name: Pie<PERSON> Mundo Digital Theme
Theme URI: https://pietramundodigital.com/
Author: Tu Nombre o Agencia
Author URI: https://tuweb.com/
Description: Tema personalizado para Pietra Mundo Digital basado en HTML y Tailwind.
Version: 1.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Tags: custom-background, custom-logo, custom-menu, featured-images, theme-options, translation-ready
Text Domain: pietramundodigital-theme
*/

/* Copia aquí todos los estilos que estaban dentro de las etiquetas <style> en tu HTML */
body {
    font-family: 'Inter', sans-serif;
    background-color: #111111;
    color: #FFFFFF;
    /* scroll-behavior: smooth; */ /* Comentado */
}
h1, h2, h3, h4, h5, h6 {
    font-family: 'Sintony', sans-serif;
}
.bg-dark {
    background-color: #1a1a1a;
}
.bg-darker {
    background-color: #111111;
}
.text-accent {
    color: #34eb95;
}
.bg-accent {
    background-color: #34eb95;
}
.border-accent {
    border-color: #34eb95;
}
.rounded-custom {
    border-radius: 6px;
}
.nav-link {
    position: relative;
}
.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: #34eb95;
    transition: width 0.3s ease;
}
.nav-link:hover::after {
    width: 100%;
}
.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(52, 235, 149, 0.2);
}
.testimonial-card {
    background: linear-gradient(145deg, #1a1a1a, #111111);
    box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.3);
}

/* Nota: Las clases de Tailwind (como py-20, bg-dark, etc.) seguirán funcionando
   si mantienes el enlace CDN de Tailwind en tu header.php.
   Para una integración más profunda, necesitarías un proceso de compilación de Tailwind. */

/* Estilos para el Modal de Cookies */
#cookie-consent-modal {
    /* Puedes ajustar más estilos aquí si es necesario */
    /* La clase 'hidden' de Tailwind lo oculta inicialmente */
}

/* Ajustes menores si son necesarios para botones de cookies */
#cookie-consent-modal .flex-shrink-0 {
  /* Asegura que los botones no se compriman demasiado */
}

/* Efecto fade para la imagen del héroe */
.hero-image-fade {
  /* Crea una máscara radial que es opaca en el centro y transparente en los bordes */
  mask-image: radial-gradient(ellipse at center, black 60%, transparent 100%);
  -webkit-mask-image: radial-gradient(ellipse at center, black 60%, transparent 100%); /* Para compatibilidad con Webkit/Blink */
}

/* Estilos para Páginas Legales */
.legal-content h1 { /* Estilo añadido o ajustado si es necesario */ }
.legal-content h2 { margin-bottom: 1rem; font-size: 1.875rem; line-height: 2.25rem; font-weight: 700; color: #34eb95; /* text-accent */}
.legal-content h3 { margin-top: 1.5rem; margin-bottom: 0.75rem; font-size: 1.25rem; line-height: 1.75rem; font-weight: 700; }
.legal-content p { margin-bottom: 1rem; line-height: 1.625; }
.legal-content ul { margin-bottom: 1rem; list-style: disc; padding-left: 1.5rem; }
.legal-content li { margin-bottom: 0.5rem; }
.legal-content a { color: #34eb95; text-decoration: underline; }
.legal-content table { width: 100%; margin-bottom: 1rem; border-collapse: collapse; }
.legal-content th, .legal-content td { border: 1px solid #4b5563; /* gray-600 */ padding: 0.5rem 0.75rem; text-align: left; }
.legal-content th { background-color: #1f2937; /* gray-800 */ font-weight: bold; }

/* Utilidad para ocultar la barra de scroll (compatible con varios navegadores) */
.scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

/* Estilo para el brillo difuminado en la imagen del héroe */
.hero-image-glow {
    box-shadow: 0 0 15px 5px #34eb95; 
} 