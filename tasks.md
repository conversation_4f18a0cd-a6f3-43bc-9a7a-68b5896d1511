# Plan de Desarrollo IA: Extensión de Chrome para Google Analytics 4

## 1. Tareas y Subtareas (Instrucciones para IA)

### Fase 1: Definición y Diseño del Producto

1.  **PROMPT: Definir Alcance y Funcionalidades Clave (MVP)**
    * **INSTRUCCIÓN 1.1:** Investiga y lista las métricas y dimensiones más relevantes de Google Analytics 4 (GA4) para un análisis rápido. Considera: Usuarios, Sesiones, Tasa de Interacción, Conversiones, Fuentes de Tráfico, Eventos Principales, Vistas por página. `OUTPUT_FORMAT: Lista JSON de métricas y dimensiones con descripción breve.`
    * **INSTRUCCIÓN 1.2:** Determina y especifica qué datos de GA4 se mostrarán: (a) para la URL activa, (b) para la propiedad GA4 seleccionada por el usuario, o (c) una combinación. Justifica la elección. `OUTPUT_FORMAT: Documento de especificación.`
    * **INSTRUCCIÓN 1.3:** Define el alcance inicial para la visualización de datos (periodos de tiempo por defecto, ej., "hoy", "últimos 7 días", "últimos 28 días"). `OUTPUT_FORMAT: Lista de periodos de tiempo por defecto.`
    * **INSTRUCCIÓN 1.4:** Genera un borrador de las funcionalidades para la primera versión (MVP) de la extensión. `OUTPUT_FORMAT: Lista de funcionalidades del MVP.`
2.  **PROMPT: Diseñar Arquitectura del Sistema**
    * **INSTRUCCIÓN 2.1:** Especifica el flujo de comunicación detallado entre: popup de la extensión, script de fondo (background script, si es necesario), API de Datos de GA4, API de Administración de GA4, y el backend propio. Incluye diagramas de secuencia si es posible. `OUTPUT_FORMAT: Documento de arquitectura con diagramas.`
    * **INSTRUCCIÓN 2.2:** Propón una estructura de archivos y carpetas para el frontend de la extensión, similar a la extensión de GSC analizada (`popup.html`, `popup.css`, `popup.js`, `api.js`, `auth.js`, `ui.js`, `features.js`, `state.js`, `utils.js`, `config.js`, `locales/`). `OUTPUT_FORMAT: Estructura de directorios en formato árbol.`
    * **INSTRUCCIÓN 2.3:** Diseña la arquitectura para el backend. Especifica: lenguaje/framework (ej. Node.js/Express, Python/Flask), estructura de la API (endpoints, métodos HTTP, formato de request/response). `OUTPUT_FORMAT: Especificación de API del backend.`
3.  **PROMPT: Diseñar Interfaz de Usuario (UI) y Experiencia de Usuario (UX)**
    * **INSTRUCCIÓN 3.1:** Genera wireframes detallados para el popup de la extensión, incluyendo todas las secciones y controles definidos en el MVP. `OUTPUT_FORMAT: Serie de imágenes (wireframes) o archivo de Figma/Sketch.`
    * **INSTRUCCIÓN 3.2:** Diseña el flujo de autenticación del usuario con Google OAuth 2.0, mostrando cada paso en la UI. `OUTPUT_FORMAT: Diagrama de flujo y mockups de UI.`
    * **INSTRUCCIÓN 3.3:** Diseña la UI y el flujo para que el usuario seleccione la cuenta, propiedad y flujo de datos de GA4 a analizar. `OUTPUT_FORMAT: Diagrama de flujo y mockups de UI.`
    * **INSTRUCCIÓN 3.4:** Especifica cómo se presentarán los datos de GA4 (tablas, gráficos sencillos, KPIs). Proporciona ejemplos visuales. `OUTPUT_FORMAT: Mockups de UI para la visualización de datos.`
4.  **PROMPT: Definir Modelo de Datos y Almacenamiento**
    * **INSTRUCCIÓN 4.1:** Detalla las consultas específicas (métricas, dimensiones, filtros, rangos de fechas) que se realizarán a la API de Datos de GA4. `OUTPUT_FORMAT: Lista de ejemplos de cuerpos de solicitud JSON para la API de GA4.`
    * **INSTRUCCIÓN 4.2:** Diseña el esquema de la base de datos del backend. Incluye tablas para `usuarios` (con campos para google_id, email, plan_id, usage_count, tokens de acceso/refresco encriptados) y `planes` (con campos para nombre del plan, límites, características). `OUTPUT_FORMAT: Esquema SQL DDL o descripción detallada del esquema JSON para NoSQL.`
    * **INSTRUCCIÓN 4.3:** Especifica qué datos se almacenarán en `chrome.storage.local` (ej. token de acceso de Google, ID de propiedad GA4 seleccionada, preferencias de usuario, datos de GA4 cacheados) y la estrategia de caché (duración, invalidación). `OUTPUT_FORMAT: Documento de especificación de almacenamiento local.`
5.  **PROMPT: Definir Tiers (Planes) y Limitaciones**
    * **INSTRUCCIÓN 5.1:** Define un plan "Gratuito". Especifica: número de consultas a la API de GA4 permitidas (ej. por día/mes), funcionalidades básicas incluidas, funcionalidades restringidas. `OUTPUT_FORMAT: Descripción del plan Gratuito.`
    * **INSTRUCCIÓN 5.2:** Define al menos un plan "Premium". Especifica: precio (hipotético), número de consultas a la API (o ilimitado), funcionalidades avanzadas (ej. comparativas de fechas, filtros avanzados, más métricas, exportación de datos). `OUTPUT_FORMAT: Descripción del plan Premium.`
    * **INSTRUCCIÓN 5.3:** Detalla la lógica del backend para gestionar y verificar estos tiers. Incluye cómo se rastreará el uso y cómo se aplicarán las restricciones. `OUTPUT_FORMAT: Diagrama de flujo o pseudocódigo.`
6.  **PROMPT: Investigar API de Google Analytics 4**
    * **INSTRUCCIÓN 6.1:** Revisa la documentación de la [API de Datos de Google Analytics](https://developers.google.com/analytics/devguides/reporting/data/v1) y la [API de Administración de Google Analytics](https://developers.google.com/analytics/devguides/config/admin/v1). Extrae los endpoints clave que se utilizarán. `OUTPUT_FORMAT: Lista de endpoints y su propósito.`
    * **INSTRUCCIÓN 6.2:** Confirma los scopes de OAuth 2.0 necesarios. El scope principal será `https://www.googleapis.com/auth/analytics.readonly`. Verifica si se necesitan otros para la API de Administración. `OUTPUT_FORMAT: Lista de scopes OAuth 2.0.`
    * **INSTRUCCIÓN 6.3:** Resume las cuotas y límites de uso de las APIs de GA4 (Data y Admin). `OUTPUT_FORMAT: Resumen de cuotas y límites.`

### Fase 2: Configuración del Entorno de Desarrollo

1.  **PROMPT: Crear Proyecto de Extensión**
    * **INSTRUCCIÓN 1.1:** Genera la estructura de carpetas base para la extensión. Crea un archivo `manifest.json` inicial con los permisos y configuraciones mínimas. `OUTPUT_FORMAT: Estructura de directorios y contenido del manifest.json inicial.`
2.  **PROMPT: Configurar Herramientas de Desarrollo**
    * **INSTRUCCIÓN 2.1:** Especifica la configuración recomendada para VS Code (extensiones útiles para desarrollo web y JavaScript). `OUTPUT_FORMAT: Lista de extensiones de VS Code y configuraciones JSON.`
    * **INSTRUCCIÓN 2.2:** Genera archivos de configuración base para ESLint y Prettier con reglas comunes para JavaScript. `OUTPUT_FORMAT: Archivos `.eslintrc.json` y `.prettierrc.json`.`
3.  **PROMPT: Configurar Control de Versiones**
    * **INSTRUCCIÓN 3.1:** Proporciona los comandos Git para inicializar un repositorio. `OUTPUT_FORMAT: Comandos Git.`
    * **INSTRUCCIÓN 3.2:** Proporciona los comandos Git para conectar a un repositorio remoto (ej. GitHub). `OUTPUT_FORMAT: Comandos Git.`

### Fase 3: Desarrollo del Frontend

1.  **PROMPT: Generar Estructura HTML y CSS del Popup**
    * **INSTRUCCIÓN 1.1:** Genera el código HTML para `popup.html` basado en los wireframes de la Fase 1. Incluye todos los elementos de la UI (contenedores, botones, selectores, áreas para datos). `OUTPUT_FORMAT: Código HTML completo.`
    * **INSTRUCCIÓN 1.2:** Genera el código CSS básico para `popup.css` para dar estilo inicial a los elementos de `popup.html`. `OUTPUT_FORMAT: Código CSS.`
2.  **PROMPT: Implementar Autenticación con Google (OAuth 2.0)**
    * **INSTRUCCIÓN 2.1:** Desarrolla el script `auth.js`. Implementa la función `getAuthToken(interactive)` usando `chrome.identity.getAuthToken`. Maneja errores y la obtención del token. `OUTPUT_FORMAT: Código JavaScript (`auth.js`).`
    * **INSTRUCCIÓN 2.2:** Actualiza el `manifest.json` con los scopes OAuth 2.0 correctos para GA4 y el `client_id` (usar un placeholder para el client_id). `OUTPUT_FORMAT: JSON (`manifest.json` actualizado).`
    * **INSTRUCCIÓN 2.3:** Implementa funciones en `auth.js` para almacenar el token de forma segura (ej. en `chrome.storage.local` o gestionado por el backend) y para revocarlo/limpiarlo al cerrar sesión. `OUTPUT_FORMAT: Código JavaScript (funciones adicionales en `auth.js`).`
3.  **PROMPT: Desarrollar Lógica de Interfaz de Usuario (`ui.js`, `popup.js`)**
    * **INSTRUCCIÓN 3.1:** En `ui.js`, crea funciones para: `showLoading()`, `hideLoading()`, `showError(message)`, `showResultsSection()`, `showAuthSection()`. `OUTPUT_FORMAT: Código JavaScript (`ui.js`).`
    * **INSTRUCCIÓN 3.2:** En `popup.js`, implementa la lógica para inicializar la extensión, verificar el estado de autenticación, y llamar a funciones de `ui.js` para mostrar la sección apropiada. `OUTPUT_FORMAT: Código JavaScript (`popup.js`).`
    * **INSTRUCCIÓN 3.3:** En `ui.js`, desarrolla la función para poblar dinámicamente los selectores de cuenta, propiedad y flujo de datos de GA4. Esta función recibirá datos de la API. `OUTPUT_FORMAT: Código JavaScript (función en `ui.js`).`
    * **INSTRUCCIÓN 3.4:** En `ui.js`, implementa la lógica para manejar los selectores de rango de fechas y actualizar la UI cuando cambien. `OUTPUT_FORMAT: Código JavaScript (funciones en `ui.js`).`
    * **INSTRUCCIÓN 3.5:** En `ui.js`, crea funciones para renderizar los datos de GA4 recibidos en tablas y para preparar los datos para los gráficos. `OUTPUT_FORMAT: Código JavaScript (funciones en `ui.js`).`
4.  **PROMPT: Implementar Conexión con API de GA4 (`api.js`)**
    * **INSTRUCCIÓN 4.1:** En `api.js`, crea una función `fetchGA4AccountSummaries(accessToken)` que use la API de Administración de GA4 para listar cuentas, propiedades y flujos de datos a los que el usuario tiene acceso. `OUTPUT_FORMAT: Código JavaScript (función en `api.js`).`
    * **INSTRUCCIÓN 4.2:** En `api.js`, crea una función `runGA4Report(accessToken, propertyId, reportRequest)` que tome un `propertyId` y un objeto de solicitud de informe (métricas, dimensiones, dateRanges, dimensionFilter) y devuelva los datos de la API de Datos de GA4. `OUTPUT_FORMAT: Código JavaScript (función en `api.js`).`
    * **INSTRUCCIÓN 4.3:** Implementa manejo de errores robusto para las llamadas a la API, incluyendo errores de autenticación (401/403) y errores de cuota (429). `OUTPUT_FORMAT: Código JavaScript (manejo de errores en `api.js`).`
    * **INSTRUCCIÓN 4.4:** Implementa funciones `getCachedGA4Data(cacheKey)` y `setCachedGA4Data(cacheKey, data, durationInMs)` en `api.js` usando `chrome.storage.local`. `OUTPUT_FORMAT: Código JavaScript (funciones de caché en `api.js`).`
5.  **PROMPT: Desarrollar Visualización de Datos (`features.js`)**
    * **INSTRUCCIÓN 5.1:** En `features.js`, crea una función `processGA4DataForDisplay(apiResponse)` que transforme la respuesta de la API de GA4 en un formato fácil de usar para la UI. `OUTPUT_FORMAT: Código JavaScript (función en `features.js`).`
    * **INSTRUCCIÓN 5.2:** En `features.js`, implementa una función `renderTrendChart(canvasId, data, chartOptions)` usando Chart.js para mostrar tendencias de métricas clave. `OUTPUT_FORMAT: Código JavaScript (función en `features.js`).`
    * **INSTRUCCIÓN 5.3:** En `features.js`, crea una función `renderDataTable(tableElementId, data, columns)` para mostrar datos detallados en una tabla HTML. `OUTPUT_FORMAT: Código JavaScript (función en `features.js`).`
6.  **PROMPT: Implementar Internacionalización (i18n)**
    * **INSTRUCCIÓN 6.1:** Genera el script `i18n.js` con funciones `loadLanguage(lang)`, `t(key, params)`, e `initI18n()`. `OUTPUT_FORMAT: Código JavaScript (`i18n.js`).`
    * **INSTRUCCIÓN 6.2:** Crea archivos `locales/es.json` y `locales/en.json` con algunas claves de ejemplo para la UI (título de la extensión, botones). `OUTPUT_FORMAT: Archivos JSON.`
    * **INSTRUCCIÓN 6.3:** Modifica `popup.html` y `popup.js`/`ui.js` para usar atributos `data-i18n` y la función `t()` para todas las cadenas de texto visibles. `OUTPUT_FORMAT: Código HTML y JavaScript actualizados.`
7.  **PROMPT: Desarrollar Manejo de Estado y Utilidades**
    * **INSTRUCCIÓN 7.1:** Crea el archivo `state.js` y define variables globales iniciales (ej. `activeTabUrl = null`, `selectedPropertyId = null`, `authToken = null`, `isLoading = false`). Incluye una función `initializeDOMReferences()` para obtener referencias a elementos clave del DOM. `OUTPUT_FORMAT: Código JavaScript (`state.js`).`
    * **INSTRUCCIÓN 7.2:** Crea el archivo `utils.js`. Incluye funciones para formatear fechas (ej. `formatDateForAPI(dateObj)`, `formatDateForDisplay(dateObj)`), y cualquier otra función de ayuda general. `OUTPUT_FORMAT: Código JavaScript (`utils.js`).`
8.  **PROMPT: Implementar Identificación de URL Activa**
    * **INSTRUCCIÓN 8.1:** En `popup.js` o `utils.js`, crea una función `getCurrentTabUrl()` que use `chrome.tabs.query({active: true, currentWindow: true})` para obtener la URL de la pestaña activa. `OUTPUT_FORMAT: Código JavaScript (función).`
    * **INSTRUCCIÓN 8.2:** (Opcional) Si se requieren datos específicos de la página actual, modifica `runGA4Report` en `api.js` para aceptar un `pagePathFilter` y añadirlo al `dimensionFilter` de la solicitud a la API de GA4. `OUTPUT_FORMAT: Código JavaScript (función `runGA4Report` actualizada).`

### Fase 4: Desarrollo del Backend y Base de Datos

1.  **PROMPT: Seleccionar Stack Tecnológico Backend y BD**
    * **INSTRUCCIÓN 1.1:** Selecciona un lenguaje/framework para el backend (ej. Node.js con Express.js). Justifica la elección. `OUTPUT_FORMAT: Documento de justificación.`
    * **INSTRUCCIÓN 1.2:** Selecciona un motor de base de datos (ej. Firebase Firestore por su integración con Google y escalabilidad serverless). Justifica la elección. `OUTPUT_FORMAT: Documento de justificación.`
2.  **PROMPT: Diseñar y Desarrollar API del Backend**
    * **INSTRUCCIÓN 2.1:** Implementa el endpoint `/verify-token` (POST). Debe:
        1.  Recibir un `accessToken` de Google.
        2.  Validar el token con Google.
        3.  Obtener el `google_id` y `email` del usuario.
        4.  Buscar/crear usuario en la BD.
        5.  Devolver `userId`, `email`, `plan` (ej. "Gratuito", "Premium"), `usageCount`, `usageLimit`.
        `OUTPUT_FORMAT: Código del endpoint (ej. Node.js/Express).`
    * **INSTRUCCIÓN 2.2:** Implementa el endpoint `/confirm-usage` (POST, protegido por token de sesión/API key propio). Debe:
        1.  Identificar al usuario.
        2.  Si es plan gratuito, incrementar `usage_count`.
        3.  Verificar si `usage_count` excede `usageLimit`.
        4.  Devolver el nuevo `usage_count` y `limitReached: true/false`.
        `OUTPUT_FORMAT: Código del endpoint.`
    * **INSTRUCCIÓN 2.3:** (Opcional) Define los endpoints necesarios para la integración con un sistema de pagos como Stripe (ej. `/create-checkout-session`, `/stripe-webhook`). `OUTPUT_FORMAT: Especificación de API para pagos.`
3.  **PROMPT: Configurar y Desarrollar Base de Datos**
    * **INSTRUCCIÓN 3.1:** Genera el código (ej. scripts de migración o configuración de Firestore) para crear las colecciones/tablas `users` y `plans` según el esquema definido en la Fase 1. `OUTPUT_FORMAT: Scripts de configuración de BD.`
    * **INSTRUCCIÓN 3.2:** Implementa funciones CRUD básicas para la gestión de usuarios y planes en el backend. `OUTPUT_FORMAT: Código de funciones CRUD.`
4.  **PROMPT: Implementar Lógica de Tiers y Limitaciones en Backend**
    * **INSTRUCCIÓN 4.1:** Implementa un middleware o decorador en el backend para verificar el plan del usuario y los permisos antes de acceder a endpoints protegidos o que consumen cuota. `OUTPUT_FORMAT: Código del middleware/decorador.`
    * **INSTRUCCIÓN 4.2:** Asegúrate de que el endpoint `/confirm-usage` actualiza correctamente `usage_count` y respeta `usageLimit`. `OUTPUT_FORMAT: Pruebas o verificación de lógica.`
    * **INSTRUCCIÓN 4.3:** Implementa una tarea programada (cron job) o lógica para resetear `usage_count` al inicio de cada periodo de facturación (ej. mensualmente). `OUTPUT_FORMAT: Descripción de la tarea programada o pseudocódigo.`
5.  **PROMPT: Integrar Sistema de Pagos (si aplica)**
    * **INSTRUCCIÓN 5.1:** Genera el código de integración básico para Stripe (o similar) para crear sesiones de checkout y manejar suscripciones. `OUTPUT_FORMAT: Fragmentos de código de integración de pagos.`
    * **INSTRUCCIÓN 5.2:** Implementa el webhook para recibir eventos de Stripe (ej. `checkout.session.completed`, `customer.subscription.updated`) y actualizar el plan del usuario en la BD. `OUTPUT_FORMAT: Código del manejador de webhooks.`

### Fase 5: Integración, Pruebas y Depuración

1.  **PROMPT: Generar Pruebas Unitarias**
    * **INSTRUCCIÓN 1.1:** Escribe pruebas unitarias para funciones clave en `utils.js` (ej. formateo de fechas), `api.js` (simulando respuestas de API), y lógica de cálculo en el backend (ej. verificación de límites). `OUTPUT_FORMAT: Código de pruebas (ej. Jest).`
2.  **PROMPT: Definir Casos de Prueba de Integración**
    * **INSTRUCCIÓN 2.1:** Define casos de prueba para el flujo completo: Autenticación -> Selección de propiedad GA4 -> Solicitud de datos -> Visualización en UI. `OUTPUT_FORMAT: Lista de casos de prueba.`
    * **INSTRUCCIÓN 2.2:** Define casos de prueba para la comunicación Frontend <-> Backend: Verificación de token, actualización de contador de uso, bloqueo por límite alcanzado. `OUTPUT_FORMAT: Lista de casos de prueba.`
3.  **PROMPT: Definir Casos de Prueba de UI/UX**
    * **INSTRUCCIÓN 3.1:** Lista los escenarios a probar para la usabilidad general en Chrome. `OUTPUT_FORMAT: Lista de escenarios de prueba de UI/UX.`
    * **INSTRUCCIÓN 3.2:** Especifica cómo verificar la correcta visualización de datos y gráficos para diferentes conjuntos de datos (vacíos, pequeños, grandes). `OUTPUT_FORMAT: Criterios de aceptación visual.`
    * **INSTRUCCIÓN 3.3:** Define pruebas para el cambio de idioma y la correcta aplicación de las traducciones. `OUTPUT_FORMAT: Casos de prueba de i18n.`
4.  **PROMPT: Definir Casos de Prueba de Tiers y Limitaciones**
    * **INSTRUCCIÓN 4.1:** Detalla cómo probar que las limitaciones del plan gratuito (funcionalidades restringidas, límite de API) se aplican correctamente. `OUTPUT_FORMAT: Casos de prueba para plan gratuito.`
    * **INSTRUCCIÓN 4.2:** Detalla cómo probar el acceso a funcionalidades premium y el comportamiento sin límites (o con límites más altos) con una cuenta de prueba premium. `OUTPUT_FORMAT: Casos de prueba para plan premium.`
5.  **PROMPT: Estrategia de Depuración**
    * **INSTRUCCIÓN 5.1:** Lista las herramientas y técnicas a utilizar para la depuración del frontend (Chrome DevTools: Console, Network, Sources, Application). `OUTPUT_FORMAT: Guía de depuración frontend.`
    * **INSTRUCCIÓN 5.2:** Lista las herramientas y técnicas para la depuración del backend (logs del servidor, debuggers del lenguaje/framework). `OUTPUT_FORMAT: Guía de depuración backend.`

### Fase 6: Despliegue

1.  **PROMPT: Plan de Despliegue del Backend**
    * **INSTRUCCIÓN 1.1:** Selecciona una plataforma de despliegue para el backend (ej. Google Cloud Run). Proporciona los pasos de configuración y despliegue. `OUTPUT_FORMAT: Guía de despliegue para la plataforma seleccionada.`
    * **INSTRUCCIÓN 1.2:** Detalla cómo desplegar la base de datos y asegurar su conexión con el backend en producción. `OUTPUT_FORMAT: Guía de despliegue de BD.`
    * **INSTRUCCIÓN 1.3:** Especifica los pasos para configurar un dominio personalizado y un certificado SSL para la API del backend. `OUTPUT_FORMAT: Instrucciones de configuración de dominio/SSL.`
2.  **PROMPT: Proceso de Empaquetado y Publicación de la Extensión**
    * **INSTRUCCIÓN 2.1:** Proporciona el comando o los pasos para empaquetar la extensión en un archivo `.zip`. `OUTPUT_FORMAT: Instrucciones de empaquetado.`
    * **INSTRUCCIÓN 2.2:** Lista los requisitos y pasos para crear una cuenta de desarrollador en la Chrome Web Store. `OUTPUT_FORMAT: Guía de registro en CWS.`
    * **INSTRUCCIÓN 2.3:** Genera un borrador del texto para la descripción, y una lista de capturas de pantalla necesarias para la ficha de la extensión en la CWS. `OUTPUT_FORMAT: Borrador de contenido para CWS.`
    * **INSTRUCCIÓN 2.4:** Describe el proceso de envío y revisión de la extensión en la CWS. `OUTPUT_FORMAT: Resumen del proceso de revisión.`
    * **INSTRUCCIÓN 2.5:** Genera una plantilla básica para la página de política de privacidad de la extensión. `OUTPUT_FORMAT: Plantilla de Política de Privacidad (Markdown o HTML).`

### Fase 7: Post-Lanzamiento: Mantenimiento y Evolución

1.  **PROMPT: Plan de Monitorización y Soporte**
    * **INSTRUCCIÓN 1.1:** Recomienda herramientas para monitorizar errores en el frontend y backend (ej. Sentry, Google Cloud Logging). `OUTPUT_FORMAT: Lista de herramientas de monitorización.`
    * **INSTRUCCIÓN 1.2:** Define un proceso para recoger y gestionar el feedback de los usuarios y las reseñas de la CWS. `OUTPUT_FORMAT: Descripción del proceso de feedback.`
2.  **PROMPT: Estrategia de Actualizaciones**
    * **INSTRUCCIÓN 2.1:** Describe un flujo de trabajo para la corrección de bugs (reporte -> priorización -> corrección -> prueba -> despliegue). `OUTPUT_FORMAT: Diagrama de flujo de corrección de bugs.`
    * **INSTRUCCIÓN 2.2:** Define cómo se priorizarán y desarrollarán nuevas funcionalidades. `OUTPUT_FORMAT: Proceso de gestión de hoja de ruta.`
    * **INSTRUCCIÓN 2.3:** Establece un plan para monitorizar cambios en las APIs de Chrome y GA4 y adaptar la extensión. `OUTPUT_FORMAT: Plan de mantenimiento de compatibilidad.`
3.  **PROMPT: Ideas para Marketing y Crecimiento**
    * **INSTRUCCIÓN 3.1:** Genera una lista de posibles canales y estrategias para promocionar la extensión. `OUTPUT_FORMAT: Lista de ideas de marketing.`