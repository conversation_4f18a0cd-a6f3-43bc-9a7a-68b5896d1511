<?php
// config.php
define('DB_HOST', 'localhost'); // Hostinger generalmente usa localhost para conexiones desde el mismo servidor
define('DB_USER', 'u152603351_db_pmd'); // Usuario proporcionado
define('DB_PASS', 'Parqueroca123!'); // Contraseña proporcionada
define('DB_NAME', 'u152603351_db_pmd'); // Nombre de BD proporcionado

// ¡IMPORTANTE! Obtén este Client ID de tu proyecto en Google Cloud Console.

// Client ID para la Aplicación de Chrome (Extensión) - Usado en popup.js y verify.php
define('GOOGLE_CLIENT_ID_EXTENSION', '37136391139-llu00lsfic3o3c4ocon6e78t2n9tlgr5.apps.googleusercontent.com');

// Client ID para la Aplicación Web - Usado en login.php y verify_google_web.php
define('GOOGLE_CLIENT_ID_WEB', '37136391139-mpciacvvb3mn7tdtfea58b5dmm4sv0vc.apps.googleusercontent.com');

// Límite de inspecciones para usuarios gratuitos (puedes ajustarlo)
define('FREE_PLAN_LIMIT', 30);

// Límites para planes de pago
define('PERSONAL_PLAN_LIMIT', 120);
define('PLUS_PLAN_LIMIT', 360);
define('AGENCY_PLAN_LIMIT', 1200);

// URL base de tu API (útil si necesitas generar URLs completas)
// define('API_BASE_URL', 'https://tu-dominio.com/api');

// --- Configuración de Stripe ---
// Intenta obtener las claves desde variables de entorno, usa fallbacks si no existen
// ¡Los fallbacks aquí son solo EJEMPLOS para desarrollo, NUNCA uses claves reales aquí en producción!
define('STRIPE_SECRET_KEY', getenv('STRIPE_SECRET_KEY') ?: 'sk_test_YOUR_FALLBACK_SECRET_KEY');
define('STRIPE_PUBLIC_KEY', getenv('STRIPE_PUBLIC_KEY') ?: 'pk_test_YOUR_FALLBACK_PUBLIC_KEY');
define('STRIPE_WEBHOOK_SECRET', getenv('STRIPE_WEBHOOK_SECRET') ?: 'whsec_YOUR_FALLBACK_WEBHOOK_SECRET');

// IDs de los Precios (Products -> Pricing) en tu dashboard de Stripe
// Asegúrate de que los nombres ('PERSONAL', 'PLUS', etc.) coincidan con tus planes internos
define('STRIPE_PRICE_ID_PERSONAL', getenv('STRIPE_PRICE_ID_PERSONAL') ?: 'price_YOUR_PERSONAL_PLAN_PRICE_ID');
define('STRIPE_PRICE_ID_PLUS', getenv('STRIPE_PRICE_ID_PLUS') ?: 'price_YOUR_PLUS_PLAN_PRICE_ID');
define('STRIPE_PRICE_ID_AGENCY', getenv('STRIPE_PRICE_ID_AGENCY') ?: 'price_YOUR_AGENCY_PLAN_PRICE_ID');
// Añade aquí otros Price IDs si tienes más planes (ej. anuales)
// define('STRIPE_PRICE_ID_PERSONAL_YEARLY', getenv('STRIPE_PRICE_ID_PERSONAL_YEARLY') ?: 'price_...');

// --- Fin Configuración ---

?>
