<?php
// db.php
require_once __DIR__ . '/config.php'; // Incluir config desde el mismo directorio

/**
 * Obtiene una instancia de la conexión PDO a la base de datos.
 *
 * Utiliza un patrón singleton estático para evitar múltiples conexiones.
 *
 * @return PDO|null Retorna el objeto PDO en caso de éxito, null en caso de error.
 */
function getDbConnection(): ?PDO {
    static $conn = null;
    if ($conn === null) {
        try {
            // Construir el DSN (Data Source Name)
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            
            // Opciones de PDO
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION, // Lanzar excepciones en errores
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,       // Devolver resultados como arrays asociativos
                PDO::ATTR_EMULATE_PREPARES   => false,                  // Usar preparaciones nativas
            ];

            // Crear la instancia de PDO
            $conn = new PDO($dsn, DB_USER, DB_PASS, $options);

        } catch (PDOException $e) {
            // En un entorno de producción, deberías loguear este error
            // en lugar de simplemente "morir". Nunca muestres detalles del error al usuario final.
            error_log("Error de conexión a la BD: " . $e->getMessage());
            
            // Para la API, devolver un error JSON sería más apropiado que die()
            // si este script fuera directamente un endpoint, pero como es una función
            // de utilidad, retornar null permite al llamador manejar el error.
             return null; 
            // O podrías lanzar una excepción personalizada:
            // throw new RuntimeException("No se pudo conectar a la base de datos.");
        }
    }
    return $conn;
}

?>
