<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\WebhookEvent;
use App\Services\StripeService;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessStripeWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 60;

    /**
     * The webhook event to process.
     *
     * @var WebhookEvent
     */
    protected WebhookEvent $webhookEvent;

    /**
     * Create a new job instance.
     */
    public function __construct(WebhookEvent $webhookEvent)
    {
        $this->webhookEvent = $webhookEvent;
    }

    /**
     * Execute the job.
     */
    public function handle(StripeService $stripeService): void
    {
        Log::info('Processing Stripe webhook job', [
            'webhook_id' => $this->webhookEvent->id,
            'stripe_event_id' => $this->webhookEvent->stripe_event_id,
            'event_type' => $this->webhookEvent->type,
            'attempt' => $this->attempts()
        ]);

        try {
            // Update webhook status to processing
            $this->webhookEvent->update([
                'status' => 'processing',
                'attempts' => $this->attempts()
            ]);

            // Process the webhook event
            $stripeService->handleWebhookEvent($this->webhookEvent->data);

            // Mark as processed
            $this->webhookEvent->update([
                'status' => 'processed',
                'processed_at' => now(),
                'error_message' => null
            ]);

            Log::info('Stripe webhook processed successfully', [
                'webhook_id' => $this->webhookEvent->id,
                'stripe_event_id' => $this->webhookEvent->stripe_event_id
            ]);

        } catch (Exception $e) {
            Log::error('Stripe webhook processing failed', [
                'webhook_id' => $this->webhookEvent->id,
                'stripe_event_id' => $this->webhookEvent->stripe_event_id,
                'attempt' => $this->attempts(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Update webhook with error
            $this->webhookEvent->update([
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'attempts' => $this->attempts()
            ]);

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Stripe webhook job failed permanently', [
            'webhook_id' => $this->webhookEvent->id,
            'stripe_event_id' => $this->webhookEvent->stripe_event_id,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage()
        ]);

        // Mark webhook as permanently failed
        $this->webhookEvent->update([
            'status' => 'failed',
            'error_message' => 'Job failed after ' . $this->tries . ' attempts: ' . $exception->getMessage(),
            'attempts' => $this->tries
        ]);

        // Send notification to administrators
        $this->notifyAdministrators($exception);
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 120, 300]; // 30 seconds, 2 minutes, 5 minutes
    }

    /**
     * Determine if the job should be retried based on the exception.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(2); // Stop retrying after 2 hours
    }

    /**
     * Notify administrators of webhook failure.
     */
    protected function notifyAdministrators(Exception $exception): void
    {
        try {
            // Send notification via configured channels (Slack, email, etc.)
            $message = "Stripe webhook processing failed permanently:\n" .
                      "Event ID: {$this->webhookEvent->stripe_event_id}\n" .
                      "Event Type: {$this->webhookEvent->type}\n" .
                      "Error: {$exception->getMessage()}";

            // Log for now - implement actual notifications as needed
            Log::critical('Webhook processing failed permanently', [
                'webhook_id' => $this->webhookEvent->id,
                'stripe_event_id' => $this->webhookEvent->stripe_event_id,
                'event_type' => $this->webhookEvent->type,
                'error' => $exception->getMessage(),
                'notification_message' => $message
            ]);

            // TODO: Implement actual notification sending
            // - Send Slack notification
            // - Send email to administrators
            // - Create internal alert/ticket

        } catch (Exception $e) {
            Log::error('Failed to notify administrators of webhook failure', [
                'original_error' => $exception->getMessage(),
                'notification_error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'stripe',
            'webhook',
            $this->webhookEvent->type,
            'event:' . $this->webhookEvent->stripe_event_id
        ];
    }
}
