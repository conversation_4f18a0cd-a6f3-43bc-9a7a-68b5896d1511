<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WebhookEvent extends Model
{
    use HasFactory;

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'stripe_event_id',
        'type',
        'object_id',
        'object_type',
        'data',
        'status',
        'error_message',
        'attempts',
        'processed_at',
        'created_at'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'data' => 'array',
        'attempts' => 'integer',
        'processed_at' => 'datetime',
        'created_at' => 'datetime'
    ];

    /**
     * The attributes that should be mutated to dates.
     */
    protected $dates = [
        'processed_at',
        'created_at'
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->created_at) {
                $model->created_at = now();
            }
        });
    }

    /**
     * Check if the event is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the event is processed.
     */
    public function isProcessed(): bool
    {
        return $this->status === 'processed';
    }

    /**
     * Check if the event failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get the event data object.
     */
    public function getEventObject(): ?array
    {
        return $this->data['data']['object'] ?? null;
    }

    /**
     * Get the event API version.
     */
    public function getApiVersion(): ?string
    {
        return $this->data['api_version'] ?? null;
    }

    /**
     * Get the event request ID.
     */
    public function getRequestId(): ?string
    {
        return $this->data['request']['id'] ?? null;
    }

    /**
     * Get the event idempotency key.
     */
    public function getIdempotencyKey(): ?string
    {
        return $this->data['request']['idempotency_key'] ?? null;
    }

    /**
     * Get human readable event type.
     */
    public function getHumanTypeAttribute(): string
    {
        $types = [
            'customer.subscription.created' => 'Suscripción creada',
            'customer.subscription.updated' => 'Suscripción actualizada',
            'customer.subscription.deleted' => 'Suscripción cancelada',
            'invoice.payment_succeeded' => 'Pago exitoso',
            'invoice.payment_failed' => 'Pago fallido',
            'invoice.created' => 'Factura creada',
            'invoice.updated' => 'Factura actualizada',
            'payment_method.attached' => 'Método de pago agregado',
            'payment_method.detached' => 'Método de pago removido',
            'customer.created' => 'Cliente creado',
            'customer.updated' => 'Cliente actualizado'
        ];

        return $types[$this->type] ?? $this->type;
    }

    /**
     * Get status in Spanish.
     */
    public function getStatusInSpanishAttribute(): string
    {
        $statuses = [
            'pending' => 'Pendiente',
            'processed' => 'Procesado',
            'failed' => 'Fallido'
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Scope to only include pending events.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to only include processed events.
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', 'processed');
    }

    /**
     * Scope to only include failed events.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to only include events of specific type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to only include events for specific object.
     */
    public function scopeForObject($query, string $objectId, string $objectType = null)
    {
        $query = $query->where('object_id', $objectId);
        
        if ($objectType) {
            $query->where('object_type', $objectType);
        }
        
        return $query;
    }

    /**
     * Scope to only include recent events.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Convert the model instance to an array for API responses.
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'stripe_event_id' => $this->stripe_event_id,
            'type' => $this->type,
            'human_type' => $this->human_type,
            'object_id' => $this->object_id,
            'object_type' => $this->object_type,
            'status' => $this->status,
            'status_spanish' => $this->status_in_spanish,
            'error_message' => $this->error_message,
            'attempts' => $this->attempts,
            'api_version' => $this->getApiVersion(),
            'request_id' => $this->getRequestId(),
            'processed_at' => $this->processed_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'is_pending' => $this->isPending(),
            'is_processed' => $this->isProcessed(),
            'is_failed' => $this->isFailed()
        ];
    }
}
