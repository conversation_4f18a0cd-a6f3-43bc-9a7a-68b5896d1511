<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Invoice extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'subscription_id',
        'stripe_invoice_id',
        'stripe_customer_id',
        'stripe_subscription_id',
        'number',
        'status',
        'amount_due',
        'amount_paid',
        'amount_remaining',
        'subtotal',
        'tax',
        'total',
        'currency',
        'description',
        'hosted_invoice_url',
        'invoice_pdf',
        'period_start',
        'period_end',
        'due_date',
        'paid_at',
        'line_items',
        'metadata'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'amount_due' => 'decimal:2',
        'amount_paid' => 'decimal:2',
        'amount_remaining' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'tax' => 'decimal:2',
        'total' => 'decimal:2',
        'period_start' => 'datetime',
        'period_end' => 'datetime',
        'due_date' => 'datetime',
        'paid_at' => 'datetime',
        'line_items' => 'array',
        'metadata' => 'array'
    ];

    /**
     * Get the user that owns the invoice.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription associated with the invoice.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Check if the invoice is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Check if the invoice is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === 'open' && 
               $this->due_date && 
               $this->due_date->isPast();
    }

    /**
     * Check if the invoice is void.
     */
    public function isVoid(): bool
    {
        return $this->status === 'void';
    }

    /**
     * Get formatted total with currency.
     */
    public function getFormattedTotalAttribute(): string
    {
        return '$' . number_format($this->total, 2);
    }

    /**
     * Get invoice status in Spanish.
     */
    public function getStatusInSpanishAttribute(): string
    {
        $statuses = [
            'draft' => 'Borrador',
            'open' => 'Pendiente',
            'paid' => 'Pagada',
            'void' => 'Anulada',
            'uncollectible' => 'Incobrable'
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Scope to only include paid invoices.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to only include unpaid invoices.
     */
    public function scopeUnpaid($query)
    {
        return $query->where('status', 'open');
    }

    /**
     * Scope to only include overdue invoices.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'open')
                    ->where('due_date', '<', now());
    }
}
