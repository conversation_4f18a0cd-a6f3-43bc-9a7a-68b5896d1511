<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'google_id',
        'email',
        'name',
        'avatar_url',
        'plan_id',
        'usage_count',
        'usage_reset_at',
        'last_login_at',
        'preferences',
        'selected_property_id',
        'timezone',
        'language',
        'email_verified_at'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'usage_reset_at' => 'datetime',
        'preferences' => 'array',
        'usage_count' => 'integer'
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
        'email_verified_at',
        'last_login_at',
        'usage_reset_at'
    ];

    /**
     * Get the plan that the user belongs to.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the user's API usage records.
     */
    public function usageRecords(): HasMany
    {
        return $this->hasMany(UsageRecord::class);
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the user's GA4 properties.
     */
    public function ga4Properties(): HasMany
    {
        return $this->hasMany(GA4Property::class);
    }

    /**
     * Get the user's activity logs.
     */
    public function activityLogs(): HasMany
    {
        return $this->hasMany(ActivityLog::class);
    }

    /**
     * Get the user's current subscription.
     */
    public function currentSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->first();
    }

    /**
     * Check if user has premium subscription.
     */
    public function hasPremiumSubscription(): bool
    {
        $subscription = $this->currentSubscription();
        return $subscription && $subscription->plan_id === 'premium';
    }

    /**
     * Check if user can make API calls (within usage limits).
     */
    public function canMakeApiCall(): bool
    {
        // Premium users have unlimited calls
        if ($this->hasPremiumSubscription()) {
            return true;
        }

        // Check if usage reset is needed
        if ($this->usage_reset_at && $this->usage_reset_at->isPast()) {
            $this->resetUsage();
        }

        // Check against plan limits
        $planLimit = $this->plan->api_call_limit ?? config('app.free_plan_limit', 50);
        
        return $this->usage_count < $planLimit;
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(int $count = 1): void
    {
        $this->increment('usage_count', $count);
        
        // Log usage
        $this->usageRecords()->create([
            'action' => 'api_call',
            'count' => $count,
            'metadata' => [
                'timestamp' => now()->toISOString(),
                'user_agent' => request()->userAgent(),
                'ip_address' => request()->ip()
            ]
        ]);
    }

    /**
     * Reset usage count (monthly reset).
     */
    public function resetUsage(): void
    {
        $this->update([
            'usage_count' => 0,
            'usage_reset_at' => now()->addMonth()
        ]);

        // Log reset
        $this->usageRecords()->create([
            'action' => 'usage_reset',
            'count' => 0,
            'metadata' => [
                'reset_date' => now()->toISOString(),
                'previous_count' => $this->usage_count
            ]
        ]);
    }

    /**
     * Get remaining API calls for current period.
     */
    public function getRemainingApiCalls(): ?int
    {
        if ($this->hasPremiumSubscription()) {
            return null; // Unlimited
        }

        $planLimit = $this->plan->api_call_limit ?? config('app.free_plan_limit', 50);
        return max(0, $planLimit - $this->usage_count);
    }

    /**
     * Get usage percentage for current period.
     */
    public function getUsagePercentage(): float
    {
        if ($this->hasPremiumSubscription()) {
            return 0; // No limits
        }

        $planLimit = $this->plan->api_call_limit ?? config('app.free_plan_limit', 50);
        
        if ($planLimit === 0) {
            return 100;
        }

        return min(100, ($this->usage_count / $planLimit) * 100);
    }

    /**
     * Check if user is approaching usage limit.
     */
    public function isApproachingLimit(float $threshold = 80): bool
    {
        if ($this->hasPremiumSubscription()) {
            return false;
        }

        return $this->getUsagePercentage() >= $threshold;
    }

    /**
     * Get user's preferred timezone.
     */
    protected function timezone(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ?? 'UTC',
        );
    }

    /**
     * Get user's preferred language.
     */
    protected function language(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ?? 'es',
        );
    }

    /**
     * Get user's display name.
     */
    protected function displayName(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->name ?? explode('@', $this->email)[0],
        );
    }

    /**
     * Get user's initials for avatar fallback.
     */
    protected function initials(): Attribute
    {
        return Attribute::make(
            get: function () {
                $name = $this->name ?? $this->email;
                $words = explode(' ', $name);
                
                if (count($words) >= 2) {
                    return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
                }
                
                return strtoupper(substr($name, 0, 2));
            }
        );
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive($query)
    {
        return $query->whereNotNull('email_verified_at')
                    ->whereNull('deleted_at');
    }

    /**
     * Scope a query to only include premium users.
     */
    public function scopePremium($query)
    {
        return $query->whereHas('subscriptions', function ($q) {
            $q->where('status', 'active')
              ->where('ends_at', '>', now())
              ->where('plan_id', 'premium');
        });
    }

    /**
     * Scope a query to only include users approaching limits.
     */
    public function scopeApproachingLimits($query, float $threshold = 80)
    {
        return $query->whereRaw('(usage_count / (SELECT api_call_limit FROM plans WHERE plans.id = users.plan_id)) * 100 >= ?', [$threshold]);
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'google_id';
    }

    /**
     * Convert the model instance to an array for API responses.
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'google_id' => $this->google_id,
            'email' => $this->email,
            'name' => $this->name,
            'display_name' => $this->display_name,
            'avatar_url' => $this->avatar_url,
            'initials' => $this->initials,
            'plan' => [
                'id' => $this->plan_id,
                'name' => $this->plan->name ?? 'Plan Gratuito',
                'api_call_limit' => $this->plan->api_call_limit,
                'features' => $this->plan->features ?? []
            ],
            'usage' => [
                'count' => $this->usage_count,
                'limit' => $this->plan->api_call_limit,
                'remaining' => $this->getRemainingApiCalls(),
                'percentage' => $this->getUsagePercentage(),
                'reset_at' => $this->usage_reset_at?->toISOString()
            ],
            'preferences' => $this->preferences ?? [],
            'timezone' => $this->timezone,
            'language' => $this->language,
            'selected_property_id' => $this->selected_property_id,
            'last_login_at' => $this->last_login_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'is_premium' => $this->hasPremiumSubscription(),
            'is_approaching_limit' => $this->isApproachingLimit()
        ];
    }
}
