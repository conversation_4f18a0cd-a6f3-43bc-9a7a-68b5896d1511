<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Subscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'stripe_price_id',
        'plan_id',
        'status',
        'amount',
        'currency',
        'interval',
        'interval_count',
        'current_period_start',
        'current_period_end',
        'trial_start',
        'trial_end',
        'canceled_at',
        'ends_at',
        'metadata'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'interval_count' => 'integer',
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
        'trial_start' => 'datetime',
        'trial_end' => 'datetime',
        'canceled_at' => 'datetime',
        'ends_at' => 'datetime',
        'metadata' => 'array'
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the plan associated with the subscription.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the invoices for the subscription.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Check if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               (!$this->ends_at || $this->ends_at->isFuture());
    }

    /**
     * Check if the subscription is canceled.
     */
    public function isCanceled(): bool
    {
        return $this->status === 'canceled' || 
               ($this->canceled_at && $this->canceled_at->isPast());
    }

    /**
     * Check if the subscription is past due.
     */
    public function isPastDue(): bool
    {
        return $this->status === 'past_due';
    }

    /**
     * Check if the subscription is on trial.
     */
    public function onTrial(): bool
    {
        return $this->trial_end && $this->trial_end->isFuture();
    }

    /**
     * Check if the subscription has ended.
     */
    public function hasEnded(): bool
    {
        return $this->ends_at && $this->ends_at->isPast();
    }

    /**
     * Get the number of days until the subscription ends.
     */
    public function daysUntilEnd(): ?int
    {
        if (!$this->current_period_end) {
            return null;
        }

        return max(0, now()->diffInDays($this->current_period_end, false));
    }

    /**
     * Get the number of days remaining in trial.
     */
    public function trialDaysRemaining(): ?int
    {
        if (!$this->onTrial()) {
            return null;
        }

        return max(0, now()->diffInDays($this->trial_end, false));
    }

    /**
     * Check if the subscription is set to cancel at period end.
     */
    public function cancelAtPeriodEnd(): bool
    {
        return $this->canceled_at && 
               $this->ends_at && 
               $this->ends_at->equalTo($this->current_period_end);
    }

    /**
     * Get the subscription's next billing date.
     */
    public function nextBillingDate(): ?Carbon
    {
        if ($this->isCanceled() || $this->hasEnded()) {
            return null;
        }

        return $this->current_period_end;
    }

    /**
     * Get formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return '$' . number_format($this->amount, 2);
    }

    /**
     * Get human readable interval.
     */
    public function getHumanIntervalAttribute(): string
    {
        $intervals = [
            'month' => $this->interval_count === 1 ? 'mensual' : $this->interval_count . ' meses',
            'year' => $this->interval_count === 1 ? 'anual' : $this->interval_count . ' años',
            'week' => $this->interval_count === 1 ? 'semanal' : $this->interval_count . ' semanas',
            'day' => $this->interval_count === 1 ? 'diario' : $this->interval_count . ' días'
        ];

        return $intervals[$this->interval] ?? $this->interval;
    }

    /**
     * Get subscription status in Spanish.
     */
    public function getStatusInSpanishAttribute(): string
    {
        $statuses = [
            'active' => 'Activa',
            'canceled' => 'Cancelada',
            'past_due' => 'Vencida',
            'unpaid' => 'Sin pagar',
            'incomplete' => 'Incompleta',
            'incomplete_expired' => 'Expirada',
            'trialing' => 'En prueba'
        ];

        return $statuses[$this->status] ?? ucfirst($this->status);
    }

    /**
     * Scope to only include active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('ends_at')
                          ->orWhere('ends_at', '>', now());
                    });
    }

    /**
     * Scope to only include canceled subscriptions.
     */
    public function scopeCanceled($query)
    {
        return $query->where('status', 'canceled')
                    ->orWhereNotNull('canceled_at');
    }

    /**
     * Scope to only include subscriptions on trial.
     */
    public function scopeOnTrial($query)
    {
        return $query->whereNotNull('trial_end')
                    ->where('trial_end', '>', now());
    }

    /**
     * Scope to only include subscriptions ending soon.
     */
    public function scopeEndingSoon($query, int $days = 7)
    {
        return $query->where('current_period_end', '<=', now()->addDays($days))
                    ->where('current_period_end', '>', now());
    }

    /**
     * Convert the model instance to an array for API responses.
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'stripe_subscription_id' => $this->stripe_subscription_id,
            'plan_id' => $this->plan_id,
            'plan_name' => $this->plan->name ?? 'Plan Desconocido',
            'status' => $this->status,
            'status_spanish' => $this->status_in_spanish,
            'amount' => $this->amount,
            'formatted_amount' => $this->formatted_amount,
            'currency' => $this->currency,
            'interval' => $this->interval,
            'interval_count' => $this->interval_count,
            'human_interval' => $this->human_interval,
            'current_period_start' => $this->current_period_start?->toISOString(),
            'current_period_end' => $this->current_period_end?->toISOString(),
            'trial_start' => $this->trial_start?->toISOString(),
            'trial_end' => $this->trial_end?->toISOString(),
            'canceled_at' => $this->canceled_at?->toISOString(),
            'ends_at' => $this->ends_at?->toISOString(),
            'next_billing_date' => $this->nextBillingDate()?->toISOString(),
            'days_until_end' => $this->daysUntilEnd(),
            'trial_days_remaining' => $this->trialDaysRemaining(),
            'is_active' => $this->isActive(),
            'is_canceled' => $this->isCanceled(),
            'is_past_due' => $this->isPastDue(),
            'on_trial' => $this->onTrial(),
            'has_ended' => $this->hasEnded(),
            'cancel_at_period_end' => $this->cancelAtPeriodEnd(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString()
        ];
    }
}
