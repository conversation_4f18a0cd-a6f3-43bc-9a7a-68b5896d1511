<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PaymentMethod extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'stripe_payment_method_id',
        'stripe_customer_id',
        'type',
        'brand',
        'last_four',
        'exp_month',
        'exp_year',
        'country',
        'funding',
        'is_default',
        'metadata'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'exp_month' => 'integer',
        'exp_year' => 'integer',
        'is_default' => 'boolean',
        'metadata' => 'array'
    ];

    /**
     * Get the user that owns the payment method.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the payment method is expired.
     */
    public function isExpired(): bool
    {
        if (!$this->exp_month || !$this->exp_year) {
            return false;
        }

        $now = now();
        $expiryDate = now()->setYear($this->exp_year)->setMonth($this->exp_month)->endOfMonth();

        return $now->isAfter($expiryDate);
    }

    /**
     * Check if the payment method expires soon.
     */
    public function expiresSoon(int $months = 2): bool
    {
        if (!$this->exp_month || !$this->exp_year) {
            return false;
        }

        $now = now();
        $expiryDate = now()->setYear($this->exp_year)->setMonth($this->exp_month)->endOfMonth();
        $warningDate = $now->addMonths($months);

        return $expiryDate->isBefore($warningDate);
    }

    /**
     * Get formatted expiry date.
     */
    public function getFormattedExpiryAttribute(): ?string
    {
        if (!$this->exp_month || !$this->exp_year) {
            return null;
        }

        return sprintf('%02d/%d', $this->exp_month, $this->exp_year);
    }

    /**
     * Get masked card number.
     */
    public function getMaskedNumberAttribute(): ?string
    {
        if (!$this->last_four) {
            return null;
        }

        return '**** **** **** ' . $this->last_four;
    }

    /**
     * Get brand display name.
     */
    public function getBrandDisplayNameAttribute(): string
    {
        $brands = [
            'visa' => 'Visa',
            'mastercard' => 'Mastercard',
            'amex' => 'American Express',
            'discover' => 'Discover',
            'diners' => 'Diners Club',
            'jcb' => 'JCB',
            'unionpay' => 'UnionPay'
        ];

        return $brands[$this->brand] ?? ucfirst($this->brand ?? 'Unknown');
    }

    /**
     * Get funding type display name.
     */
    public function getFundingDisplayNameAttribute(): string
    {
        $funding = [
            'credit' => 'Crédito',
            'debit' => 'Débito',
            'prepaid' => 'Prepagada',
            'unknown' => 'Desconocido'
        ];

        return $funding[$this->funding] ?? ucfirst($this->funding ?? 'Unknown');
    }

    /**
     * Scope to only include default payment methods.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to only include expired payment methods.
     */
    public function scopeExpired($query)
    {
        $now = now();
        return $query->where(function ($q) use ($now) {
            $q->where('exp_year', '<', $now->year)
              ->orWhere(function ($q2) use ($now) {
                  $q2->where('exp_year', '=', $now->year)
                     ->where('exp_month', '<', $now->month);
              });
        });
    }

    /**
     * Scope to only include payment methods expiring soon.
     */
    public function scopeExpiringSoon($query, int $months = 2)
    {
        $warningDate = now()->addMonths($months);
        return $query->where(function ($q) use ($warningDate) {
            $q->where('exp_year', '<', $warningDate->year)
              ->orWhere(function ($q2) use ($warningDate) {
                  $q2->where('exp_year', '=', $warningDate->year)
                     ->where('exp_month', '<=', $warningDate->month);
              });
        });
    }

    /**
     * Convert the model instance to an array for API responses.
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'stripe_payment_method_id' => $this->stripe_payment_method_id,
            'type' => $this->type,
            'brand' => $this->brand,
            'brand_display_name' => $this->brand_display_name,
            'last_four' => $this->last_four,
            'masked_number' => $this->masked_number,
            'exp_month' => $this->exp_month,
            'exp_year' => $this->exp_year,
            'formatted_expiry' => $this->formatted_expiry,
            'country' => $this->country,
            'funding' => $this->funding,
            'funding_display_name' => $this->funding_display_name,
            'is_default' => $this->is_default,
            'is_expired' => $this->isExpired(),
            'expires_soon' => $this->expiresSoon(),
            'created_at' => $this->created_at->toISOString()
        ];
    }
}
