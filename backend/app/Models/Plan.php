<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    use HasFactory;

    /**
     * Indicates if the IDs are auto-incrementing.
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     */
    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'id',
        'name',
        'description',
        'price',
        'currency',
        'billing_period',
        'api_call_limit',
        'features',
        'is_active',
        'sort_order'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'price' => 'decimal:2',
        'api_call_limit' => 'integer',
        'features' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Get the users for the plan.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the subscriptions for the plan.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Check if the plan is free.
     */
    public function isFree(): bool
    {
        return $this->price == 0 || $this->id === 'free';
    }

    /**
     * Check if the plan is premium.
     */
    public function isPremium(): bool
    {
        return $this->price > 0 && $this->id !== 'free';
    }

    /**
     * Check if the plan has unlimited API calls.
     */
    public function hasUnlimitedApiCalls(): bool
    {
        return $this->api_call_limit === null;
    }

    /**
     * Get formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->isFree()) {
            return 'Gratis';
        }

        return '$' . number_format($this->price, 2) . '/' . $this->getHumanBillingPeriodAttribute();
    }

    /**
     * Get human readable billing period.
     */
    public function getHumanBillingPeriodAttribute(): string
    {
        $periods = [
            'monthly' => 'mes',
            'yearly' => 'año',
            'weekly' => 'semana',
            'daily' => 'día'
        ];

        return $periods[$this->billing_period] ?? $this->billing_period;
    }

    /**
     * Get API call limit text.
     */
    public function getApiCallLimitTextAttribute(): string
    {
        if ($this->hasUnlimitedApiCalls()) {
            return 'Ilimitadas';
        }

        return number_format($this->api_call_limit) . ' consultas/mes';
    }

    /**
     * Get plan features as a formatted list.
     */
    public function getFeatureListAttribute(): array
    {
        $features = $this->features ?? [];
        $featureList = [];

        // Basic features
        if ($features['basic_analytics'] ?? false) {
            $featureList[] = 'Análisis básicos de GA4';
        }

        if ($features['page_analysis'] ?? false) {
            $featureList[] = 'Análisis de página actual';
        }

        if ($features['site_overview'] ?? false) {
            $featureList[] = 'Resumen del sitio completo';
        }

        if ($features['traffic_sources'] ?? false) {
            $featureList[] = 'Fuentes de tráfico';
        }

        if ($features['device_breakdown'] ?? false) {
            $featureList[] = 'Desglose por dispositivos';
        }

        // Usage limits
        if ($features['monthly_limit']) {
            if ($features['monthly_limit'] === null) {
                $featureList[] = 'Consultas ilimitadas';
            } else {
                $featureList[] = $features['monthly_limit'] . ' consultas/mes';
            }
        }

        // Premium features
        if ($features['comparison_periods'] ?? false) {
            $featureList[] = 'Comparación de períodos';
        }

        if ($features['data_export'] ?? false) {
            $featureList[] = 'Exportación de datos';
        }

        if ($features['email_reports'] ?? false) {
            $featureList[] = 'Reportes por email';
        }

        if ($features['advanced_filters'] ?? false) {
            $featureList[] = 'Filtros avanzados';
        }

        if ($features['priority_support'] ?? false) {
            $featureList[] = 'Soporte prioritario';
        }

        // Enterprise features
        if ($features['team_management'] ?? false) {
            $featureList[] = 'Gestión de equipos';
        }

        if ($features['white_label'] ?? false) {
            $featureList[] = 'Marca blanca';
        }

        if ($features['dedicated_support'] ?? false) {
            $featureList[] = 'Soporte dedicado';
        }

        return $featureList;
    }

    /**
     * Get Stripe price IDs for the plan.
     */
    public function getStripePriceIds(): array
    {
        $priceIds = [];

        switch ($this->id) {
            case 'premium':
                $priceIds['monthly'] = config('services.stripe.plans.premium_monthly');
                $priceIds['yearly'] = config('services.stripe.plans.premium_yearly');
                break;
            case 'enterprise':
                $priceIds['monthly'] = config('services.stripe.plans.enterprise_monthly');
                $priceIds['yearly'] = config('services.stripe.plans.enterprise_yearly');
                break;
        }

        return array_filter($priceIds);
    }

    /**
     * Get the default Stripe price ID for the plan.
     */
    public function getDefaultStripePriceId(): ?string
    {
        $priceIds = $this->getStripePriceIds();
        
        // Default to monthly if available
        return $priceIds['monthly'] ?? $priceIds['yearly'] ?? null;
    }

    /**
     * Scope to only include active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to only include premium plans.
     */
    public function scopePremium($query)
    {
        return $query->where('price', '>', 0);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Convert the model instance to an array for API responses.
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'formatted_price' => $this->formatted_price,
            'currency' => $this->currency,
            'billing_period' => $this->billing_period,
            'human_billing_period' => $this->human_billing_period,
            'api_call_limit' => $this->api_call_limit,
            'api_call_limit_text' => $this->api_call_limit_text,
            'features' => $this->features,
            'feature_list' => $this->feature_list,
            'is_free' => $this->isFree(),
            'is_premium' => $this->isPremium(),
            'has_unlimited_api_calls' => $this->hasUnlimitedApiCalls(),
            'stripe_price_ids' => $this->getStripePriceIds(),
            'default_stripe_price_id' => $this->getDefaultStripePriceId(),
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order
        ];
    }
}
