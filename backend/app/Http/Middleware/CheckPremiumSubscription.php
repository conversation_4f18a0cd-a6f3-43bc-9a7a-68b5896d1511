<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class CheckPremiumSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'AUTHENTICATION_REQUIRED',
                    'message' => 'Authentication required',
                    'details' => 'You must be logged in to access this feature'
                ]
            ], 401);
        }

        // Check if user has premium subscription
        if (!$user->hasPremiumSubscription()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'PREMIUM_REQUIRED',
                    'message' => 'Premium subscription required',
                    'details' => 'This feature is only available for premium subscribers',
                    'upgrade_url' => config('app.url') . '/upgrade'
                ]
            ], 403);
        }

        // Check if subscription is active
        $subscription = $user->currentSubscription();
        if (!$subscription || !$subscription->isActive()) {
            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SUBSCRIPTION_INACTIVE',
                    'message' => 'Subscription is not active',
                    'details' => 'Your premium subscription is not active or has expired',
                    'subscription_status' => $subscription?->status ?? 'none'
                ]
            ], 403);
        }

        return $next($request);
    }
}
