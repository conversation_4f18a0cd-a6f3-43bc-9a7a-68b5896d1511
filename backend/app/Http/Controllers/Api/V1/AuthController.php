<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\GoogleAuthService;
use App\Services\JWTService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Exception;

class AuthController extends Controller
{
    protected GoogleAuthService $googleAuthService;
    protected JWTService $jwtService;

    public function __construct(GoogleAuthService $googleAuthService, JWTService $jwtService)
    {
        $this->googleAuthService = $googleAuthService;
        $this->jwtService = $jwtService;
    }

    /**
     * Verify Google OAuth token and authenticate user
     */
    public function verifyGoogleToken(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'googleAccessToken' => 'required|string',
                'clientInfo' => 'sometimes|array',
                'clientInfo.extensionVersion' => 'sometimes|string',
                'clientInfo.userAgent' => 'sometimes|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'VALIDATION_ERROR',
                        'message' => 'Invalid request data',
                        'details' => $validator->errors()
                    ]
                ], 400);
            }

            $googleToken = $request->input('googleAccessToken');
            $clientInfo = $request->input('clientInfo', []);

            Log::info('Google token verification started', [
                'request_id' => $request->header('X-Request-ID'),
                'extension_version' => $clientInfo['extensionVersion'] ?? 'unknown',
                'user_agent' => $request->userAgent()
            ]);

            // Verify Google token and get user info
            $googleUserInfo = $this->googleAuthService->verifyToken($googleToken);

            if (!$googleUserInfo) {
                Log::warning('Google token verification failed', [
                    'request_id' => $request->header('X-Request-ID')
                ]);

                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_GOOGLE_TOKEN',
                        'message' => 'Invalid or expired Google access token',
                        'details' => 'The provided Google access token is invalid or has expired'
                    ]
                ], 401);
            }

            // Check if user has required GA4 permissions
            $hasGA4Access = $this->googleAuthService->checkGA4Access($googleToken);

            if (!$hasGA4Access) {
                Log::warning('User lacks GA4 access', [
                    'google_id' => $googleUserInfo['id'],
                    'email' => $googleUserInfo['email']
                ]);

                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INSUFFICIENT_PERMISSIONS',
                        'message' => 'No access to Google Analytics 4',
                        'details' => 'Your Google account does not have access to any GA4 properties'
                    ]
                ], 403);
            }

            // Find or create user
            $user = User::where('google_id', $googleUserInfo['id'])->first();

            if (!$user) {
                $user = User::create([
                    'google_id' => $googleUserInfo['id'],
                    'email' => $googleUserInfo['email'],
                    'name' => $googleUserInfo['name'] ?? null,
                    'avatar_url' => $googleUserInfo['picture'] ?? null,
                    'plan_id' => 'free',
                    'email_verified_at' => now(),
                    'usage_count' => 0,
                    'usage_reset_at' => now()->addMonth()
                ]);

                Log::info('New user created', [
                    'user_id' => $user->id,
                    'google_id' => $user->google_id,
                    'email' => $user->email
                ]);
            } else {
                // Update user info
                $user->update([
                    'email' => $googleUserInfo['email'],
                    'name' => $googleUserInfo['name'] ?? $user->name,
                    'avatar_url' => $googleUserInfo['picture'] ?? $user->avatar_url,
                    'last_login_at' => now()
                ]);

                Log::info('Existing user updated', [
                    'user_id' => $user->id,
                    'google_id' => $user->google_id
                ]);
            }

            // Generate JWT token
            $jwtToken = $this->jwtService->generateToken($user);
            $tokenExpiry = now()->addMinutes(config('jwt.ttl', 1440));

            // Prepare response data
            $responseData = [
                'userId' => $user->id,
                'googleId' => $user->google_id,
                'email' => $user->email,
                'name' => $user->name,
                'avatarUrl' => $user->avatar_url,
                'jwtToken' => $jwtToken,
                'tokenExpiry' => $tokenExpiry->toISOString(),
                'plan' => [
                    'planId' => $user->plan_id,
                    'name' => $user->plan->name ?? 'Plan Gratuito',
                    'apiCallLimit' => $user->plan->api_call_limit,
                    'features' => $user->plan->features ?? []
                ],
                'usage' => [
                    'usageCount' => $user->usage_count,
                    'usageLimit' => $user->plan->api_call_limit,
                    'resetDate' => $user->usage_reset_at?->toISOString(),
                    'remaining' => $user->plan->api_call_limit ? 
                        max(0, $user->plan->api_call_limit - $user->usage_count) : null
                ],
                'preferences' => $user->preferences ?? [],
                'lastLogin' => $user->last_login_at?->toISOString()
            ];

            Log::info('Authentication successful', [
                'user_id' => $user->id,
                'plan' => $user->plan_id,
                'usage_count' => $user->usage_count
            ]);

            return response()->json([
                'success' => true,
                'data' => $responseData
            ]);

        } catch (Exception $e) {
            Log::error('Authentication error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_id' => $request->header('X-Request-ID')
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'AUTHENTICATION_ERROR',
                    'message' => 'Authentication failed',
                    'details' => app()->environment('production') ? 
                        'An error occurred during authentication' : $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Refresh JWT token
     */
    public function refreshToken(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'refreshToken' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'VALIDATION_ERROR',
                        'message' => 'Invalid request data',
                        'details' => $validator->errors()
                    ]
                ], 400);
            }

            $refreshToken = $request->input('refreshToken');

            // Verify and refresh token
            $newToken = $this->jwtService->refreshToken($refreshToken);

            if (!$newToken) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_REFRESH_TOKEN',
                        'message' => 'Invalid or expired refresh token',
                        'details' => 'The provided refresh token is invalid or has expired'
                    ]
                ], 401);
            }

            $tokenExpiry = now()->addMinutes(config('jwt.ttl', 1440));

            return response()->json([
                'success' => true,
                'data' => [
                    'jwtToken' => $newToken,
                    'tokenExpiry' => $tokenExpiry->toISOString()
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Token refresh error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'TOKEN_REFRESH_ERROR',
                    'message' => 'Token refresh failed',
                    'details' => app()->environment('production') ? 
                        'An error occurred during token refresh' : $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Handle Google OAuth callback (for web-based flows)
     */
    public function handleGoogleCallback(Request $request): JsonResponse
    {
        try {
            $code = $request->input('code');
            $state = $request->input('state');

            if (!$code) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'MISSING_AUTH_CODE',
                        'message' => 'Authorization code is required',
                        'details' => 'No authorization code provided in callback'
                    ]
                ], 400);
            }

            // Exchange code for tokens
            $tokens = $this->googleAuthService->exchangeCodeForTokens($code);

            if (!$tokens) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'TOKEN_EXCHANGE_FAILED',
                        'message' => 'Failed to exchange authorization code for tokens',
                        'details' => 'Could not obtain access tokens from Google'
                    ]
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'accessToken' => $tokens['access_token'],
                    'expiresIn' => $tokens['expires_in'] ?? 3600
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Google callback error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'CALLBACK_ERROR',
                    'message' => 'OAuth callback failed',
                    'details' => app()->environment('production') ? 
                        'An error occurred during OAuth callback' : $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            Log::info('User logout', [
                'user_id' => $user->id,
                'email' => $user->email
            ]);

            // Revoke current token
            $request->user()->currentAccessToken()->delete();

            return response()->json([
                'success' => true,
                'message' => 'Logged out successfully'
            ]);

        } catch (Exception $e) {
            Log::error('Logout error', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'LOGOUT_ERROR',
                    'message' => 'Logout failed',
                    'details' => 'An error occurred during logout'
                ]
            ], 500);
        }
    }

    /**
     * Get current authenticated user
     */
    public function me(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            return response()->json([
                'success' => true,
                'data' => [
                    'userId' => $user->id,
                    'googleId' => $user->google_id,
                    'email' => $user->email,
                    'name' => $user->name,
                    'avatarUrl' => $user->avatar_url,
                    'plan' => [
                        'planId' => $user->plan_id,
                        'name' => $user->plan->name ?? 'Plan Gratuito',
                        'apiCallLimit' => $user->plan->api_call_limit,
                        'features' => $user->plan->features ?? []
                    ],
                    'usage' => [
                        'usageCount' => $user->usage_count,
                        'usageLimit' => $user->plan->api_call_limit,
                        'resetDate' => $user->usage_reset_at?->toISOString(),
                        'remaining' => $user->plan->api_call_limit ? 
                            max(0, $user->plan->api_call_limit - $user->usage_count) : null
                    ],
                    'preferences' => $user->preferences ?? [],
                    'lastLogin' => $user->last_login_at?->toISOString(),
                    'createdAt' => $user->created_at->toISOString()
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Get user info error', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'USER_INFO_ERROR',
                    'message' => 'Failed to get user information',
                    'details' => 'An error occurred while retrieving user data'
                ]
            ], 500);
        }
    }
}
