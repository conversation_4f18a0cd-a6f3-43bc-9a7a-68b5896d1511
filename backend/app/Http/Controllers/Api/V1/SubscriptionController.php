<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\StripeService;
use App\Models\Subscription;
use App\Models\Plan;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Exception;

class SubscriptionController extends Controller
{
    protected StripeService $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    /**
     * Get current user subscription
     */
    public function getCurrentSubscription(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $subscription = $user->currentSubscription();

            if (!$subscription) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'subscription' => null,
                        'plan' => $user->plan->toApiArray(),
                        'has_subscription' => false
                    ]
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription' => $subscription->toApiArray(),
                    'plan' => $subscription->plan->toApiArray(),
                    'has_subscription' => true
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get current subscription', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SUBSCRIPTION_FETCH_ERROR',
                    'message' => 'Failed to get subscription information',
                    'details' => 'An error occurred while retrieving subscription data'
                ]
            ], 500);
        }
    }

    /**
     * Create new subscription
     */
    public function createSubscription(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|string|exists:plans,id',
                'payment_method_id' => 'required|string',
                'billing_period' => 'sometimes|string|in:monthly,yearly'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'VALIDATION_ERROR',
                        'message' => 'Invalid request data',
                        'details' => $validator->errors()
                    ]
                ], 400);
            }

            $user = $request->user();
            $planId = $request->input('plan_id');
            $paymentMethodId = $request->input('payment_method_id');
            $billingPeriod = $request->input('billing_period', 'monthly');

            Log::info('Creating subscription', [
                'user_id' => $user->id,
                'plan_id' => $planId,
                'billing_period' => $billingPeriod
            ]);

            // Check if user already has an active subscription
            $existingSubscription = $user->currentSubscription();
            if ($existingSubscription) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'SUBSCRIPTION_EXISTS',
                        'message' => 'User already has an active subscription',
                        'details' => 'Cancel the current subscription before creating a new one'
                    ]
                ], 400);
            }

            // Get plan details
            $plan = Plan::find($planId);
            if (!$plan || !$plan->is_active) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'INVALID_PLAN',
                        'message' => 'Invalid or inactive plan',
                        'details' => 'The selected plan is not available'
                    ]
                ], 400);
            }

            // Free plans don't need Stripe subscription
            if ($plan->isFree()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'FREE_PLAN_ERROR',
                        'message' => 'Free plan does not require subscription',
                        'details' => 'Free plan is automatically assigned to users'
                    ]
                ], 400);
            }

            // Create subscription with Stripe
            $stripeSubscription = $this->stripeService->createSubscription(
                $user,
                $plan,
                $paymentMethodId,
                $billingPeriod
            );

            if (!$stripeSubscription) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'STRIPE_SUBSCRIPTION_ERROR',
                        'message' => 'Failed to create subscription',
                        'details' => 'Could not create subscription with payment provider'
                    ]
                ], 500);
            }

            // Create local subscription record
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'stripe_subscription_id' => $stripeSubscription->id,
                'stripe_customer_id' => $stripeSubscription->customer,
                'stripe_price_id' => $stripeSubscription->items->data[0]->price->id,
                'plan_id' => $planId,
                'status' => $stripeSubscription->status,
                'amount' => $stripeSubscription->items->data[0]->price->unit_amount / 100,
                'currency' => strtoupper($stripeSubscription->items->data[0]->price->currency),
                'interval' => $stripeSubscription->items->data[0]->price->recurring->interval,
                'interval_count' => $stripeSubscription->items->data[0]->price->recurring->interval_count,
                'current_period_start' => $stripeSubscription->current_period_start ? 
                    now()->createFromTimestamp($stripeSubscription->current_period_start) : null,
                'current_period_end' => $stripeSubscription->current_period_end ? 
                    now()->createFromTimestamp($stripeSubscription->current_period_end) : null,
                'trial_start' => $stripeSubscription->trial_start ? 
                    now()->createFromTimestamp($stripeSubscription->trial_start) : null,
                'trial_end' => $stripeSubscription->trial_end ? 
                    now()->createFromTimestamp($stripeSubscription->trial_end) : null
            ]);

            // Update user plan
            $user->update(['plan_id' => $planId]);

            Log::info('Subscription created successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'stripe_subscription_id' => $stripeSubscription->id
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription' => $subscription->toApiArray(),
                    'plan' => $plan->toApiArray(),
                    'message' => 'Subscription created successfully'
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Subscription creation failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SUBSCRIPTION_CREATION_ERROR',
                    'message' => 'Failed to create subscription',
                    'details' => app()->environment('production') ? 
                        'An error occurred while creating the subscription' : $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'cancel_at_period_end' => 'sometimes|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'VALIDATION_ERROR',
                        'message' => 'Invalid request data',
                        'details' => $validator->errors()
                    ]
                ], 400);
            }

            $user = $request->user();
            $cancelAtPeriodEnd = $request->input('cancel_at_period_end', true);

            $subscription = $user->currentSubscription();
            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'NO_SUBSCRIPTION',
                        'message' => 'No active subscription found',
                        'details' => 'User does not have an active subscription to cancel'
                    ]
                ], 404);
            }

            Log::info('Canceling subscription', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'cancel_at_period_end' => $cancelAtPeriodEnd
            ]);

            // Cancel with Stripe
            $stripeSubscription = $this->stripeService->cancelSubscription(
                $subscription->stripe_subscription_id,
                $cancelAtPeriodEnd
            );

            if (!$stripeSubscription) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'STRIPE_CANCEL_ERROR',
                        'message' => 'Failed to cancel subscription',
                        'details' => 'Could not cancel subscription with payment provider'
                    ]
                ], 500);
            }

            // Update local subscription
            $updateData = [
                'status' => $stripeSubscription->status,
                'canceled_at' => now()
            ];

            if ($cancelAtPeriodEnd) {
                $updateData['ends_at'] = $subscription->current_period_end;
            } else {
                $updateData['ends_at'] = now();
                // Downgrade to free plan immediately
                $user->update(['plan_id' => 'free']);
            }

            $subscription->update($updateData);

            Log::info('Subscription canceled successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription' => $subscription->fresh()->toApiArray(),
                    'message' => $cancelAtPeriodEnd ? 
                        'Subscription will be canceled at the end of the current period' :
                        'Subscription canceled immediately'
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Subscription cancellation failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SUBSCRIPTION_CANCEL_ERROR',
                    'message' => 'Failed to cancel subscription',
                    'details' => 'An error occurred while canceling the subscription'
                ]
            ], 500);
        }
    }

    /**
     * Resume canceled subscription
     */
    public function resumeSubscription(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $subscription = $user->currentSubscription();

            if (!$subscription || !$subscription->cancelAtPeriodEnd()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'CANNOT_RESUME',
                        'message' => 'Cannot resume subscription',
                        'details' => 'Subscription is not set to cancel at period end'
                    ]
                ], 400);
            }

            Log::info('Resuming subscription', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id
            ]);

            // Resume with Stripe
            $stripeSubscription = $this->stripeService->resumeSubscription(
                $subscription->stripe_subscription_id
            );

            if (!$stripeSubscription) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'STRIPE_RESUME_ERROR',
                        'message' => 'Failed to resume subscription',
                        'details' => 'Could not resume subscription with payment provider'
                    ]
                ], 500);
            }

            // Update local subscription
            $subscription->update([
                'status' => $stripeSubscription->status,
                'canceled_at' => null,
                'ends_at' => null
            ]);

            Log::info('Subscription resumed successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription' => $subscription->fresh()->toApiArray(),
                    'message' => 'Subscription resumed successfully'
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Subscription resume failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'SUBSCRIPTION_RESUME_ERROR',
                    'message' => 'Failed to resume subscription',
                    'details' => 'An error occurred while resuming the subscription'
                ]
            ], 500);
        }
    }

    /**
     * Get user invoices
     */
    public function getInvoices(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $limit = $request->input('limit', 10);
            $offset = $request->input('offset', 0);

            $invoices = Invoice::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->offset($offset)
                ->get();

            $invoicesData = $invoices->map(function ($invoice) {
                return [
                    'id' => $invoice->id,
                    'stripe_invoice_id' => $invoice->stripe_invoice_id,
                    'number' => $invoice->number,
                    'status' => $invoice->status,
                    'amount_due' => $invoice->amount_due,
                    'amount_paid' => $invoice->amount_paid,
                    'total' => $invoice->total,
                    'currency' => $invoice->currency,
                    'description' => $invoice->description,
                    'hosted_invoice_url' => $invoice->hosted_invoice_url,
                    'invoice_pdf' => $invoice->invoice_pdf,
                    'period_start' => $invoice->period_start?->toISOString(),
                    'period_end' => $invoice->period_end?->toISOString(),
                    'due_date' => $invoice->due_date?->toISOString(),
                    'paid_at' => $invoice->paid_at?->toISOString(),
                    'created_at' => $invoice->created_at->toISOString()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'invoices' => $invoicesData,
                    'total_count' => Invoice::where('user_id', $user->id)->count(),
                    'limit' => $limit,
                    'offset' => $offset
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to get invoices', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'INVOICES_FETCH_ERROR',
                    'message' => 'Failed to get invoices',
                    'details' => 'An error occurred while retrieving invoice data'
                ]
            ], 500);
        }
    }

    /**
     * Update payment method
     */
    public function updatePaymentMethod(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_method_id' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'VALIDATION_ERROR',
                        'message' => 'Invalid request data',
                        'details' => $validator->errors()
                    ]
                ], 400);
            }

            $user = $request->user();
            $paymentMethodId = $request->input('payment_method_id');

            $subscription = $user->currentSubscription();
            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'NO_SUBSCRIPTION',
                        'message' => 'No active subscription found',
                        'details' => 'User does not have an active subscription'
                    ]
                ], 404);
            }

            Log::info('Updating payment method', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'payment_method_id' => $paymentMethodId
            ]);

            // Update payment method with Stripe
            $success = $this->stripeService->updateSubscriptionPaymentMethod(
                $subscription->stripe_subscription_id,
                $paymentMethodId
            );

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'error' => [
                        'code' => 'PAYMENT_METHOD_UPDATE_ERROR',
                        'message' => 'Failed to update payment method',
                        'details' => 'Could not update payment method with payment provider'
                    ]
                ], 500);
            }

            Log::info('Payment method updated successfully', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'message' => 'Payment method updated successfully'
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Payment method update failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => [
                    'code' => 'PAYMENT_METHOD_UPDATE_ERROR',
                    'message' => 'Failed to update payment method',
                    'details' => 'An error occurred while updating the payment method'
                ]
            ], 500);
        }
    }

    /**
     * Handle Stripe webhooks
     */
    public function handleStripeWebhook(Request $request): JsonResponse
    {
        try {
            Log::info('Stripe webhook received', [
                'headers' => $request->headers->all(),
                'payload_size' => strlen($request->getContent())
            ]);

            // Verify webhook signature
            $payload = $request->getContent();
            $signature = $request->header('Stripe-Signature');
            
            if (!$this->stripeService->verifyWebhookSignature($payload, $signature)) {
                Log::warning('Invalid Stripe webhook signature');
                return response()->json(['error' => 'Invalid signature'], 400);
            }

            $event = json_decode($payload, true);
            
            // Process the webhook event
            $this->stripeService->handleWebhookEvent($event);

            return response()->json(['success' => true]);

        } catch (Exception $e) {
            Log::error('Stripe webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }
}
