<?php

namespace App\Services;

use Stripe\Stripe;
use Stripe\Customer;
use Stripe\Subscription;
use Stripe\PaymentMethod;
use Stripe\Invoice;
use Stripe\WebhookEndpoint;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Webhook;
use App\Models\User;
use App\Models\Plan;
use App\Models\Subscription as LocalSubscription;
use App\Models\Invoice as LocalInvoice;
use App\Models\PaymentMethod as LocalPaymentMethod;
use App\Models\WebhookEvent;
use Illuminate\Support\Facades\Log;
use Exception;

class StripeService
{
    protected string $secretKey;
    protected string $webhookSecret;

    public function __construct()
    {
        $this->secretKey = config('services.stripe.secret');
        $this->webhookSecret = config('services.stripe.webhook_secret');
        
        Stripe::setApiKey($this->secretKey);
        Stripe::setApiVersion('2023-10-16');
    }

    /**
     * Create or get Stripe customer for user
     */
    public function createOrGetCustomer(User $user): Customer
    {
        try {
            // Check if user already has a Stripe customer ID
            if ($user->stripe_customer_id) {
                try {
                    return Customer::retrieve($user->stripe_customer_id);
                } catch (Exception $e) {
                    Log::warning('Existing Stripe customer not found', [
                        'user_id' => $user->id,
                        'stripe_customer_id' => $user->stripe_customer_id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Create new Stripe customer
            $customer = Customer::create([
                'email' => $user->email,
                'name' => $user->name,
                'metadata' => [
                    'user_id' => $user->id,
                    'google_id' => $user->google_id
                ]
            ]);

            // Save customer ID to user
            $user->update(['stripe_customer_id' => $customer->id]);

            Log::info('Stripe customer created', [
                'user_id' => $user->id,
                'stripe_customer_id' => $customer->id
            ]);

            return $customer;

        } catch (Exception $e) {
            Log::error('Failed to create Stripe customer', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Create subscription
     */
    public function createSubscription(User $user, Plan $plan, string $paymentMethodId, string $billingPeriod = 'monthly'): ?Subscription
    {
        try {
            Log::info('Creating Stripe subscription', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'billing_period' => $billingPeriod
            ]);

            // Get or create customer
            $customer = $this->createOrGetCustomer($user);

            // Attach payment method to customer
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId);
            $paymentMethod->attach(['customer' => $customer->id]);

            // Set as default payment method
            $customer->invoice_settings = ['default_payment_method' => $paymentMethodId];
            $customer->save();

            // Get Stripe price ID for the plan
            $priceIds = $plan->getStripePriceIds();
            $priceId = $priceIds[$billingPeriod] ?? $plan->getDefaultStripePriceId();

            if (!$priceId) {
                throw new Exception("No Stripe price ID found for plan {$plan->id} with billing period {$billingPeriod}");
            }

            // Create subscription
            $subscription = Subscription::create([
                'customer' => $customer->id,
                'items' => [
                    ['price' => $priceId]
                ],
                'payment_behavior' => 'default_incomplete',
                'payment_settings' => [
                    'save_default_payment_method' => 'on_subscription'
                ],
                'expand' => ['latest_invoice.payment_intent'],
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'billing_period' => $billingPeriod
                ]
            ]);

            Log::info('Stripe subscription created', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'status' => $subscription->status
            ]);

            return $subscription;

        } catch (Exception $e) {
            Log::error('Failed to create Stripe subscription', [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(string $subscriptionId, bool $cancelAtPeriodEnd = true): ?Subscription
    {
        try {
            Log::info('Canceling Stripe subscription', [
                'subscription_id' => $subscriptionId,
                'cancel_at_period_end' => $cancelAtPeriodEnd
            ]);

            $subscription = Subscription::retrieve($subscriptionId);

            if ($cancelAtPeriodEnd) {
                $subscription->cancel_at_period_end = true;
                $subscription->save();
            } else {
                $subscription = $subscription->cancel();
            }

            Log::info('Stripe subscription canceled', [
                'subscription_id' => $subscriptionId,
                'status' => $subscription->status
            ]);

            return $subscription;

        } catch (Exception $e) {
            Log::error('Failed to cancel Stripe subscription', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Resume subscription
     */
    public function resumeSubscription(string $subscriptionId): ?Subscription
    {
        try {
            Log::info('Resuming Stripe subscription', [
                'subscription_id' => $subscriptionId
            ]);

            $subscription = Subscription::retrieve($subscriptionId);
            $subscription->cancel_at_period_end = false;
            $subscription->save();

            Log::info('Stripe subscription resumed', [
                'subscription_id' => $subscriptionId,
                'status' => $subscription->status
            ]);

            return $subscription;

        } catch (Exception $e) {
            Log::error('Failed to resume Stripe subscription', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Update subscription payment method
     */
    public function updateSubscriptionPaymentMethod(string $subscriptionId, string $paymentMethodId): bool
    {
        try {
            Log::info('Updating subscription payment method', [
                'subscription_id' => $subscriptionId,
                'payment_method_id' => $paymentMethodId
            ]);

            $subscription = Subscription::retrieve($subscriptionId);
            
            // Attach payment method to customer
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId);
            $paymentMethod->attach(['customer' => $subscription->customer]);

            // Update customer's default payment method
            $customer = Customer::retrieve($subscription->customer);
            $customer->invoice_settings = ['default_payment_method' => $paymentMethodId];
            $customer->save();

            Log::info('Subscription payment method updated', [
                'subscription_id' => $subscriptionId
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to update subscription payment method', [
                'subscription_id' => $subscriptionId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get customer invoices
     */
    public function getCustomerInvoices(string $customerId, int $limit = 10): array
    {
        try {
            $invoices = Invoice::all([
                'customer' => $customerId,
                'limit' => $limit
            ]);

            return $invoices->data;

        } catch (Exception $e) {
            Log::error('Failed to get customer invoices', [
                'customer_id' => $customerId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        try {
            Webhook::constructEvent($payload, $signature, $this->webhookSecret);
            return true;
        } catch (SignatureVerificationException $e) {
            Log::warning('Invalid webhook signature', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Handle webhook event
     */
    public function handleWebhookEvent(array $event): void
    {
        try {
            Log::info('Processing Stripe webhook event', [
                'event_id' => $event['id'],
                'event_type' => $event['type']
            ]);

            // Store webhook event
            $webhookEvent = WebhookEvent::create([
                'stripe_event_id' => $event['id'],
                'type' => $event['type'],
                'object_id' => $event['data']['object']['id'] ?? null,
                'object_type' => $event['data']['object']['object'] ?? null,
                'data' => $event,
                'status' => 'pending'
            ]);

            // Process based on event type
            switch ($event['type']) {
                case 'customer.subscription.created':
                case 'customer.subscription.updated':
                    $this->handleSubscriptionEvent($event['data']['object']);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionDeleted($event['data']['object']);
                    break;

                case 'invoice.payment_succeeded':
                    $this->handleInvoicePaymentSucceeded($event['data']['object']);
                    break;

                case 'invoice.payment_failed':
                    $this->handleInvoicePaymentFailed($event['data']['object']);
                    break;

                case 'invoice.created':
                case 'invoice.updated':
                    $this->handleInvoiceEvent($event['data']['object']);
                    break;

                case 'payment_method.attached':
                    $this->handlePaymentMethodAttached($event['data']['object']);
                    break;

                default:
                    Log::info('Unhandled webhook event type', [
                        'event_type' => $event['type']
                    ]);
            }

            // Mark as processed
            $webhookEvent->update([
                'status' => 'processed',
                'processed_at' => now()
            ]);

        } catch (Exception $e) {
            Log::error('Webhook event processing failed', [
                'event_id' => $event['id'] ?? 'unknown',
                'event_type' => $event['type'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);

            // Mark as failed
            if (isset($webhookEvent)) {
                $webhookEvent->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage(),
                    'attempts' => $webhookEvent->attempts + 1
                ]);
            }
        }
    }

    /**
     * Handle subscription events
     */
    protected function handleSubscriptionEvent(array $subscriptionData): void
    {
        $subscription = LocalSubscription::where('stripe_subscription_id', $subscriptionData['id'])->first();

        if (!$subscription) {
            Log::warning('Local subscription not found for Stripe subscription', [
                'stripe_subscription_id' => $subscriptionData['id']
            ]);
            return;
        }

        // Update subscription data
        $subscription->update([
            'status' => $subscriptionData['status'],
            'current_period_start' => $subscriptionData['current_period_start'] ? 
                now()->createFromTimestamp($subscriptionData['current_period_start']) : null,
            'current_period_end' => $subscriptionData['current_period_end'] ? 
                now()->createFromTimestamp($subscriptionData['current_period_end']) : null,
            'trial_start' => $subscriptionData['trial_start'] ? 
                now()->createFromTimestamp($subscriptionData['trial_start']) : null,
            'trial_end' => $subscriptionData['trial_end'] ? 
                now()->createFromTimestamp($subscriptionData['trial_end']) : null,
            'canceled_at' => $subscriptionData['canceled_at'] ? 
                now()->createFromTimestamp($subscriptionData['canceled_at']) : null,
            'ends_at' => $subscriptionData['cancel_at_period_end'] ? 
                now()->createFromTimestamp($subscriptionData['current_period_end']) : null
        ]);

        Log::info('Subscription updated from webhook', [
            'subscription_id' => $subscription->id,
            'status' => $subscriptionData['status']
        ]);
    }

    /**
     * Handle subscription deletion
     */
    protected function handleSubscriptionDeleted(array $subscriptionData): void
    {
        $subscription = LocalSubscription::where('stripe_subscription_id', $subscriptionData['id'])->first();

        if ($subscription) {
            $subscription->update([
                'status' => 'canceled',
                'ends_at' => now()
            ]);

            // Downgrade user to free plan
            $subscription->user->update(['plan_id' => 'free']);

            Log::info('Subscription deleted from webhook', [
                'subscription_id' => $subscription->id
            ]);
        }
    }

    /**
     * Handle successful invoice payment
     */
    protected function handleInvoicePaymentSucceeded(array $invoiceData): void
    {
        $this->updateOrCreateInvoice($invoiceData);

        Log::info('Invoice payment succeeded', [
            'invoice_id' => $invoiceData['id']
        ]);
    }

    /**
     * Handle failed invoice payment
     */
    protected function handleInvoicePaymentFailed(array $invoiceData): void
    {
        $this->updateOrCreateInvoice($invoiceData);

        // Handle subscription status if needed
        if ($invoiceData['subscription']) {
            $subscription = LocalSubscription::where('stripe_subscription_id', $invoiceData['subscription'])->first();
            if ($subscription && $subscription->status !== 'past_due') {
                $subscription->update(['status' => 'past_due']);
            }
        }

        Log::warning('Invoice payment failed', [
            'invoice_id' => $invoiceData['id']
        ]);
    }

    /**
     * Handle invoice events
     */
    protected function handleInvoiceEvent(array $invoiceData): void
    {
        $this->updateOrCreateInvoice($invoiceData);
    }

    /**
     * Handle payment method attached
     */
    protected function handlePaymentMethodAttached(array $paymentMethodData): void
    {
        // Find user by customer ID
        $user = User::where('stripe_customer_id', $paymentMethodData['customer'])->first();
        
        if (!$user) {
            Log::warning('User not found for payment method', [
                'customer_id' => $paymentMethodData['customer']
            ]);
            return;
        }

        // Create or update payment method record
        LocalPaymentMethod::updateOrCreate(
            ['stripe_payment_method_id' => $paymentMethodData['id']],
            [
                'user_id' => $user->id,
                'stripe_customer_id' => $paymentMethodData['customer'],
                'type' => $paymentMethodData['type'],
                'brand' => $paymentMethodData['card']['brand'] ?? null,
                'last_four' => $paymentMethodData['card']['last4'] ?? null,
                'exp_month' => $paymentMethodData['card']['exp_month'] ?? null,
                'exp_year' => $paymentMethodData['card']['exp_year'] ?? null,
                'country' => $paymentMethodData['card']['country'] ?? null,
                'funding' => $paymentMethodData['card']['funding'] ?? null
            ]
        );

        Log::info('Payment method attached', [
            'user_id' => $user->id,
            'payment_method_id' => $paymentMethodData['id']
        ]);
    }

    /**
     * Update or create invoice record
     */
    protected function updateOrCreateInvoice(array $invoiceData): void
    {
        // Find user by customer ID
        $user = User::where('stripe_customer_id', $invoiceData['customer'])->first();
        
        if (!$user) {
            Log::warning('User not found for invoice', [
                'customer_id' => $invoiceData['customer']
            ]);
            return;
        }

        // Find subscription if exists
        $subscription = null;
        if ($invoiceData['subscription']) {
            $subscription = LocalSubscription::where('stripe_subscription_id', $invoiceData['subscription'])->first();
        }

        LocalInvoice::updateOrCreate(
            ['stripe_invoice_id' => $invoiceData['id']],
            [
                'user_id' => $user->id,
                'subscription_id' => $subscription?->id,
                'stripe_customer_id' => $invoiceData['customer'],
                'stripe_subscription_id' => $invoiceData['subscription'],
                'number' => $invoiceData['number'],
                'status' => $invoiceData['status'],
                'amount_due' => $invoiceData['amount_due'] / 100,
                'amount_paid' => $invoiceData['amount_paid'] / 100,
                'amount_remaining' => $invoiceData['amount_remaining'] / 100,
                'subtotal' => $invoiceData['subtotal'] / 100,
                'tax' => $invoiceData['tax'] / 100,
                'total' => $invoiceData['total'] / 100,
                'currency' => strtoupper($invoiceData['currency']),
                'description' => $invoiceData['description'],
                'hosted_invoice_url' => $invoiceData['hosted_invoice_url'],
                'invoice_pdf' => $invoiceData['invoice_pdf'],
                'period_start' => $invoiceData['period_start'] ? 
                    now()->createFromTimestamp($invoiceData['period_start']) : null,
                'period_end' => $invoiceData['period_end'] ? 
                    now()->createFromTimestamp($invoiceData['period_end']) : null,
                'due_date' => $invoiceData['due_date'] ? 
                    now()->createFromTimestamp($invoiceData['due_date']) : null,
                'paid_at' => $invoiceData['status_transitions']['paid_at'] ? 
                    now()->createFromTimestamp($invoiceData['status_transitions']['paid_at']) : null,
                'line_items' => $invoiceData['lines']['data'] ?? null
            ]
        );
    }
}
