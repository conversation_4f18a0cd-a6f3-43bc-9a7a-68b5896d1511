<?php

namespace App\Services;

use Google\Client as GoogleClient;
use Google\Service\Analytics;
use Google\Service\AnalyticsData;
use Google\Service\Oauth2;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class GoogleAuthService
{
    protected GoogleClient $client;
    protected string $clientId;
    protected string $clientSecret;
    protected string $redirectUri;

    public function __construct()
    {
        $this->clientId = config('services.google.client_id');
        $this->clientSecret = config('services.google.client_secret');
        $this->redirectUri = config('services.google.redirect_uri');

        $this->client = new GoogleClient();
        $this->client->setClientId($this->clientId);
        $this->client->setClientSecret($this->clientSecret);
        $this->client->setRedirectUri($this->redirectUri);
        $this->client->setScopes([
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile'
        ]);
        $this->client->setAccessType('offline');
        $this->client->setPrompt('consent');
    }

    /**
     * Verify Google OAuth access token and get user info
     */
    public function verifyToken(string $accessToken): ?array
    {
        try {
            Log::info('Verifying Google access token');

            // First, verify the token with Google's tokeninfo endpoint
            $response = Http::get('https://oauth2.googleapis.com/tokeninfo', [
                'access_token' => $accessToken
            ]);

            if (!$response->successful()) {
                Log::warning('Google token verification failed', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return null;
            }

            $tokenInfo = $response->json();

            // Verify the token is for our client
            if ($tokenInfo['aud'] !== $this->clientId) {
                Log::warning('Token audience mismatch', [
                    'expected' => $this->clientId,
                    'actual' => $tokenInfo['aud'] ?? 'unknown'
                ]);
                return null;
            }

            // Verify required scopes
            $requiredScopes = [
                'https://www.googleapis.com/auth/analytics.readonly',
                'https://www.googleapis.com/auth/userinfo.email'
            ];

            $tokenScopes = explode(' ', $tokenInfo['scope'] ?? '');
            
            foreach ($requiredScopes as $scope) {
                if (!in_array($scope, $tokenScopes)) {
                    Log::warning('Missing required scope', [
                        'required_scope' => $scope,
                        'token_scopes' => $tokenScopes
                    ]);
                    return null;
                }
            }

            // Get user info using the access token
            $this->client->setAccessToken($accessToken);
            $oauth2Service = new Oauth2($this->client);
            $userInfo = $oauth2Service->userinfo->get();

            $userData = [
                'id' => $userInfo->getId(),
                'email' => $userInfo->getEmail(),
                'name' => $userInfo->getName(),
                'picture' => $userInfo->getPicture(),
                'verified_email' => $userInfo->getVerifiedEmail()
            ];

            Log::info('Google token verified successfully', [
                'user_id' => $userData['id'],
                'email' => $userData['email']
            ]);

            return $userData;

        } catch (Exception $e) {
            Log::error('Google token verification error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Check if user has access to GA4 properties
     */
    public function checkGA4Access(string $accessToken): bool
    {
        try {
            Log::info('Checking GA4 access for user');

            $this->client->setAccessToken($accessToken);
            
            // Try to access Analytics Admin API to list account summaries
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json'
            ])->get('https://analyticsadmin.googleapis.com/v1beta/accountSummaries');

            if (!$response->successful()) {
                Log::warning('GA4 access check failed', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }

            $data = $response->json();
            $accountSummaries = $data['accountSummaries'] ?? [];

            // Check if user has any GA4 properties
            $hasGA4Properties = false;
            foreach ($accountSummaries as $account) {
                $properties = $account['propertySummaries'] ?? [];
                foreach ($properties as $property) {
                    // GA4 properties have propertyType = 'PROPERTY_TYPE_ORDINARY'
                    if (($property['propertyType'] ?? '') === 'PROPERTY_TYPE_ORDINARY') {
                        $hasGA4Properties = true;
                        break 2;
                    }
                }
            }

            Log::info('GA4 access check completed', [
                'has_access' => $hasGA4Properties,
                'accounts_count' => count($accountSummaries)
            ]);

            return $hasGA4Properties;

        } catch (Exception $e) {
            Log::error('GA4 access check error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Exchange authorization code for access tokens
     */
    public function exchangeCodeForTokens(string $code): ?array
    {
        try {
            Log::info('Exchanging authorization code for tokens');

            $this->client->authenticate($code);
            $tokens = $this->client->getAccessToken();

            if (!$tokens) {
                Log::warning('Failed to exchange code for tokens');
                return null;
            }

            Log::info('Authorization code exchanged successfully');

            return $tokens;

        } catch (Exception $e) {
            Log::error('Token exchange error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refreshAccessToken(string $refreshToken): ?array
    {
        try {
            Log::info('Refreshing access token');

            $this->client->refreshToken($refreshToken);
            $tokens = $this->client->getAccessToken();

            if (!$tokens) {
                Log::warning('Failed to refresh access token');
                return null;
            }

            Log::info('Access token refreshed successfully');

            return $tokens;

        } catch (Exception $e) {
            Log::error('Token refresh error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get GA4 properties for authenticated user
     */
    public function getGA4Properties(string $accessToken): array
    {
        try {
            $cacheKey = 'ga4_properties_' . md5($accessToken);
            
            return Cache::remember($cacheKey, 3600, function () use ($accessToken) {
                Log::info('Fetching GA4 properties');

                $response = Http::withHeaders([
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json'
                ])->get('https://analyticsadmin.googleapis.com/v1beta/accountSummaries');

                if (!$response->successful()) {
                    Log::error('Failed to fetch GA4 properties', [
                        'status' => $response->status(),
                        'response' => $response->body()
                    ]);
                    return [];
                }

                $data = $response->json();
                $accountSummaries = $data['accountSummaries'] ?? [];
                $ga4Properties = [];

                foreach ($accountSummaries as $account) {
                    $properties = $account['propertySummaries'] ?? [];
                    
                    foreach ($properties as $property) {
                        // Only include GA4 properties
                        if (($property['propertyType'] ?? '') === 'PROPERTY_TYPE_ORDINARY') {
                            $ga4Properties[] = [
                                'account_id' => str_replace('accounts/', '', $account['account']),
                                'account_name' => $account['displayName'],
                                'property_id' => $property['property'],
                                'property_name' => $property['displayName'],
                                'website_url' => $property['websiteUrl'] ?? null,
                                'create_time' => $property['createTime'] ?? null,
                                'time_zone' => $property['timeZone'] ?? null,
                                'currency_code' => $property['currencyCode'] ?? null
                            ];
                        }
                    }
                }

                Log::info('GA4 properties fetched successfully', [
                    'properties_count' => count($ga4Properties)
                ]);

                return $ga4Properties;
            });

        } catch (Exception $e) {
            Log::error('Error fetching GA4 properties', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Validate GA4 property access for user
     */
    public function validatePropertyAccess(string $accessToken, string $propertyId): bool
    {
        try {
            Log::info('Validating property access', ['property_id' => $propertyId]);

            $properties = $this->getGA4Properties($accessToken);
            
            foreach ($properties as $property) {
                if ($property['property_id'] === $propertyId) {
                    Log::info('Property access validated', ['property_id' => $propertyId]);
                    return true;
                }
            }

            Log::warning('Property access denied', ['property_id' => $propertyId]);
            return false;

        } catch (Exception $e) {
            Log::error('Property access validation error', [
                'property_id' => $propertyId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get authorization URL for OAuth flow
     */
    public function getAuthorizationUrl(array $options = []): string
    {
        $state = $options['state'] ?? bin2hex(random_bytes(16));
        
        $this->client->setState($state);
        
        return $this->client->createAuthUrl();
    }

    /**
     * Revoke access token
     */
    public function revokeToken(string $accessToken): bool
    {
        try {
            Log::info('Revoking access token');

            $response = Http::post('https://oauth2.googleapis.com/revoke', [
                'token' => $accessToken
            ]);

            $success = $response->successful();

            Log::info('Token revocation completed', ['success' => $success]);

            return $success;

        } catch (Exception $e) {
            Log::error('Token revocation error', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
