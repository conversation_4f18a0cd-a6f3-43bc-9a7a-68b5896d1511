<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Subscription;
use App\Models\User;
use Stripe\Stripe;
use Stripe\Subscription as StripeSubscription;
use Illuminate\Support\Facades\Log;

class SyncStripeSubscriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stripe:sync-subscriptions 
                            {--user= : Sync subscriptions for specific user ID}
                            {--limit=100 : Maximum number of subscriptions to sync}
                            {--force : Force sync even if recently synced}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync subscription data with <PERSON><PERSON>';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting Stripe subscription sync...');

        // Initialize Stripe
        Stripe::setApiKey(config('services.stripe.secret'));

        $userId = $this->option('user');
        $limit = $this->option('limit');
        $force = $this->option('force');

        // Get subscriptions to sync
        $query = Subscription::query();

        if ($userId) {
            $query->where('user_id', $userId);
            $this->info("Syncing subscriptions for user ID: {$userId}");
        } else {
            // Only sync active subscriptions or recently updated ones
            if (!$force) {
                $query->where(function ($q) {
                    $q->whereIn('status', ['active', 'past_due', 'unpaid'])
                      ->orWhere('updated_at', '>', now()->subHours(24));
                });
            }
        }

        $subscriptions = $query->limit($limit)->get();

        if ($subscriptions->isEmpty()) {
            $this->info('No subscriptions to sync');
            return self::SUCCESS;
        }

        $this->info("Found {$subscriptions->count()} subscriptions to sync");

        $synced = 0;
        $errors = 0;

        $progressBar = $this->output->createProgressBar($subscriptions->count());
        $progressBar->start();

        foreach ($subscriptions as $subscription) {
            try {
                // Fetch subscription from Stripe
                $stripeSubscription = StripeSubscription::retrieve($subscription->stripe_subscription_id);

                // Update local subscription with Stripe data
                $updateData = [
                    'status' => $stripeSubscription->status,
                    'current_period_start' => $stripeSubscription->current_period_start ? 
                        now()->createFromTimestamp($stripeSubscription->current_period_start) : null,
                    'current_period_end' => $stripeSubscription->current_period_end ? 
                        now()->createFromTimestamp($stripeSubscription->current_period_end) : null,
                    'trial_start' => $stripeSubscription->trial_start ? 
                        now()->createFromTimestamp($stripeSubscription->trial_start) : null,
                    'trial_end' => $stripeSubscription->trial_end ? 
                        now()->createFromTimestamp($stripeSubscription->trial_end) : null,
                    'canceled_at' => $stripeSubscription->canceled_at ? 
                        now()->createFromTimestamp($stripeSubscription->canceled_at) : null,
                ];

                // Set ends_at based on cancel_at_period_end
                if ($stripeSubscription->cancel_at_period_end) {
                    $updateData['ends_at'] = $updateData['current_period_end'];
                } elseif ($stripeSubscription->status === 'canceled') {
                    $updateData['ends_at'] = $updateData['canceled_at'] ?? now();
                } else {
                    $updateData['ends_at'] = null;
                }

                $subscription->update($updateData);

                // Update user plan if subscription status changed
                $user = $subscription->user;
                if ($stripeSubscription->status === 'active' && $user->plan_id !== $subscription->plan_id) {
                    $user->update(['plan_id' => $subscription->plan_id]);
                } elseif (in_array($stripeSubscription->status, ['canceled', 'incomplete_expired']) && $user->plan_id !== 'free') {
                    $user->update(['plan_id' => 'free']);
                }

                $synced++;

            } catch (\Exception $e) {
                $this->newLine();
                $this->error("Failed to sync subscription {$subscription->id}: {$e->getMessage()}");
                
                Log::error('Subscription sync failed', [
                    'subscription_id' => $subscription->id,
                    'stripe_subscription_id' => $subscription->stripe_subscription_id,
                    'error' => $e->getMessage()
                ]);

                $errors++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $this->info("Sync complete:");
        $this->line("- Synced: {$synced}");
        $this->line("- Errors: {$errors}");

        if ($errors > 0) {
            $this->warn("Some subscriptions failed to sync. Check logs for details.");
            return self::FAILURE;
        }

        return self::SUCCESS;
    }
}
