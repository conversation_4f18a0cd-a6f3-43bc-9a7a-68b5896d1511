<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WebhookEvent;
use App\Services\StripeService;
use Illuminate\Support\Facades\Log;

class ProcessFailedWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webhooks:process-failed 
                            {--limit=10 : Maximum number of webhooks to process}
                            {--max-attempts=3 : Maximum number of retry attempts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process failed webhook events';

    protected StripeService $stripeService;

    /**
     * Create a new command instance.
     */
    public function __construct(StripeService $stripeService)
    {
        parent::__construct();
        $this->stripeService = $stripeService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $limit = $this->option('limit');
        $maxAttempts = $this->option('max-attempts');

        $this->info("Processing failed webhooks (limit: {$limit}, max attempts: {$maxAttempts})");

        // Get failed webhook events that haven't exceeded max attempts
        $failedWebhooks = WebhookEvent::where('status', 'failed')
            ->where('attempts', '<', $maxAttempts)
            ->orderBy('created_at', 'asc')
            ->limit($limit)
            ->get();

        if ($failedWebhooks->isEmpty()) {
            $this->info('No failed webhooks to process');
            return self::SUCCESS;
        }

        $this->info("Found {$failedWebhooks->count()} failed webhooks to retry");

        $processed = 0;
        $succeeded = 0;
        $failed = 0;

        foreach ($failedWebhooks as $webhook) {
            $this->line("Processing webhook {$webhook->stripe_event_id} (attempt {$webhook->attempts + 1})");

            try {
                // Reset status to pending
                $webhook->update([
                    'status' => 'pending',
                    'error_message' => null,
                    'attempts' => $webhook->attempts + 1
                ]);

                // Process the webhook event
                $this->stripeService->handleWebhookEvent($webhook->data);

                // Mark as processed
                $webhook->update([
                    'status' => 'processed',
                    'processed_at' => now()
                ]);

                $this->info("✓ Successfully processed webhook {$webhook->stripe_event_id}");
                $succeeded++;

            } catch (\Exception $e) {
                // Mark as failed again
                $webhook->update([
                    'status' => 'failed',
                    'error_message' => $e->getMessage()
                ]);

                $this->error("✗ Failed to process webhook {$webhook->stripe_event_id}: {$e->getMessage()}");
                $failed++;

                Log::error('Failed webhook retry failed', [
                    'webhook_id' => $webhook->id,
                    'stripe_event_id' => $webhook->stripe_event_id,
                    'attempt' => $webhook->attempts,
                    'error' => $e->getMessage()
                ]);
            }

            $processed++;
        }

        $this->newLine();
        $this->info("Processing complete:");
        $this->line("- Total processed: {$processed}");
        $this->line("- Succeeded: {$succeeded}");
        $this->line("- Failed: {$failed}");

        // Check for webhooks that have exceeded max attempts
        $exhaustedWebhooks = WebhookEvent::where('status', 'failed')
            ->where('attempts', '>=', $maxAttempts)
            ->count();

        if ($exhaustedWebhooks > 0) {
            $this->warn("Warning: {$exhaustedWebhooks} webhooks have exceeded maximum retry attempts");
        }

        return self::SUCCESS;
    }
}
