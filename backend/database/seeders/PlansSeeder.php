<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Plan;

class PlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Free Plan
        Plan::updateOrCreate(
            ['id' => 'free'],
            [
                'name' => 'Plan Gratuito',
                'description' => 'Perfecto para empezar con análisis básicos de GA4',
                'price' => 0.00,
                'currency' => 'USD',
                'billing_period' => 'monthly',
                'api_call_limit' => 50,
                'features' => [
                    'basic_analytics' => true,
                    'page_analysis' => true,
                    'site_overview' => true,
                    'traffic_sources' => true,
                    'device_breakdown' => true,
                    'monthly_limit' => 50,
                    'comparison_periods' => false,
                    'data_export' => false,
                    'email_reports' => false,
                    'advanced_filters' => false,
                    'priority_support' => false
                ],
                'is_active' => true,
                'sort_order' => 1
            ]
        );

        // Premium Plan
        Plan::updateOrCreate(
            ['id' => 'premium'],
            [
                'name' => 'Plan Premium',
                'description' => 'Análisis avanzados sin límites para profesionales',
                'price' => 9.99,
                'currency' => 'USD',
                'billing_period' => 'monthly',
                'api_call_limit' => null, // unlimited
                'features' => [
                    'basic_analytics' => true,
                    'page_analysis' => true,
                    'site_overview' => true,
                    'traffic_sources' => true,
                    'device_breakdown' => true,
                    'monthly_limit' => null, // unlimited
                    'comparison_periods' => true,
                    'data_export' => true,
                    'email_reports' => true,
                    'advanced_filters' => true,
                    'priority_support' => true,
                    'cohort_analysis' => true,
                    'funnel_analysis' => true,
                    'custom_dimensions' => true,
                    'api_access' => true
                ],
                'is_active' => true,
                'sort_order' => 2
            ]
        );

        // Enterprise Plan (for future use)
        Plan::updateOrCreate(
            ['id' => 'enterprise'],
            [
                'name' => 'Plan Empresarial',
                'description' => 'Solución completa para equipos y empresas',
                'price' => 29.99,
                'currency' => 'USD',
                'billing_period' => 'monthly',
                'api_call_limit' => null, // unlimited
                'features' => [
                    'basic_analytics' => true,
                    'page_analysis' => true,
                    'site_overview' => true,
                    'traffic_sources' => true,
                    'device_breakdown' => true,
                    'monthly_limit' => null, // unlimited
                    'comparison_periods' => true,
                    'data_export' => true,
                    'email_reports' => true,
                    'advanced_filters' => true,
                    'priority_support' => true,
                    'cohort_analysis' => true,
                    'funnel_analysis' => true,
                    'custom_dimensions' => true,
                    'api_access' => true,
                    'team_management' => true,
                    'white_label' => true,
                    'custom_integrations' => true,
                    'dedicated_support' => true,
                    'sla_guarantee' => true
                ],
                'is_active' => false, // Not yet available
                'sort_order' => 3
            ]
        );

        $this->command->info('Plans seeded successfully!');
    }
}
