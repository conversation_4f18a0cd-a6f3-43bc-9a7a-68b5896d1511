<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('google_id')->unique()->index();
            $table->string('email')->unique();
            $table->string('name')->nullable();
            $table->string('avatar_url')->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamp('last_login_at')->nullable();
            
            // Plan and usage tracking
            $table->string('plan_id')->default('free')->index();
            $table->integer('usage_count')->default(0);
            $table->timestamp('usage_reset_at')->nullable();
            
            // User preferences and settings
            $table->json('preferences')->nullable();
            $table->string('selected_property_id')->nullable();
            $table->string('timezone')->default('UTC');
            $table->string('language', 5)->default('es');
            
            // Standard Laravel timestamps
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['plan_id', 'usage_count']);
            $table->index(['usage_reset_at']);
            $table->index(['created_at']);
            $table->index(['last_login_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
