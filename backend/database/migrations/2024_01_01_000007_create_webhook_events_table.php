<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_events', function (Blueprint $table) {
            $table->id();
            $table->string('stripe_event_id')->unique()->index();
            $table->string('type'); // customer.subscription.created, invoice.paid, etc.
            $table->string('object_id')->nullable(); // ID of the object (subscription, invoice, etc.)
            $table->string('object_type')->nullable(); // subscription, invoice, etc.
            $table->json('data'); // Full event data from Stripe
            $table->string('status')->default('pending'); // pending, processed, failed
            $table->text('error_message')->nullable();
            $table->integer('attempts')->default(0);
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('created_at');
            
            // Indexes
            $table->index(['type', 'status']);
            $table->index(['object_id', 'object_type']);
            $table->index(['status', 'created_at']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_events');
    }
};
