APP_NAME="GA4 Insights API"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://pietramundodigital.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=info

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ga4_insights
DB_USERNAME=ga4_user
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=database
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Google OAuth Configuration
GOOGLE_CLIENT_ID=308478830904-7pt651p5lepi60agv9afnmsvmcq2ic4i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://pietramundodigital.com/api/v1/auth/google/callback

# Chrome Extension OAuth Configuration
CHROME_EXTENSION_CLIENT_ID=308478830904-d37c5kjjba68kpa3boog6r8i464gf70a.apps.googleusercontent.com

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_TTL=1440
JWT_REFRESH_TTL=20160

# GA4 API Configuration
GA4_DATA_API_URL=https://analyticsdata.googleapis.com/v1beta
GA4_ADMIN_API_URL=https://analyticsadmin.googleapis.com/v1beta
GA4_TOKEN_INFO_URL=https://oauth2.googleapis.com/tokeninfo

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000
RATE_LIMIT_PER_DAY=10000

# Usage Limits
FREE_PLAN_MONTHLY_LIMIT=50
PREMIUM_PLAN_MONTHLY_LIMIT=null

# Stripe Configuration (for premium subscriptions)
STRIPE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_WEBHOOK_TOLERANCE=300

# Stripe Price IDs (create these in Stripe Dashboard)
STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_premium_monthly_id
STRIPE_PREMIUM_YEARLY_PRICE_ID=price_premium_yearly_id
STRIPE_ENTERPRISE_MONTHLY_PRICE_ID=price_enterprise_monthly_id
STRIPE_ENTERPRISE_YEARLY_PRICE_ID=price_enterprise_yearly_id

# Cache Configuration
CACHE_TTL_USER_DATA=1440
CACHE_TTL_GA4_DATA=15
CACHE_TTL_PROPERTIES=1440

# Logging Configuration
LOG_SLACK_WEBHOOK_URL=
LOG_DISCORD_WEBHOOK_URL=

# Monitoring
SENTRY_LARAVEL_DSN=
BUGSNAG_API_KEY=

# CORS Configuration
CORS_ALLOWED_ORIGINS="chrome-extension://*,https://pietramundodigital.com,http://localhost:*"
CORS_ALLOWED_METHODS="GET,POST,PUT,DELETE,OPTIONS"
CORS_ALLOWED_HEADERS="Content-Type,Authorization,X-Requested-With,X-Request-ID,X-Extension-Version,X-Chrome-Extension-ID"

# Security
SANCTUM_STATEFUL_DOMAINS=pietramundodigital.com
SESSION_DOMAIN=.pietramundodigital.com

# Extension Configuration
EXTENSION_CURRENT_VERSION=0.1.0
EXTENSION_MIN_SUPPORTED_VERSION=0.1.0

# Feature Flags
FEATURE_COMPARISON_ENABLED=false
FEATURE_EXPORT_ENABLED=false
FEATURE_EMAIL_REPORTS_ENABLED=false
FEATURE_ADVANCED_FILTERS_ENABLED=false

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="API en mantenimiento. Volveremos pronto."

# Analytics
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
MIXPANEL_TOKEN=

# Backup Configuration
BACKUP_DISK=s3
BACKUP_NOTIFICATION_MAIL=<EMAIL>
