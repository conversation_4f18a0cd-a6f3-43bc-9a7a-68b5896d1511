# Stripe Integration Documentation

## Overview

This document describes the Stripe integration for GA4 Insights subscription management, including payment processing, webhooks, and subscription lifecycle management.

## Architecture

### Components

1. **StripeService** - Main service for Stripe API interactions
2. **SubscriptionController** - API endpoints for subscription management
3. **Webhook Processing** - Handles Stripe webhook events
4. **Models** - Database models for subscriptions, invoices, payment methods
5. **Jobs** - Asynchronous webhook processing
6. **Commands** - CLI tools for maintenance and sync

### Database Schema

```sql
-- Subscriptions
subscriptions (
    id, user_id, stripe_subscription_id, stripe_customer_id,
    stripe_price_id, plan_id, status, amount, currency,
    interval, current_period_start, current_period_end,
    trial_start, trial_end, canceled_at, ends_at
)

-- Invoices
invoices (
    id, user_id, subscription_id, stripe_invoice_id,
    number, status, amount_due, amount_paid, total,
    hosted_invoice_url, invoice_pdf, due_date, paid_at
)

-- Payment Methods
payment_methods (
    id, user_id, stripe_payment_method_id, type,
    brand, last_four, exp_month, exp_year, is_default
)

-- Webhook Events
webhook_events (
    id, stripe_event_id, type, object_id, object_type,
    data, status, error_message, attempts, processed_at
)
```

## Setup Instructions

### 1. Stripe Dashboard Configuration

1. **Create Stripe Account**
   - Sign up at https://stripe.com
   - Complete account verification

2. **Create Products and Prices**
   ```bash
   # Premium Monthly
   stripe products create --name="GA4 Insights Premium" --description="Premium plan with unlimited queries"
   stripe prices create --product=prod_xxx --unit-amount=999 --currency=usd --recurring[interval]=month

   # Premium Yearly (20% discount)
   stripe prices create --product=prod_xxx --unit-amount=9590 --currency=usd --recurring[interval]=year
   ```

3. **Configure Webhooks**
   - Endpoint URL: `https://pietramundodigital.com/api/v1/webhooks/stripe`
   - Events to send:
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
     - `invoice.created`
     - `invoice.updated`
     - `payment_method.attached`
     - `payment_method.detached`

### 2. Environment Configuration

```bash
# Copy environment variables
cp .env.example .env

# Configure Stripe keys
STRIPE_KEY=pk_live_your_publishable_key
STRIPE_SECRET=sk_live_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Configure price IDs
STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_1234567890
STRIPE_PREMIUM_YEARLY_PRICE_ID=price_0987654321
```

### 3. Database Migration

```bash
php artisan migrate
php artisan db:seed --class=PlansSeeder
```

### 4. Queue Configuration

```bash
# Configure Redis for queues
QUEUE_CONNECTION=redis

# Start queue worker
php artisan queue:work --queue=default,webhooks
```

## API Endpoints

### Subscription Management

#### Get Current Subscription
```http
GET /api/v1/subscriptions/current
Authorization: Bearer {jwt_token}
```

#### Create Subscription
```http
POST /api/v1/subscriptions/create
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "plan_id": "premium",
    "payment_method_id": "pm_1234567890",
    "billing_period": "monthly"
}
```

#### Cancel Subscription
```http
POST /api/v1/subscriptions/cancel
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "cancel_at_period_end": true
}
```

#### Resume Subscription
```http
POST /api/v1/subscriptions/resume
Authorization: Bearer {jwt_token}
```

#### Update Payment Method
```http
POST /api/v1/subscriptions/update-payment-method
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
    "payment_method_id": "pm_0987654321"
}
```

#### Get Invoices
```http
GET /api/v1/subscriptions/invoices?limit=10&offset=0
Authorization: Bearer {jwt_token}
```

### Webhook Endpoint

```http
POST /api/v1/webhooks/stripe
Stripe-Signature: {webhook_signature}
Content-Type: application/json

{
    "id": "evt_1234567890",
    "type": "customer.subscription.created",
    "data": {
        "object": {
            // Stripe object data
        }
    }
}
```

## Webhook Processing

### Supported Events

1. **customer.subscription.created** - New subscription created
2. **customer.subscription.updated** - Subscription status/details changed
3. **customer.subscription.deleted** - Subscription canceled
4. **invoice.payment_succeeded** - Payment successful
5. **invoice.payment_failed** - Payment failed
6. **invoice.created/updated** - Invoice created or updated
7. **payment_method.attached** - Payment method added

### Processing Flow

1. **Webhook Received** → Signature verification
2. **Event Stored** → Create WebhookEvent record
3. **Job Queued** → ProcessStripeWebhook job
4. **Event Processed** → Update local data
5. **Status Updated** → Mark as processed/failed

### Error Handling

- **Retry Logic**: 3 attempts with exponential backoff
- **Failed Events**: Stored for manual review
- **Monitoring**: Logs and notifications for failures

## CLI Commands

### Sync Subscriptions
```bash
# Sync all active subscriptions
php artisan stripe:sync-subscriptions

# Sync specific user
php artisan stripe:sync-subscriptions --user=123

# Force sync all subscriptions
php artisan stripe:sync-subscriptions --force
```

### Process Failed Webhooks
```bash
# Process failed webhooks
php artisan webhooks:process-failed

# Limit number of webhooks to process
php artisan webhooks:process-failed --limit=5

# Set max retry attempts
php artisan webhooks:process-failed --max-attempts=5
```

## Frontend Integration

### Stripe Elements Setup

```javascript
// Initialize Stripe
const stripe = Stripe('pk_live_your_publishable_key');
const elements = stripe.elements();

// Create card element
const cardElement = elements.create('card');
cardElement.mount('#card-element');

// Handle form submission
async function createSubscription(planId) {
    const {paymentMethod, error} = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
    });

    if (error) {
        console.error('Payment method creation failed:', error);
        return;
    }

    // Send to backend
    const response = await fetch('/api/v1/subscriptions/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${jwtToken}`
        },
        body: JSON.stringify({
            plan_id: planId,
            payment_method_id: paymentMethod.id,
            billing_period: 'monthly'
        })
    });

    const result = await response.json();
    
    if (result.success) {
        console.log('Subscription created successfully');
        // Redirect to success page
    } else {
        console.error('Subscription creation failed:', result.error);
    }
}
```

## Security Considerations

### Webhook Security
- **Signature Verification**: All webhooks verified with Stripe signature
- **Idempotency**: Duplicate events handled gracefully
- **Rate Limiting**: Webhook endpoint protected against abuse

### Payment Security
- **PCI Compliance**: No card data stored locally
- **Stripe Elements**: Secure card input handling
- **Token-based**: Payment methods tokenized by Stripe

### API Security
- **JWT Authentication**: All endpoints require valid JWT
- **Permission Checks**: Premium features require active subscription
- **Input Validation**: All inputs validated and sanitized

## Monitoring and Logging

### Key Metrics
- Subscription creation/cancellation rates
- Payment success/failure rates
- Webhook processing success rates
- Revenue metrics

### Logging
- All Stripe API calls logged
- Webhook events logged with full context
- Error tracking with stack traces
- Performance metrics

### Alerts
- Failed webhook processing
- Payment failures
- Subscription cancellations
- API errors

## Testing

### Test Mode Setup
```bash
# Use test keys
STRIPE_KEY=pk_test_your_test_key
STRIPE_SECRET=sk_test_your_test_key

# Test webhook endpoint
STRIPE_WEBHOOK_SECRET=whsec_test_webhook_secret
```

### Test Cards
```
# Successful payment
****************

# Declined payment
****************

# Requires authentication
****************
```

### Testing Webhooks
```bash
# Install Stripe CLI
stripe listen --forward-to localhost:8000/api/v1/webhooks/stripe

# Trigger test events
stripe trigger customer.subscription.created
stripe trigger invoice.payment_succeeded
```

## Troubleshooting

### Common Issues

1. **Webhook Signature Verification Failed**
   - Check webhook secret configuration
   - Verify endpoint URL is correct
   - Ensure raw request body is used

2. **Subscription Not Created**
   - Check Stripe logs for API errors
   - Verify price IDs are correct
   - Check payment method validity

3. **Payment Failures**
   - Review Stripe Dashboard for decline reasons
   - Check customer payment method status
   - Verify billing information

### Debug Commands
```bash
# Check webhook events
php artisan tinker
>>> WebhookEvent::failed()->latest()->take(5)->get()

# Check subscription status
>>> User::find(123)->currentSubscription()

# Sync specific subscription
>>> php artisan stripe:sync-subscriptions --user=123
```

## Production Deployment

### Pre-deployment Checklist
- [ ] Live Stripe keys configured
- [ ] Webhook endpoint accessible
- [ ] Queue workers running
- [ ] Database migrations applied
- [ ] SSL certificate valid
- [ ] Monitoring configured

### Post-deployment Verification
- [ ] Test subscription creation
- [ ] Verify webhook processing
- [ ] Check payment flows
- [ ] Monitor error logs
- [ ] Validate email notifications
