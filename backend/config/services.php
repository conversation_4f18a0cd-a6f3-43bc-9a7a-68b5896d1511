<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Google Services Configuration
    |--------------------------------------------------------------------------
    */

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect_uri' => env('GOOGLE_REDIRECT_URI'),
        'scopes' => [
            'https://www.googleapis.com/auth/analytics.readonly',
            'https://www.googleapis.com/auth/userinfo.email',
            'https://www.googleapis.com/auth/userinfo.profile'
        ],
        'token_info_url' => env('GA4_TOKEN_INFO_URL', 'https://oauth2.googleapis.com/tokeninfo'),
        'revoke_url' => 'https://oauth2.googleapis.com/revoke'
    ],

    /*
    |--------------------------------------------------------------------------
    | GA4 API Configuration
    |--------------------------------------------------------------------------
    */

    'ga4' => [
        'data_api_url' => env('GA4_DATA_API_URL', 'https://analyticsdata.googleapis.com/v1beta'),
        'admin_api_url' => env('GA4_ADMIN_API_URL', 'https://analyticsadmin.googleapis.com/v1beta'),
        'rate_limit' => [
            'requests_per_minute' => env('GA4_RATE_LIMIT_PER_MINUTE', 60),
            'requests_per_hour' => env('GA4_RATE_LIMIT_PER_HOUR', 1000),
            'requests_per_day' => env('GA4_RATE_LIMIT_PER_DAY', 10000)
        ],
        'cache_ttl' => [
            'properties' => env('CACHE_TTL_PROPERTIES', 1440), // 24 hours
            'recent_data' => env('CACHE_TTL_GA4_DATA', 15), // 15 minutes
            'historical_data' => 1440, // 24 hours
            'user_data' => env('CACHE_TTL_USER_DATA', 1440) // 24 hours
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | JWT Configuration
    |--------------------------------------------------------------------------
    */

    'jwt' => [
        'secret' => env('JWT_SECRET'),
        'ttl' => env('JWT_TTL', 1440), // 24 hours in minutes
        'refresh_ttl' => env('JWT_REFRESH_TTL', 20160), // 2 weeks in minutes
        'algo' => 'HS256',
        'required_claims' => [
            'iss',
            'iat',
            'exp',
            'nbf',
            'sub',
            'jti'
        ],
        'persistent_claims' => [
            // Claims that should persist when refreshing
        ],
        'lock_subject' => true,
        'leeway' => env('JWT_LEEWAY', 0),
        'blacklist_enabled' => env('JWT_BLACKLIST_ENABLED', true),
        'blacklist_grace_period' => env('JWT_BLACKLIST_GRACE_PERIOD', 0),
        'decrypt_cookies' => false,
        'providers' => [
            'jwt' => 'App\Services\JWTService',
            'auth' => 'App\Services\JWTAuthService',
            'storage' => 'App\Services\JWTStorageService'
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Stripe Configuration
    |--------------------------------------------------------------------------
    */

    'stripe' => [
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook_secret' => env('STRIPE_WEBHOOK_SECRET'),
        'api_version' => '2023-10-16',
        'plans' => [
            'premium_monthly' => env('STRIPE_PREMIUM_MONTHLY_PRICE_ID', 'price_premium_monthly'),
            'premium_yearly' => env('STRIPE_PREMIUM_YEARLY_PRICE_ID', 'price_premium_yearly'),
            'enterprise_monthly' => env('STRIPE_ENTERPRISE_MONTHLY_PRICE_ID', 'price_enterprise_monthly'),
            'enterprise_yearly' => env('STRIPE_ENTERPRISE_YEARLY_PRICE_ID', 'price_enterprise_yearly')
        ],
        'webhooks' => [
            'tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300), // 5 minutes
            'events' => [
                'customer.subscription.created',
                'customer.subscription.updated',
                'customer.subscription.deleted',
                'invoice.payment_succeeded',
                'invoice.payment_failed',
                'invoice.created',
                'invoice.updated',
                'payment_method.attached',
                'payment_method.detached',
                'customer.created',
                'customer.updated'
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Usage Limits Configuration
    |--------------------------------------------------------------------------
    */

    'usage_limits' => [
        'free_plan' => [
            'monthly_limit' => env('FREE_PLAN_MONTHLY_LIMIT', 50),
            'daily_limit' => null,
            'hourly_limit' => null
        ],
        'premium_plan' => [
            'monthly_limit' => env('PREMIUM_PLAN_MONTHLY_LIMIT', null), // unlimited
            'daily_limit' => null,
            'hourly_limit' => null
        ],
        'rate_limiting' => [
            'per_minute' => env('RATE_LIMIT_PER_MINUTE', 60),
            'per_hour' => env('RATE_LIMIT_PER_HOUR', 1000),
            'per_day' => env('RATE_LIMIT_PER_DAY', 10000)
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring and Logging Services
    |--------------------------------------------------------------------------
    */

    'sentry' => [
        'dsn' => env('SENTRY_LARAVEL_DSN'),
        'traces_sample_rate' => env('SENTRY_TRACES_SAMPLE_RATE', 0.1),
        'profiles_sample_rate' => env('SENTRY_PROFILES_SAMPLE_RATE', 0.1)
    ],

    'bugsnag' => [
        'api_key' => env('BUGSNAG_API_KEY')
    ],

    'slack' => [
        'webhook_url' => env('LOG_SLACK_WEBHOOK_URL'),
        'channel' => env('LOG_SLACK_CHANNEL', '#ga4-insights-logs'),
        'username' => env('LOG_SLACK_USERNAME', 'GA4 Insights Bot'),
        'emoji' => env('LOG_SLACK_EMOJI', ':robot_face:')
    ],

    'discord' => [
        'webhook_url' => env('LOG_DISCORD_WEBHOOK_URL')
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics and Tracking
    |--------------------------------------------------------------------------
    */

    'analytics' => [
        'google_analytics_id' => env('GOOGLE_ANALYTICS_ID'),
        'mixpanel_token' => env('MIXPANEL_TOKEN'),
        'hotjar_id' => env('HOTJAR_ID')
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */

    'features' => [
        'comparison_enabled' => env('FEATURE_COMPARISON_ENABLED', false),
        'export_enabled' => env('FEATURE_EXPORT_ENABLED', false),
        'email_reports_enabled' => env('FEATURE_EMAIL_REPORTS_ENABLED', false),
        'advanced_filters_enabled' => env('FEATURE_ADVANCED_FILTERS_ENABLED', false),
        'cohort_analysis_enabled' => env('FEATURE_COHORT_ANALYSIS_ENABLED', false),
        'funnel_analysis_enabled' => env('FEATURE_FUNNEL_ANALYSIS_ENABLED', false),
        'real_time_data_enabled' => env('FEATURE_REAL_TIME_DATA_ENABLED', false),
        'custom_dimensions_enabled' => env('FEATURE_CUSTOM_DIMENSIONS_ENABLED', false)
    ],

    /*
    |--------------------------------------------------------------------------
    | Extension Configuration
    |--------------------------------------------------------------------------
    */

    'extension' => [
        'current_version' => env('EXTENSION_CURRENT_VERSION', '0.1.0'),
        'min_supported_version' => env('EXTENSION_MIN_SUPPORTED_VERSION', '0.1.0'),
        'update_check_interval' => env('EXTENSION_UPDATE_CHECK_INTERVAL', 86400), // 24 hours
        'chrome_web_store_url' => 'https://chrome.google.com/webstore/detail/ga4-insights',
        'supported_browsers' => ['chrome', 'edge', 'brave', 'opera']
    ],

    /*
    |--------------------------------------------------------------------------
    | CORS Configuration
    |--------------------------------------------------------------------------
    */

    'cors' => [
        'allowed_origins' => explode(',', env('CORS_ALLOWED_ORIGINS', 'chrome-extension://*')),
        'allowed_methods' => explode(',', env('CORS_ALLOWED_METHODS', 'GET,POST,PUT,DELETE,OPTIONS')),
        'allowed_headers' => explode(',', env('CORS_ALLOWED_HEADERS', 'Content-Type,Authorization,X-Requested-With,X-Request-ID,X-Extension-Version')),
        'exposed_headers' => ['X-Request-ID', 'X-Rate-Limit-Remaining', 'X-Rate-Limit-Reset'],
        'max_age' => 86400, // 24 hours
        'supports_credentials' => false
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Configuration
    |--------------------------------------------------------------------------
    */

    'backup' => [
        'disk' => env('BACKUP_DISK', 's3'),
        'notification_mail' => env('BACKUP_NOTIFICATION_MAIL'),
        'schedule' => env('BACKUP_SCHEDULE', 'daily'),
        'retention_days' => env('BACKUP_RETENTION_DAYS', 30)
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Configuration
    |--------------------------------------------------------------------------
    */

    'maintenance' => [
        'enabled' => env('MAINTENANCE_MODE', false),
        'message' => env('MAINTENANCE_MESSAGE', 'API en mantenimiento. Volveremos pronto.'),
        'allowed_ips' => explode(',', env('MAINTENANCE_ALLOWED_IPS', '')),
        'retry_after' => env('MAINTENANCE_RETRY_AFTER', 3600) // 1 hour
    ]

];
