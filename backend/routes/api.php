<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\GA4Controller;
use App\Http\Controllers\Api\V1\UsageController;
use App\Http\Controllers\Api\V1\SubscriptionController;
use App\Http\Controllers\Api\V1\LogController;
use App\Http\Middleware\EnsureExtensionVersion;
use App\Http\Middleware\CheckUsageLimits;
use App\Http\Middleware\LogApiUsage;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => config('app.version', '1.0.0'),
        'environment' => app()->environment()
    ]);
});

// API Version 1
Route::prefix('v1')->group(function () {
    
    // Public routes (no authentication required)
    Route::prefix('auth')->group(function () {
        Route::post('/verify-token', [AuthController::class, 'verifyGoogleToken']);
        Route::post('/refresh', [AuthController::class, 'refreshToken']);
        Route::get('/google/callback', [AuthController::class, 'handleGoogleCallback']);
    });
    
    // Extension version check
    Route::get('/version/check', function (Request $request) {
        $currentVersion = $request->header('X-Extension-Version', '0.0.0');
        $minVersion = config('app.extension_min_version', '0.1.0');
        $latestVersion = config('app.extension_current_version', '0.1.0');
        
        return response()->json([
            'success' => true,
            'data' => [
                'current_version' => $currentVersion,
                'latest_version' => $latestVersion,
                'min_supported_version' => $minVersion,
                'update_required' => version_compare($currentVersion, $minVersion, '<'),
                'update_available' => version_compare($currentVersion, $latestVersion, '<'),
                'download_url' => 'https://chrome.google.com/webstore/detail/ga4-insights'
            ]
        ]);
    });
    
    // Protected routes (require authentication)
    Route::middleware(['auth:sanctum', EnsureExtensionVersion::class, LogApiUsage::class])->group(function () {
        
        // Authentication management
        Route::prefix('auth')->group(function () {
            Route::post('/logout', [AuthController::class, 'logout']);
            Route::get('/me', [AuthController::class, 'me']);
        });
        
        // User management
        Route::prefix('users')->group(function () {
            Route::get('/profile', [UserController::class, 'profile']);
            Route::put('/profile', [UserController::class, 'updateProfile']);
            Route::get('/preferences', [UserController::class, 'getPreferences']);
            Route::put('/preferences', [UserController::class, 'updatePreferences']);
            Route::delete('/account', [UserController::class, 'deleteAccount']);
        });
        
        // Usage tracking and limits
        Route::prefix('usage')->group(function () {
            Route::get('/status', [UsageController::class, 'getUsageStatus']);
            Route::post('/confirm', [UsageController::class, 'confirmUsage']);
            Route::get('/history', [UsageController::class, 'getUsageHistory']);
            Route::get('/stats', [UsageController::class, 'getUsageStats']);
        });
        
        // GA4 Data endpoints (with usage limits)
        Route::prefix('ga4')->middleware([CheckUsageLimits::class])->group(function () {
            
            // Properties management
            Route::get('/properties', [GA4Controller::class, 'getProperties']);
            Route::get('/properties/{propertyId}', [GA4Controller::class, 'getProperty']);
            Route::post('/properties/{propertyId}/select', [GA4Controller::class, 'selectProperty']);
            
            // Data queries
            Route::post('/reports/run', [GA4Controller::class, 'runReport']);
            Route::post('/reports/batch', [GA4Controller::class, 'runBatchReports']);
            
            // Specific data endpoints
            Route::prefix('data')->group(function () {
                Route::post('/page-analytics', [GA4Controller::class, 'getPageAnalytics']);
                Route::post('/site-overview', [GA4Controller::class, 'getSiteOverview']);
                Route::post('/traffic-sources', [GA4Controller::class, 'getTrafficSources']);
                Route::post('/device-breakdown', [GA4Controller::class, 'getDeviceBreakdown']);
                Route::post('/top-pages', [GA4Controller::class, 'getTopPages']);
                Route::post('/events', [GA4Controller::class, 'getEvents']);
                Route::post('/conversions', [GA4Controller::class, 'getConversions']);
                Route::post('/audience', [GA4Controller::class, 'getAudience']);
            });
            
            // Premium features
            Route::middleware(['check.premium'])->group(function () {
                Route::post('/data/comparison', [GA4Controller::class, 'getComparison']);
                Route::post('/data/cohorts', [GA4Controller::class, 'getCohorts']);
                Route::post('/data/funnels', [GA4Controller::class, 'getFunnels']);
                Route::post('/export', [GA4Controller::class, 'exportData']);
            });
        });
        
        // Subscription management
        Route::prefix('subscriptions')->group(function () {
            Route::get('/current', [SubscriptionController::class, 'getCurrentSubscription']);
            Route::post('/create', [SubscriptionController::class, 'createSubscription']);
            Route::post('/cancel', [SubscriptionController::class, 'cancelSubscription']);
            Route::post('/resume', [SubscriptionController::class, 'resumeSubscription']);
            Route::get('/invoices', [SubscriptionController::class, 'getInvoices']);
            Route::post('/update-payment-method', [SubscriptionController::class, 'updatePaymentMethod']);
        });
        
        // Logging endpoints
        Route::prefix('logs')->group(function () {
            Route::post('/error', [LogController::class, 'logError']);
            Route::post('/event', [LogController::class, 'logEvent']);
            Route::get('/debug', [LogController::class, 'getDebugLogs'])->middleware(['check.debug']);
        });
        
        // Cache management
        Route::prefix('cache')->group(function () {
            Route::delete('/clear', function (Request $request) {
                $user = $request->user();
                
                // Clear user-specific cache
                Cache::tags(['user:' . $user->id])->flush();
                
                return response()->json([
                    'success' => true,
                    'message' => 'Cache cleared successfully'
                ]);
            });
            
            Route::get('/stats', function (Request $request) {
                $user = $request->user();
                
                // Get cache statistics (implementation depends on cache driver)
                return response()->json([
                    'success' => true,
                    'data' => [
                        'cache_driver' => config('cache.default'),
                        'user_cache_size' => 0, // Placeholder
                        'last_cleared' => null  // Placeholder
                    ]
                ]);
            });
        });
    });
    
    // Webhook endpoints (no authentication, but verified by signature)
    Route::prefix('webhooks')->group(function () {
        Route::post('/stripe', [SubscriptionController::class, 'handleStripeWebhook']);
        Route::post('/google', [GA4Controller::class, 'handleGoogleWebhook']);
    });
    
    // Admin endpoints (require admin authentication)
    Route::prefix('admin')->middleware(['auth:sanctum', 'check.admin'])->group(function () {
        Route::get('/users', [UserController::class, 'getAllUsers']);
        Route::get('/users/{user}/usage', [UsageController::class, 'getUserUsage']);
        Route::post('/users/{user}/reset-usage', [UsageController::class, 'resetUserUsage']);
        Route::get('/stats/overview', [UsageController::class, 'getSystemStats']);
        Route::get('/logs/system', [LogController::class, 'getSystemLogs']);
        Route::post('/maintenance/enable', function () {
            Artisan::call('down');
            return response()->json(['success' => true, 'message' => 'Maintenance mode enabled']);
        });
        Route::post('/maintenance/disable', function () {
            Artisan::call('up');
            return response()->json(['success' => true, 'message' => 'Maintenance mode disabled']);
        });
    });
});

// Fallback route for API
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'error' => [
            'code' => 'ENDPOINT_NOT_FOUND',
            'message' => 'API endpoint not found',
            'details' => 'The requested API endpoint does not exist'
        ]
    ], 404);
});

// Rate limiting test endpoint
Route::middleware(['throttle:60,1'])->get('/test-rate-limit', function () {
    return response()->json([
        'success' => true,
        'message' => 'Rate limit test successful',
        'timestamp' => now()->toISOString()
    ]);
});
