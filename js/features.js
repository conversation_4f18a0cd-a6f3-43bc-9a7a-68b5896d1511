/**
 * features.js - GA4 Data Processing and Feature Implementation
 * 
 * Handles specific GA4 data processing, metric calculations,
 * and feature implementations for the extension.
 */

class FeaturesManager {
    constructor() {
        this.queryBuilder = new GA4QueryBuilder();
        Logger.info('FeaturesManager initialized');
    }
    
    /**
     * Get page analytics data
     * @param {string} pagePath - Current page path
     * @param {string} dimension - Selected dimension
     * @param {string} dateRange - Date range key
     * @returns {Object} Processed page analytics data
     */
    async getPageAnalytics(pagePath, dimension, dateRange) {
        try {
            Logger.info('Getting page analytics', { pagePath, dimension, dateRange });
            
            const propertyId = State.getSelectedPropertyId();
            if (!propertyId) {
                throw new Error('No property selected');
            }
            
            const dateConfig = CONFIG.DATE_RANGES.PAGE_VIEW[dateRange];
            if (!dateConfig) {
                throw new Error('Invalid date range');
            }
            
            // Build query for page metrics
            const query = this.queryBuilder
                .setProperty(propertyId)
                .setDateRange(dateConfig.start, dateConfig.end)
                .addMetrics(CONFIG.METRICS.PAGE_ANALYSIS)
                .addDimensions(['date'])
                .addPageFilter(dimension, pagePath)
                .setOrderBy('date', false, false)
                .setLimit(dateConfig.days)
                .build();
            
            // Execute query
            const reportData = await API.runGA4Report(query);
            
            // Process the data
            const processedData = this.processPageMetrics(reportData, dateRange);
            
            Logger.info('Page analytics completed', { 
                rowCount: reportData.rowCount,
                metricsCount: processedData.metrics.length
            });
            
            return processedData;
            
        } catch (error) {
            Logger.error('Failed to get page analytics', { pagePath, dimension, dateRange }, error);
            throw error;
        }
    }
    
    /**
     * Get page traffic sources
     * @param {string} pagePath - Current page path
     * @param {string} dimension - Selected dimension
     * @param {string} dateRange - Date range key
     * @returns {Object} Traffic sources data
     */
    async getPageTrafficSources(pagePath, dimension, dateRange) {
        try {
            Logger.debug('Getting page traffic sources', { pagePath, dimension });
            
            const propertyId = State.getSelectedPropertyId();
            const dateConfig = CONFIG.DATE_RANGES.PAGE_VIEW[dateRange];
            
            const query = this.queryBuilder
                .setProperty(propertyId)
                .setDateRange(dateConfig.start, dateConfig.end)
                .addMetrics(CONFIG.METRICS.TRAFFIC_SOURCES)
                .addDimensions(CONFIG.DIMENSIONS.TRAFFIC_SOURCES)
                .addPageFilter(dimension, pagePath)
                .setOrderBy('activeUsers', true, true)
                .setLimit(10)
                .build();
            
            const reportData = await API.runGA4Report(query);
            const processedData = this.processTrafficSources(reportData);
            
            Logger.debug('Traffic sources completed', { 
                sourcesCount: processedData.sources.length 
            });
            
            return processedData;
            
        } catch (error) {
            Logger.error('Failed to get traffic sources', { pagePath }, error);
            throw error;
        }
    }
    
    /**
     * Get page device data
     * @param {string} pagePath - Current page path
     * @param {string} dimension - Selected dimension
     * @param {string} dateRange - Date range key
     * @returns {Object} Device breakdown data
     */
    async getPageDeviceData(pagePath, dimension, dateRange) {
        try {
            Logger.debug('Getting page device data', { pagePath, dimension });
            
            const propertyId = State.getSelectedPropertyId();
            const dateConfig = CONFIG.DATE_RANGES.PAGE_VIEW[dateRange];
            
            const query = this.queryBuilder
                .setProperty(propertyId)
                .setDateRange(dateConfig.start, dateConfig.end)
                .addMetrics(CONFIG.METRICS.DEVICES)
                .addDimensions(CONFIG.DIMENSIONS.DEVICES)
                .addPageFilter(dimension, pagePath)
                .setOrderBy('activeUsers', true, true)
                .build();
            
            const reportData = await API.runGA4Report(query);
            const processedData = this.processDeviceData(reportData);
            
            Logger.debug('Device data completed', { 
                deviceCount: processedData.devices.length 
            });
            
            return processedData;
            
        } catch (error) {
            Logger.error('Failed to get device data', { pagePath }, error);
            throw error;
        }
    }
    
    /**
     * Get site summary data
     * @param {string} dateRange - Date range key
     * @returns {Object} Site summary data
     */
    async getSiteSummary(dateRange) {
        try {
            Logger.info('Getting site summary', { dateRange });
            
            const propertyId = State.getSelectedPropertyId();
            const dateConfig = CONFIG.DATE_RANGES.SITE_VIEW[dateRange];
            
            const query = this.queryBuilder
                .setProperty(propertyId)
                .setDateRange(dateConfig.start, dateConfig.end)
                .addMetrics(CONFIG.METRICS.SITE_OVERVIEW)
                .addDimensions(['date'])
                .setOrderBy('date', false, false)
                .setLimit(dateConfig.days)
                .build();
            
            const reportData = await API.runGA4Report(query);
            const processedData = this.processSiteMetrics(reportData, dateRange);
            
            Logger.info('Site summary completed', { 
                totalUsers: processedData.totals.activeUsers 
            });
            
            return processedData;
            
        } catch (error) {
            Logger.error('Failed to get site summary', { dateRange }, error);
            throw error;
        }
    }
    
    /**
     * Get top pages data
     * @param {string} dateRange - Date range key
     * @returns {Object} Top pages data
     */
    async getTopPages(dateRange) {
        try {
            Logger.debug('Getting top pages', { dateRange });
            
            const propertyId = State.getSelectedPropertyId();
            const dateConfig = CONFIG.DATE_RANGES.SITE_VIEW[dateRange];
            
            const query = this.queryBuilder
                .setProperty(propertyId)
                .setDateRange(dateConfig.start, dateConfig.end)
                .addMetrics(['screenPageViews', 'activeUsers', 'engagementRate'])
                .addDimensions(CONFIG.DIMENSIONS.TOP_PAGES)
                .setOrderBy('screenPageViews', true, true)
                .setLimit(10)
                .build();
            
            const reportData = await API.runGA4Report(query);
            const processedData = this.processTopPages(reportData);
            
            Logger.debug('Top pages completed', { 
                pagesCount: processedData.pages.length 
            });
            
            return processedData;
            
        } catch (error) {
            Logger.error('Failed to get top pages', { dateRange }, error);
            throw error;
        }
    }
    
    /**
     * Process page metrics data
     * @private
     */
    processPageMetrics(reportData, dateRange) {
        const metrics = [];
        const trends = [];
        const totals = {};
        
        // Extract metric headers
        const metricHeaders = reportData.metricHeaders.map(h => h.name);
        
        // Process totals
        if (reportData.totals && reportData.totals.length > 0) {
            const totalRow = reportData.totals[0];
            metricHeaders.forEach((metricName, index) => {
                const value = parseFloat(totalRow.metricValues[index].value) || 0;
                totals[metricName] = value;
                
                // Create metric card data
                metrics.push({
                    name: metricName,
                    displayName: I18n.getMetricName(metricName),
                    value: value,
                    formattedValue: Utils.formatMetricValue(value, Utils.getMetricType(metricName)),
                    icon: Utils.getMetricIcon(metricName),
                    change: this.calculateChange(value, dateRange), // Placeholder
                    type: Utils.getMetricType(metricName)
                });
            });
        }
        
        // Process daily trends
        if (reportData.rows) {
            reportData.rows.forEach(row => {
                const date = row.dimensionValues[0];
                const dayData = { date };
                
                metricHeaders.forEach((metricName, index) => {
                    dayData[metricName] = parseFloat(row.metricValues[index].value) || 0;
                });
                
                trends.push(dayData);
            });
        }
        
        return {
            metrics,
            trends,
            totals,
            dateRange,
            lastUpdated: Date.now()
        };
    }
    
    /**
     * Process traffic sources data
     * @private
     */
    processTrafficSources(reportData) {
        const sources = [];
        let totalUsers = 0;
        
        if (reportData.rows) {
            // Calculate total users first
            reportData.rows.forEach(row => {
                totalUsers += parseFloat(row.metricValues[0].value) || 0;
            });
            
            // Process each source
            reportData.rows.forEach(row => {
                const sourceMedium = row.dimensionValues[0];
                const users = parseFloat(row.metricValues[0].value) || 0;
                const sessions = parseFloat(row.metricValues[1].value) || 0;
                const engagementRate = parseFloat(row.metricValues[2].value) || 0;
                
                const [source, medium] = sourceMedium.split(' / ');
                
                sources.push({
                    sourceMedium,
                    source: source || '(unknown)',
                    medium: medium || '(unknown)',
                    users,
                    sessions,
                    engagementRate,
                    percentage: Utils.calculatePercentage(users, totalUsers),
                    icon: this.getSourceIcon(source, medium)
                });
            });
        }
        
        return {
            sources,
            totalUsers,
            lastUpdated: Date.now()
        };
    }
    
    /**
     * Process device data
     * @private
     */
    processDeviceData(reportData) {
        const devices = [];
        let totalUsers = 0;
        
        if (reportData.rows) {
            // Calculate total users
            reportData.rows.forEach(row => {
                totalUsers += parseFloat(row.metricValues[0].value) || 0;
            });
            
            // Process each device
            reportData.rows.forEach(row => {
                const deviceCategory = row.dimensionValues[0];
                const users = parseFloat(row.metricValues[0].value) || 0;
                const sessions = parseFloat(row.metricValues[1].value) || 0;
                const engagementRate = parseFloat(row.metricValues[2].value) || 0;
                
                devices.push({
                    category: deviceCategory,
                    displayName: this.getDeviceDisplayName(deviceCategory),
                    users,
                    sessions,
                    engagementRate,
                    percentage: Utils.calculatePercentage(users, totalUsers),
                    icon: this.getDeviceIcon(deviceCategory)
                });
            });
        }
        
        return {
            devices,
            totalUsers,
            lastUpdated: Date.now()
        };
    }
    
    /**
     * Process site metrics data
     * @private
     */
    processSiteMetrics(reportData, dateRange) {
        const metrics = [];
        const trends = [];
        const totals = {};
        
        const metricHeaders = reportData.metricHeaders.map(h => h.name);
        
        // Process totals
        if (reportData.totals && reportData.totals.length > 0) {
            const totalRow = reportData.totals[0];
            metricHeaders.forEach((metricName, index) => {
                const value = parseFloat(totalRow.metricValues[index].value) || 0;
                totals[metricName] = value;
                
                metrics.push({
                    name: metricName,
                    displayName: I18n.getMetricName(metricName),
                    value: value,
                    formattedValue: Utils.formatMetricValue(value, Utils.getMetricType(metricName)),
                    icon: Utils.getMetricIcon(metricName),
                    change: this.calculateChange(value, dateRange),
                    type: Utils.getMetricType(metricName)
                });
            });
        }
        
        // Process trends
        if (reportData.rows) {
            reportData.rows.forEach(row => {
                const date = row.dimensionValues[0];
                const dayData = { date };
                
                metricHeaders.forEach((metricName, index) => {
                    dayData[metricName] = parseFloat(row.metricValues[index].value) || 0;
                });
                
                trends.push(dayData);
            });
        }
        
        return {
            metrics,
            trends,
            totals,
            dateRange,
            lastUpdated: Date.now()
        };
    }
    
    /**
     * Process top pages data
     * @private
     */
    processTopPages(reportData) {
        const pages = [];
        
        if (reportData.rows) {
            reportData.rows.forEach(row => {
                const pagePath = row.dimensionValues[0];
                const pageTitle = row.dimensionValues[1] || pagePath;
                const pageViews = parseFloat(row.metricValues[0].value) || 0;
                const users = parseFloat(row.metricValues[1].value) || 0;
                const engagementRate = parseFloat(row.metricValues[2].value) || 0;
                
                pages.push({
                    pagePath,
                    pageTitle: Utils.truncateText(pageTitle, 50),
                    pageViews,
                    users,
                    engagementRate,
                    formattedPageViews: Utils.formatMetricValue(pageViews, 'number'),
                    formattedEngagementRate: Utils.formatMetricValue(engagementRate, 'percentage')
                });
            });
        }
        
        return {
            pages,
            lastUpdated: Date.now()
        };
    }
    
    /**
     * Calculate change percentage (placeholder - would need comparison data)
     * @private
     */
    calculateChange(currentValue, dateRange) {
        // Placeholder implementation
        // In a real implementation, this would compare with previous period
        const randomChange = (Math.random() - 0.5) * 20; // -10% to +10%
        return Utils.formatChange(randomChange);
    }
    
    /**
     * Get icon for traffic source
     * @private
     */
    getSourceIcon(source, medium) {
        const sourceIcons = {
            'google': '🔍',
            'facebook': '📘',
            'twitter': '🐦',
            'linkedin': '💼',
            'instagram': '📷',
            'youtube': '📺',
            'email': '📧',
            '(direct)': '🔗',
            '(none)': '🔗'
        };
        
        const mediumIcons = {
            'organic': '🔍',
            'cpc': '💰',
            'social': '📱',
            'email': '📧',
            'referral': '🔗'
        };
        
        return sourceIcons[source] || mediumIcons[medium] || '🌐';
    }
    
    /**
     * Get device display name
     * @private
     */
    getDeviceDisplayName(deviceCategory) {
        const deviceNames = {
            'mobile': I18n.t('mobile'),
            'desktop': I18n.t('desktop'),
            'tablet': I18n.t('tablet')
        };
        
        return deviceNames[deviceCategory] || deviceCategory;
    }
    
    /**
     * Get device icon
     * @private
     */
    getDeviceIcon(deviceCategory) {
        const deviceIcons = {
            'mobile': '📱',
            'desktop': '💻',
            'tablet': '📟'
        };
        
        return deviceIcons[deviceCategory] || '📱';
    }
}

/**
 * GA4 Query Builder Class
 */
class GA4QueryBuilder {
    constructor() {
        this.reset();
    }
    
    reset() {
        this.query = {
            property: '',
            dateRanges: [],
            metrics: [],
            dimensions: [],
            limit: 10
        };
        return this;
    }
    
    setProperty(propertyId) {
        this.query.property = propertyId;
        return this;
    }
    
    setDateRange(startDate, endDate, name = null) {
        const dateRange = { startDate, endDate };
        if (name) dateRange.name = name;
        this.query.dateRanges = [dateRange];
        return this;
    }
    
    addMetrics(metrics) {
        const metricObjects = metrics.map(name => ({ name }));
        this.query.metrics = metricObjects;
        return this;
    }
    
    addDimensions(dimensions) {
        const dimensionObjects = dimensions.map(name => ({ name }));
        this.query.dimensions = dimensionObjects;
        return this;
    }
    
    addPageFilter(dimension, pageValue) {
        this.query.dimensionFilter = {
            filter: {
                fieldName: dimension,
                stringFilter: {
                    matchType: "EXACT",
                    value: pageValue
                }
            }
        };
        return this;
    }
    
    setLimit(limit) {
        this.query.limit = limit;
        return this;
    }
    
    setOrderBy(field, isMetric = false, desc = true) {
        const orderBy = {};
        if (isMetric) {
            orderBy.metric = { metricName: field };
        } else {
            orderBy.dimension = { dimensionName: field };
        }
        orderBy.desc = desc;
        this.query.orderBys = [orderBy];
        return this;
    }
    
    build() {
        const builtQuery = { ...this.query };
        this.reset();
        return builtQuery;
    }
}

// Create global Features instance
const Features = new FeaturesManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Features;
}
