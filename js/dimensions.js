/**
 * dimensions.js - GA4 Dimensions Configuration and Management
 * 
 * Handles GA4 dimension configurations, validation, and utilities
 * for the extension's dimension selection feature.
 */

class DimensionsManager {
    constructor() {
        this.availableDimensions = CONFIG.PAGE_DIMENSIONS;
        this.currentDimension = CONFIG.DEFAULTS.PAGE_DIMENSION;
        
        Logger.info('DimensionsManager initialized');
    }
    
    /**
     * Get all available dimensions
     * @returns {Object} Available dimensions configuration
     */
    getAvailableDimensions() {
        return { ...this.availableDimensions };
    }
    
    /**
     * Get dimension configuration by name
     * @param {string} dimensionName - Name of the dimension
     * @returns {Object|null} Dimension configuration
     */
    getDimensionConfig(dimensionName) {
        return this.availableDimensions[dimensionName] || null;
    }
    
    /**
     * Validate dimension name
     * @param {string} dimensionName - Name to validate
     * @returns {boolean} Whether dimension is valid
     */
    isValidDimension(dimensionName) {
        return this.availableDimensions.hasOwnProperty(dimensionName);
    }
    
    /**
     * Get dimension display name
     * @param {string} dimensionName - Dimension name
     * @returns {string} Display name
     */
    getDimensionDisplayName(dimensionName) {
        const config = this.getDimensionConfig(dimensionName);
        return config ? config.name : dimensionName;
    }
    
    /**
     * Get dimension description
     * @param {string} dimensionName - Dimension name
     * @returns {string} Description
     */
    getDimensionDescription(dimensionName) {
        const config = this.getDimensionConfig(dimensionName);
        return config ? config.description : '';
    }
    
    /**
     * Get dimension help text
     * @param {string} dimensionName - Dimension name
     * @returns {string} Help text
     */
    getDimensionHelpText(dimensionName) {
        const config = this.getDimensionConfig(dimensionName);
        return config ? config.helpText : '';
    }
    
    /**
     * Get dimension API name for GA4 queries
     * @param {string} dimensionName - Dimension name
     * @returns {string} API name
     */
    getDimensionApiName(dimensionName) {
        const config = this.getDimensionConfig(dimensionName);
        return config ? config.apiName : dimensionName;
    }
    
    /**
     * Get filter type for dimension
     * @param {string} dimensionName - Dimension name
     * @returns {string} Filter type
     */
    getDimensionFilterType(dimensionName) {
        const config = this.getDimensionConfig(dimensionName);
        return config ? config.filterType : 'exact_match';
    }
    
    /**
     * Set current dimension
     * @param {string} dimensionName - Dimension to set as current
     * @returns {boolean} Whether dimension was set successfully
     */
    setCurrentDimension(dimensionName) {
        if (this.isValidDimension(dimensionName)) {
            this.currentDimension = dimensionName;
            Logger.debug('Current dimension set', { dimension: dimensionName });
            return true;
        }
        
        Logger.warn('Invalid dimension name', { dimension: dimensionName });
        return false;
    }
    
    /**
     * Get current dimension
     * @returns {string} Current dimension name
     */
    getCurrentDimension() {
        return this.currentDimension;
    }
    
    /**
     * Get current dimension configuration
     * @returns {Object} Current dimension configuration
     */
    getCurrentDimensionConfig() {
        return this.getDimensionConfig(this.currentDimension);
    }
    
    /**
     * Create dimension selector options for HTML select
     * @returns {Array} Array of option objects
     */
    createSelectorOptions() {
        return Object.keys(this.availableDimensions).map(dimensionName => {
            const config = this.availableDimensions[dimensionName];
            return {
                value: dimensionName,
                text: config.name,
                description: config.description,
                selected: dimensionName === this.currentDimension
            };
        });
    }
    
    /**
     * Populate HTML select element with dimension options
     * @param {string|Element} selector - CSS selector or DOM element
     */
    populateSelector(selector) {
        try {
            const selectElement = typeof selector === 'string' 
                ? document.querySelector(selector)
                : selector;
                
            if (!selectElement) {
                Logger.warn('Dimension selector element not found', { selector });
                return;
            }
            
            // Clear existing options
            selectElement.innerHTML = '';
            
            // Add dimension options
            const options = this.createSelectorOptions();
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                optionElement.title = option.description;
                optionElement.selected = option.selected;
                
                selectElement.appendChild(optionElement);
            });
            
            Logger.debug('Dimension selector populated', { 
                optionsCount: options.length,
                current: this.currentDimension 
            });
            
        } catch (error) {
            Logger.error('Failed to populate dimension selector', { selector }, error);
        }
    }
    
    /**
     * Handle dimension change from UI
     * @param {string} newDimension - New dimension selected
     * @param {Function} callback - Optional callback function
     */
    handleDimensionChange(newDimension, callback = null) {
        try {
            Logger.info('Handling dimension change', { 
                from: this.currentDimension, 
                to: newDimension 
            });
            
            if (!this.isValidDimension(newDimension)) {
                throw new Error(`Invalid dimension: ${newDimension}`);
            }
            
            const oldDimension = this.currentDimension;
            this.setCurrentDimension(newDimension);
            
            // Update help text in UI
            this.updateHelpText();
            
            // Save to state
            if (typeof State !== 'undefined') {
                State.setSelectedDimension(newDimension);
            }
            
            // Execute callback if provided
            if (callback && typeof callback === 'function') {
                callback(newDimension, oldDimension);
            }
            
            Logger.info('Dimension change completed', { 
                dimension: newDimension 
            });
            
        } catch (error) {
            Logger.error('Failed to handle dimension change', { 
                newDimension 
            }, error);
            
            // Revert to previous dimension
            this.updateSelectorValue();
        }
    }
    
    /**
     * Update help text in UI
     * @private
     */
    updateHelpText() {
        try {
            const helpElement = document.getElementById('dimension-help');
            if (helpElement) {
                const helpText = this.getDimensionHelpText(this.currentDimension);
                helpElement.textContent = helpText;
            }
        } catch (error) {
            Logger.error('Failed to update help text', {}, error);
        }
    }
    
    /**
     * Update selector value to match current dimension
     * @private
     */
    updateSelectorValue() {
        try {
            const selector = document.getElementById('dimension-selector');
            if (selector) {
                selector.value = this.currentDimension;
            }
        } catch (error) {
            Logger.error('Failed to update selector value', {}, error);
        }
    }
    
    /**
     * Get dimension examples for documentation
     * @param {string} dimensionName - Dimension name
     * @returns {Array} Array of example values
     */
    getDimensionExamples(dimensionName) {
        const examples = {
            pagePath: [
                '/productos/categoria-a',
                '/blog/articulo-ejemplo',
                '/contacto',
                '/sobre-nosotros'
            ],
            landingPage: [
                '/productos/categoria-a',
                '/blog/articulo-ejemplo',
                '/',
                '/ofertas-especiales'
            ],
            exitPage: [
                '/contacto',
                '/checkout/confirmacion',
                '/blog/articulo-ejemplo',
                '/productos/categoria-a'
            ],
            pagePathPlusQueryString: [
                '/productos/categoria-a?utm_source=google',
                '/blog/articulo-ejemplo?ref=social',
                '/?utm_campaign=homepage',
                '/ofertas?promo=descuento20'
            ]
        };
        
        return examples[dimensionName] || [];
    }
    
    /**
     * Validate page value for dimension
     * @param {string} dimensionName - Dimension name
     * @param {string} pageValue - Page value to validate
     * @returns {Object} Validation result
     */
    validatePageValue(dimensionName, pageValue) {
        const config = this.getDimensionConfig(dimensionName);
        
        if (!config) {
            return {
                valid: false,
                error: 'Invalid dimension'
            };
        }
        
        if (!pageValue || typeof pageValue !== 'string') {
            return {
                valid: false,
                error: 'Page value is required'
            };
        }
        
        // Basic validation based on dimension type
        switch (dimensionName) {
            case 'pagePath':
            case 'landingPage':
            case 'exitPage':
                if (!pageValue.startsWith('/')) {
                    return {
                        valid: false,
                        error: 'Page path must start with /'
                    };
                }
                break;
                
            case 'pagePathPlusQueryString':
                if (!pageValue.startsWith('/')) {
                    return {
                        valid: false,
                        error: 'Page path must start with /'
                    };
                }
                // Allow query parameters
                break;
        }
        
        return {
            valid: true,
            normalized: pageValue.trim()
        };
    }
    
    /**
     * Get dimension usage statistics (placeholder for future implementation)
     * @param {string} dimensionName - Dimension name
     * @returns {Object} Usage statistics
     */
    getDimensionUsageStats(dimensionName) {
        // Placeholder implementation
        return {
            dimension: dimensionName,
            totalQueries: 0,
            lastUsed: null,
            popularValues: []
        };
    }
    
    /**
     * Export dimension configuration for debugging
     * @returns {Object} Complete dimension configuration
     */
    exportConfiguration() {
        return {
            availableDimensions: this.getAvailableDimensions(),
            currentDimension: this.getCurrentDimension(),
            currentConfig: this.getCurrentDimensionConfig(),
            examples: Object.keys(this.availableDimensions).reduce((acc, dim) => {
                acc[dim] = this.getDimensionExamples(dim);
                return acc;
            }, {})
        };
    }
}

// Create global Dimensions instance
const Dimensions = new DimensionsManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Dimensions;
}
