/**
 * cache.js - Cache Management for GA4 Insights Extension
 * 
 * Manages local caching of GA4 API responses and other data
 * to optimize performance and reduce API calls.
 */

class CacheManager {
    constructor() {
        this.prefix = CONFIG.CACHE.PREFIX;
        this.maxCacheSize = CONFIG.CACHE.MAX_SIZE;
        this.ttlConfig = CONFIG.CACHE.TTL;
        
        Logger.info('CacheManager initialized', {
            prefix: this.prefix,
            maxSize: this.maxCacheSize
        });
    }
    
    /**
     * Generate cache key for a query
     * @param {Object} query - Query object
     * @param {string} propertyId - GA4 property ID
     * @param {string} type - Cache type (e.g., 'page_metrics', 'traffic_sources')
     * @returns {string} Cache key
     */
    generateCacheKey(query, propertyId, type = 'general') {
        try {
            const queryString = JSON.stringify(query, Object.keys(query).sort());
            const hash = this.simpleHash(queryString + propertyId + type);
            const key = `${this.prefix}${type}_${hash}`;
            
            Logger.debug('Cache key generated', { 
                type,
                propertyId,
                keyPreview: key.substring(0, 30) + '...'
            });
            
            return key;
            
        } catch (error) {
            Logger.error('Failed to generate cache key', { query, propertyId, type }, error);
            return `${this.prefix}${type}_${Date.now()}`;
        }
    }
    
    /**
     * Store data in cache
     * @param {string} key - Cache key
     * @param {*} data - Data to cache
     * @param {number} ttlMs - Time to live in milliseconds
     * @param {Object} metadata - Additional metadata
     */
    async set(key, data, ttlMs = this.ttlConfig.RECENT_DATA, metadata = {}) {
        try {
            const cacheEntry = {
                data,
                metadata: {
                    cachedAt: Date.now(),
                    expiresAt: Date.now() + ttlMs,
                    size: this.calculateSize(data),
                    type: metadata.type || 'unknown',
                    propertyId: metadata.propertyId,
                    queryHash: metadata.queryHash,
                    requestId: metadata.requestId || window.currentRequestId,
                    ...metadata
                }
            };
            
            await chrome.storage.local.set({ [key]: cacheEntry });
            
            Logger.debug('Data cached successfully', { 
                key: key.substring(0, 30) + '...',
                size: cacheEntry.metadata.size,
                ttl: ttlMs,
                expiresAt: new Date(cacheEntry.metadata.expiresAt).toISOString()
            });
            
            // Check if cleanup is needed
            await this.cleanupIfNeeded();
            
        } catch (error) {
            Logger.error('Failed to cache data', { key, ttl: ttlMs }, error);
        }
    }
    
    /**
     * Get data from cache
     * @param {string} key - Cache key
     * @returns {*|null} Cached data or null if not found/expired
     */
    async get(key) {
        try {
            const result = await chrome.storage.local.get(key);
            const cacheEntry = result[key];
            
            if (!cacheEntry) {
                Logger.debug('Cache miss', { key: key.substring(0, 30) + '...' });
                return null;
            }
            
            // Check expiration
            if (Date.now() > cacheEntry.metadata.expiresAt) {
                Logger.debug('Cache expired, removing', { 
                    key: key.substring(0, 30) + '...',
                    expiredAt: new Date(cacheEntry.metadata.expiresAt).toISOString()
                });
                await chrome.storage.local.remove(key);
                return null;
            }
            
            Logger.debug('Cache hit', { 
                key: key.substring(0, 30) + '...',
                age: Date.now() - cacheEntry.metadata.cachedAt,
                type: cacheEntry.metadata.type
            });
            
            return cacheEntry.data;
            
        } catch (error) {
            Logger.error('Failed to get cached data', { key }, error);
            return null;
        }
    }
    
    /**
     * Check if data exists in cache and is valid
     * @param {string} key - Cache key
     * @returns {boolean} Whether valid cached data exists
     */
    async has(key) {
        try {
            const result = await chrome.storage.local.get(key);
            const cacheEntry = result[key];
            
            if (!cacheEntry) {
                return false;
            }
            
            return Date.now() <= cacheEntry.metadata.expiresAt;
            
        } catch (error) {
            Logger.error('Failed to check cache existence', { key }, error);
            return false;
        }
    }
    
    /**
     * Remove specific cache entry
     * @param {string} key - Cache key
     */
    async remove(key) {
        try {
            await chrome.storage.local.remove(key);
            Logger.debug('Cache entry removed', { key: key.substring(0, 30) + '...' });
        } catch (error) {
            Logger.error('Failed to remove cache entry', { key }, error);
        }
    }
    
    /**
     * Clean up expired cache entries
     */
    async cleanup() {
        try {
            Logger.info('Starting cache cleanup');
            
            const allData = await chrome.storage.local.get(null);
            const now = Date.now();
            const keysToRemove = [];
            let totalSize = 0;
            let expiredCount = 0;
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const entry = allData[key];
                    if (entry && entry.metadata) {
                        totalSize += entry.metadata.size || 0;
                        
                        if (now > entry.metadata.expiresAt) {
                            keysToRemove.push(key);
                            expiredCount++;
                        }
                    }
                }
            });
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                Logger.info('Cache cleanup completed', { 
                    removedKeys: keysToRemove.length,
                    expiredCount,
                    totalSizeBefore: totalSize
                });
            } else {
                Logger.debug('No expired cache entries found');
            }
            
        } catch (error) {
            Logger.error('Cache cleanup failed', {}, error);
        }
    }
    
    /**
     * Clean up cache if approaching size limits
     * @private
     */
    async cleanupIfNeeded() {
        try {
            const usage = await this.getCacheUsage();
            
            if (usage.totalSize > this.maxCacheSize * 0.8) { // 80% of limit
                Logger.warn('Cache size approaching limit, cleaning up', { 
                    currentSize: usage.totalSize,
                    limit: this.maxCacheSize,
                    percentage: Math.round((usage.totalSize / this.maxCacheSize) * 100)
                });
                
                await this.cleanup();
                
                // If still too large, remove oldest entries
                const newUsage = await this.getCacheUsage();
                if (newUsage.totalSize > this.maxCacheSize * 0.7) {
                    await this.removeOldestEntries(10);
                }
            }
            
        } catch (error) {
            Logger.error('Failed to check cache size', {}, error);
        }
    }
    
    /**
     * Remove oldest cache entries
     * @private
     */
    async removeOldestEntries(count = 10) {
        try {
            Logger.info('Removing oldest cache entries', { count });
            
            const allData = await chrome.storage.local.get(null);
            const cacheEntries = [];
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const entry = allData[key];
                    if (entry && entry.metadata) {
                        cacheEntries.push({
                            key,
                            cachedAt: entry.metadata.cachedAt
                        });
                    }
                }
            });
            
            // Sort by age (oldest first)
            cacheEntries.sort((a, b) => a.cachedAt - b.cachedAt);
            
            // Remove oldest entries
            const keysToRemove = cacheEntries.slice(0, count).map(entry => entry.key);
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                Logger.info('Oldest cache entries removed', { 
                    removedCount: keysToRemove.length 
                });
            }
            
        } catch (error) {
            Logger.error('Failed to remove oldest entries', {}, error);
        }
    }
    
    /**
     * Get cache usage statistics
     * @returns {Object} Cache usage stats
     */
    async getCacheUsage() {
        try {
            const allData = await chrome.storage.local.get(null);
            let totalSize = 0;
            let cacheEntries = 0;
            let expiredEntries = 0;
            const now = Date.now();
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const entry = allData[key];
                    if (entry && entry.metadata) {
                        totalSize += entry.metadata.size || 0;
                        cacheEntries++;
                        
                        if (now > entry.metadata.expiresAt) {
                            expiredEntries++;
                        }
                    }
                }
            });
            
            return { 
                totalSize, 
                cacheEntries, 
                expiredEntries,
                usagePercentage: Math.round((totalSize / this.maxCacheSize) * 100)
            };
            
        } catch (error) {
            Logger.error('Failed to get cache usage', {}, error);
            return { totalSize: 0, cacheEntries: 0, expiredEntries: 0, usagePercentage: 0 };
        }
    }
    
    /**
     * Invalidate cache by pattern
     * @param {RegExp|string} pattern - Pattern to match cache keys
     */
    async invalidateByPattern(pattern) {
        try {
            Logger.info('Invalidating cache by pattern', { pattern: pattern.toString() });
            
            const allData = await chrome.storage.local.get(null);
            const keysToRemove = [];
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    if (typeof pattern === 'string') {
                        if (key.includes(pattern)) {
                            keysToRemove.push(key);
                        }
                    } else if (pattern instanceof RegExp) {
                        if (pattern.test(key)) {
                            keysToRemove.push(key);
                        }
                    }
                }
            });
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                Logger.info('Cache invalidated by pattern', { 
                    pattern: pattern.toString(),
                    removedKeys: keysToRemove.length 
                });
            }
            
        } catch (error) {
            Logger.error('Failed to invalidate cache by pattern', { pattern }, error);
        }
    }
    
    /**
     * Invalidate cache for specific property
     * @param {string} propertyId - GA4 property ID
     */
    async invalidateForProperty(propertyId) {
        try {
            Logger.info('Invalidating cache for property', { propertyId });
            
            const allData = await chrome.storage.local.get(null);
            const keysToRemove = [];
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const entry = allData[key];
                    if (entry && entry.metadata && entry.metadata.propertyId === propertyId) {
                        keysToRemove.push(key);
                    }
                }
            });
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                Logger.info('Property cache invalidated', { 
                    propertyId,
                    removedKeys: keysToRemove.length 
                });
            }
            
        } catch (error) {
            Logger.error('Failed to invalidate property cache', { propertyId }, error);
        }
    }
    
    /**
     * Clear all cache
     */
    async clear() {
        try {
            Logger.info('Clearing all cache');
            
            const allData = await chrome.storage.local.get(null);
            const cacheKeys = Object.keys(allData).filter(key => key.startsWith(this.prefix));
            
            if (cacheKeys.length > 0) {
                await chrome.storage.local.remove(cacheKeys);
                Logger.info('All cache cleared', { removedKeys: cacheKeys.length });
            }
            
        } catch (error) {
            Logger.error('Failed to clear cache', {}, error);
        }
    }
    
    /**
     * Calculate approximate size of data
     * @private
     */
    calculateSize(data) {
        try {
            return JSON.stringify(data).length;
        } catch (error) {
            return 0;
        }
    }
    
    /**
     * Simple hash function for generating cache keys
     * @private
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return Math.abs(hash).toString(36);
    }
    
    /**
     * Get cache statistics for debugging
     * @returns {Object} Detailed cache statistics
     */
    async getStats() {
        try {
            const allData = await chrome.storage.local.get(null);
            const stats = {
                totalEntries: 0,
                expiredEntries: 0,
                totalSize: 0,
                typeBreakdown: {},
                oldestEntry: null,
                newestEntry: null
            };
            
            const now = Date.now();
            
            Object.keys(allData).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const entry = allData[key];
                    if (entry && entry.metadata) {
                        stats.totalEntries++;
                        stats.totalSize += entry.metadata.size || 0;
                        
                        if (now > entry.metadata.expiresAt) {
                            stats.expiredEntries++;
                        }
                        
                        const type = entry.metadata.type || 'unknown';
                        stats.typeBreakdown[type] = (stats.typeBreakdown[type] || 0) + 1;
                        
                        if (!stats.oldestEntry || entry.metadata.cachedAt < stats.oldestEntry) {
                            stats.oldestEntry = entry.metadata.cachedAt;
                        }
                        
                        if (!stats.newestEntry || entry.metadata.cachedAt > stats.newestEntry) {
                            stats.newestEntry = entry.metadata.cachedAt;
                        }
                    }
                }
            });
            
            return stats;
            
        } catch (error) {
            Logger.error('Failed to get cache stats', {}, error);
            return null;
        }
    }
}

// Create global Cache instance
const Cache = new CacheManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Cache;
}
