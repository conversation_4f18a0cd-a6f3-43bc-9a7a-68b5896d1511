/**
 * config.js - Configuration and Constants for GA4 Insights Extension
 * 
 * This file contains all configuration settings, API endpoints,
 * and constants used throughout the extension.
 */

const CONFIG = {
    // Application Info
    APP_NAME: 'GA4 Insights',
    VERSION: '0.1.0',
    
    // Backend API Configuration
    BACKEND_URL: 'https://pietramundodigital.com/api/v1',
    
    // Google APIs Configuration
    GA4_DATA_API_BASE_URL: 'https://analyticsdata.googleapis.com/v1beta/properties',
    GA4_ADMIN_API_BASE_URL: 'https://analyticsadmin.googleapis.com/v1beta',
    GOOGLE_TOKEN_INFO_URL: 'https://oauth2.googleapis.com/tokeninfo',
    
    // OAuth Scopes
    OAUTH_SCOPES: [
        'https://www.googleapis.com/auth/analytics.readonly',
        'email',
        'profile'
    ],
    
    // Cache Configuration
    CACHE: {
        TTL: {
            AUTH_TOKEN: 24 * 60 * 60 * 1000,        // 24 hours
            USER_DATA: 24 * 60 * 60 * 1000,         // 24 hours
            PROPERTIES_LIST: 24 * 60 * 60 * 1000,   // 24 hours
            USER_PREFERENCES: 30 * 24 * 60 * 60 * 1000, // 30 days
            RECENT_DATA: 15 * 60 * 1000,            // 15 minutes
            HISTORICAL_DATA: 60 * 60 * 1000,        // 1 hour
            ACCOUNT_SUMMARIES: 60 * 60 * 1000,      // 1 hour
            APP_STATE: 24 * 60 * 60 * 1000,         // 24 hours
            DEBUG_LOGS: 60 * 60 * 1000              // 1 hour
        },
        MAX_SIZE: 5 * 1024 * 1024,                  // 5MB Chrome limit
        PREFIX: 'ga4_cache_'
    },
    
    // Storage Keys
    STORAGE_KEYS: {
        AUTH_TOKEN: 'ga4_auth_token',
        TOKEN_EXPIRY: 'ga4_token_expiry',
        USER_DATA: 'ga4_user_data',
        USER_DATA_EXPIRY: 'ga4_user_data_expiry',
        PROPERTIES_LIST: 'ga4_properties_list',
        PROPERTIES_EXPIRY: 'ga4_properties_expiry',
        SELECTED_PROPERTY: 'ga4_selected_property',
        USER_PREFERENCES: 'ga4_user_preferences',
        PREFERENCES_EXPIRY: 'ga4_preferences_expiry',
        APP_STATE: 'ga4_app_state',
        APP_STATE_EXPIRY: 'ga4_app_state_expiry'
    },
    
    // Page Dimension Options
    PAGE_DIMENSIONS: {
        pagePath: {
            name: 'Ruta de Página',
            description: 'Analiza esta página específica por su URL',
            apiName: 'pagePath',
            filterType: 'exact_match',
            helpText: 'Muestra datos para usuarios que visitaron exactamente esta URL'
        },
        landingPage: {
            name: 'Página de Destino',
            description: 'Analiza sesiones que comenzaron en esta página',
            apiName: 'landingPage',
            filterType: 'exact_match',
            helpText: 'Muestra datos para sesiones que empezaron en esta página'
        },
        exitPage: {
            name: 'Página de Salida',
            description: 'Analiza sesiones que terminaron en esta página',
            apiName: 'exitPage',
            filterType: 'exact_match',
            helpText: 'Muestra datos para sesiones que terminaron en esta página'
        },
        pagePathPlusQueryString: {
            name: 'Página + Parámetros',
            description: 'Incluye parámetros de URL en el análisis',
            apiName: 'pagePathPlusQueryString',
            filterType: 'exact_match',
            helpText: 'Incluye parámetros de consulta en el análisis (ej: ?utm_source=google)'
        }
    },
    
    // Date Range Options
    DATE_RANGES: {
        PAGE_VIEW: {
            yesterday: {
                label: 'Ayer',
                start: 'yesterday',
                end: 'yesterday',
                days: 1
            },
            '7daysAgo': {
                label: 'Últimos 7 días',
                start: '7daysAgo',
                end: 'yesterday',
                days: 7
            },
            '30daysAgo': {
                label: 'Últimos 30 días',
                start: '30daysAgo',
                end: 'yesterday',
                days: 30
            }
        },
        SITE_VIEW: {
            '7daysAgo': {
                label: 'Últimos 7 días',
                start: '7daysAgo',
                end: 'yesterday',
                days: 7
            },
            '28daysAgo': {
                label: 'Últimos 28 días',
                start: '28daysAgo',
                end: 'yesterday',
                days: 28
            },
            '90daysAgo': {
                label: 'Últimos 90 días',
                start: '90daysAgo',
                end: 'yesterday',
                days: 90
            }
        }
    },
    
    // Default Settings
    DEFAULTS: {
        PAGE_DIMENSION: 'pagePath',
        PAGE_DATE_RANGE: '7daysAgo',
        SITE_DATE_RANGE: '28daysAgo',
        LANGUAGE: 'es',
        THEME: 'light',
        COMPACT_MODE: false,
        CACHE_ENABLED: true,
        DEBUG_MODE: false
    },
    
    // GA4 Metrics Configuration
    METRICS: {
        PAGE_ANALYSIS: [
            'activeUsers',
            'screenPageViews',
            'engagementRate',
            'averageSessionDuration',
            'bounceRate'
        ],
        SITE_OVERVIEW: [
            'activeUsers',
            'newUsers',
            'sessions',
            'engagementRate',
            'conversions',
            'totalRevenue'
        ],
        TRAFFIC_SOURCES: [
            'activeUsers',
            'sessions',
            'engagementRate'
        ],
        DEVICES: [
            'activeUsers',
            'sessions',
            'engagementRate'
        ]
    },
    
    // GA4 Dimensions Configuration
    DIMENSIONS: {
        PAGE_ANALYSIS: ['date'],
        SITE_OVERVIEW: ['date'],
        TRAFFIC_SOURCES: ['sessionSourceMedium'],
        DEVICES: ['deviceCategory'],
        TOP_PAGES: ['pagePath', 'pageTitle'],
        GEOGRAPHY: ['country', 'city']
    },
    
    // API Limits and Timeouts
    API: {
        TIMEOUT: 30000,                    // 30 seconds
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000,                 // 1 second
        MAX_RESULTS_PER_QUERY: 100,
        RATE_LIMIT_DELAY: 100              // 100ms between requests
    },
    
    // Plan Limits
    PLANS: {
        FREE: {
            API_CALL_LIMIT: 50,
            MAX_DATE_RANGE_DAYS: 30,
            FEATURES: {
                canCompareDates: false,
                advancedFilters: false,
                exportData: false,
                customDimensions: false,
                emailReports: false
            }
        },
        PREMIUM: {
            API_CALL_LIMIT: null,          // Unlimited
            MAX_DATE_RANGE_DAYS: 365,
            FEATURES: {
                canCompareDates: true,
                advancedFilters: true,
                exportData: true,
                customDimensions: true,
                emailReports: true
            }
        }
    },
    
    // UI Configuration
    UI: {
        POPUP_WIDTH: 420,
        POPUP_HEIGHT: 600,
        ANIMATION_DURATION: 300,
        DEBOUNCE_DELAY: 500,
        TOAST_DURATION: 5000
    },
    
    // Logging Configuration
    LOGGING: {
        LEVELS: {
            ERROR: 0,
            WARN: 1,
            INFO: 2,
            DEBUG: 3,
            TRACE: 4
        },
        DEFAULT_LEVEL: 'INFO',
        MAX_LOG_ENTRIES: 100,
        CONSOLE_ENABLED: true
    },
    
    // Error Messages
    ERRORS: {
        AUTH_FAILED: 'Error de autenticación. Por favor, intenta de nuevo.',
        TOKEN_EXPIRED: 'Tu sesión ha expirado. Por favor, vuelve a autenticarte.',
        API_LIMIT_EXCEEDED: 'Has alcanzado el límite de consultas de tu plan.',
        NETWORK_ERROR: 'Error de conexión. Verifica tu conexión a internet.',
        GA4_API_ERROR: 'Error al consultar Google Analytics. Intenta más tarde.',
        PROPERTY_ACCESS_DENIED: 'No tienes permisos para acceder a esta propiedad.',
        NO_DATA_AVAILABLE: 'No hay datos disponibles para el período seleccionado.',
        INVALID_PROPERTY: 'La propiedad seleccionada no es válida.',
        CACHE_ERROR: 'Error al acceder al cache local.',
        UNKNOWN_ERROR: 'Ha ocurrido un error inesperado.'
    },
    
    // Success Messages
    SUCCESS: {
        AUTH_SUCCESS: 'Autenticación exitosa',
        DATA_LOADED: 'Datos cargados correctamente',
        CACHE_CLEARED: 'Cache limpiado exitosamente',
        PREFERENCES_SAVED: 'Preferencias guardadas'
    },
    
    // External URLs
    URLS: {
        UPGRADE: 'https://pietramundodigital.com/ga4-insights/upgrade',
        HELP: 'https://pietramundodigital.com/ga4-insights/help',
        PRIVACY: 'https://pietramundodigital.com/ga4-insights/privacy',
        TERMS: 'https://pietramundodigital.com/ga4-insights/terms',
        GA4_SETUP_GUIDE: 'https://support.google.com/analytics/answer/9304153',
        PERMISSIONS_HELP: 'https://pietramundodigital.com/ga4-insights/permissions'
    },
    
    // Feature Flags
    FEATURES: {
        ENABLE_COMPARISON: false,          // Will be enabled for premium users
        ENABLE_EXPORT: false,              // Will be enabled for premium users
        ENABLE_ADVANCED_FILTERS: false,    // Will be enabled for premium users
        ENABLE_EMAIL_REPORTS: false,       // Will be enabled for premium users
        ENABLE_DEBUG_MODE: true,           // For development
        ENABLE_ANALYTICS_TRACKING: false   // For usage analytics
    }
};

// Freeze the configuration to prevent accidental modifications
Object.freeze(CONFIG);

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

// Log configuration loaded
console.log(`[GA4 Insights] Configuration loaded - Version ${CONFIG.VERSION}`);
