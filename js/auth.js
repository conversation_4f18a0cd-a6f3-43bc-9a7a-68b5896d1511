/**
 * auth.js - Authentication Module for GA4 Insights Extension
 * 
 * Handles Google OAuth 2.0 authentication, token management,
 * and communication with the backend for user verification.
 */

class AuthManager {
    constructor() {
        this.isAuthenticated = false;
        this.currentUser = null;
        this.authToken = null;
        this.tokenExpiry = null;
        
        Logger.info('AuthManager initialized');
    }
    
    /**
     * Check current authentication status
     * @returns {boolean} Whether user is authenticated
     */
    async checkAuthStatus() {
        try {
            Logger.debug('Checking authentication status');
            
            // Check for stored auth token
            const storedAuth = await chrome.storage.local.get([
                CONFIG.STORAGE_KEYS.AUTH_TOKEN,
                CONFIG.STORAGE_KEYS.TOKEN_EXPIRY,
                CONFIG.STORAGE_KEYS.USER_DATA
            ]);
            
            const authToken = storedAuth[CONFIG.STORAGE_KEYS.AUTH_TOKEN];
            const tokenExpiry = storedAuth[CONFIG.STORAGE_KEYS.TOKEN_EXPIRY];
            const userData = storedAuth[CONFIG.STORAGE_KEYS.USER_DATA];
            
            if (!authToken || !tokenExpiry) {
                Logger.debug('No stored auth token found');
                return false;
            }
            
            // Check if token is expired
            if (Date.now() >= tokenExpiry) {
                Logger.info('Stored auth token expired, clearing');
                await this.clearAuthData();
                return false;
            }
            
            // Verify token with Google
            const isValidGoogle = await this.verifyGoogleToken();
            if (!isValidGoogle) {
                Logger.warn('Google token verification failed');
                await this.clearAuthData();
                return false;
            }
            
            // Verify with backend
            const isValidBackend = await this.verifyWithBackend(authToken);
            if (!isValidBackend) {
                Logger.warn('Backend token verification failed');
                await this.clearAuthData();
                return false;
            }
            
            // Set current state
            this.isAuthenticated = true;
            this.authToken = authToken;
            this.tokenExpiry = tokenExpiry;
            this.currentUser = userData;
            
            Logger.info('Authentication status verified', { 
                userId: userData?.userId,
                expiresIn: Math.round((tokenExpiry - Date.now()) / 1000 / 60) + ' minutes'
            });
            
            return true;
            
        } catch (error) {
            Logger.error('Authentication status check failed', {}, error);
            await this.clearAuthData();
            return false;
        }
    }
    
    /**
     * Initiate Google OAuth authentication flow
     * @returns {Object} Authentication result
     */
    async initiateGoogleAuth() {
        try {
            Logger.info('Starting Google OAuth flow');
            
            // Request Google OAuth token
            const googleToken = await this.getGoogleAuthToken();
            if (!googleToken) {
                throw new Error('Failed to obtain Google auth token');
            }
            
            Logger.debug('Google token obtained successfully');
            
            // Verify and process with backend
            const backendResult = await this.processWithBackend(googleToken);
            if (!backendResult.success) {
                throw new Error(backendResult.error || 'Backend processing failed');
            }
            
            // Store authentication data
            await this.storeAuthData(backendResult.data);
            
            // Update current state
            this.isAuthenticated = true;
            this.authToken = backendResult.data.jwtToken;
            this.tokenExpiry = new Date(backendResult.data.tokenExpiry).getTime();
            this.currentUser = backendResult.data;
            
            Logger.info('Google authentication completed successfully', {
                userId: backendResult.data.userId,
                plan: backendResult.data.plan.planId
            });
            
            return {
                success: true,
                user: backendResult.data
            };
            
        } catch (error) {
            Logger.error('Google authentication failed', { 
                error: error.message,
                stack: error.stack 
            }, error);
            
            return {
                success: false,
                error: this.getReadableErrorMessage(error)
            };
        }
    }
    
    /**
     * Get Google OAuth token using Chrome Identity API
     * @private
     */
    async getGoogleAuthToken() {
        return new Promise((resolve, reject) => {
            Logger.debug('Requesting Google OAuth token');
            
            chrome.identity.getAuthToken({
                interactive: true,
                scopes: CONFIG.OAUTH_SCOPES
            }, (token) => {
                if (chrome.runtime.lastError) {
                    Logger.error('Chrome identity API error', {
                        error: chrome.runtime.lastError.message
                    });
                    reject(new Error(chrome.runtime.lastError.message));
                    return;
                }
                
                if (!token) {
                    Logger.error('No token received from Chrome identity API');
                    reject(new Error('No token received'));
                    return;
                }
                
                Logger.debug('Google OAuth token received', {
                    tokenLength: token.length,
                    tokenPreview: token.substring(0, 20) + '...'
                });
                
                resolve(token);
            });
        });
    }
    
    /**
     * Verify Google token and process with backend
     * @private
     */
    async processWithBackend(googleToken) {
        try {
            Logger.debug('Processing Google token with backend');
            
            const requestId = this.generateRequestId();
            window.currentRequestId = requestId;
            
            const response = await fetch(`${CONFIG.BACKEND_URL}/auth/verify-token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Request-ID': requestId,
                    'X-Extension-Version': CONFIG.VERSION
                },
                body: JSON.stringify({
                    googleAccessToken: googleToken,
                    clientInfo: {
                        extensionVersion: CONFIG.VERSION,
                        userAgent: navigator.userAgent,
                        timestamp: Date.now()
                    }
                })
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                Logger.error('Backend verification failed', {
                    status: response.status,
                    statusText: response.statusText,
                    error: errorText,
                    requestId
                });
                throw new Error(`Backend verification failed: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success) {
                Logger.error('Backend returned error', {
                    error: data.error,
                    requestId
                });
                throw new Error(data.error?.message || 'Backend verification failed');
            }
            
            Logger.info('Backend verification successful', {
                userId: data.data.userId,
                requestId
            });
            
            return data;
            
        } catch (error) {
            Logger.error('Backend processing failed', {
                error: error.message
            }, error);
            throw error;
        }
    }
    
    /**
     * Verify Google token directly with Google
     * @private
     */
    async verifyGoogleToken() {
        try {
            // Get current Google token
            const googleToken = await this.getCurrentGoogleToken();
            if (!googleToken) {
                return false;
            }
            
            // Verify with Google's tokeninfo endpoint
            const response = await fetch(`${CONFIG.GOOGLE_TOKEN_INFO_URL}?access_token=${googleToken}`);
            
            if (!response.ok) {
                Logger.debug('Google token verification failed', { status: response.status });
                return false;
            }
            
            const tokenInfo = await response.json();
            
            // Check if token has required scopes
            const requiredScopes = CONFIG.OAUTH_SCOPES;
            const tokenScopes = tokenInfo.scope ? tokenInfo.scope.split(' ') : [];
            
            const hasRequiredScopes = requiredScopes.every(scope => 
                tokenScopes.includes(scope)
            );
            
            if (!hasRequiredScopes) {
                Logger.warn('Google token missing required scopes', {
                    required: requiredScopes,
                    actual: tokenScopes
                });
                return false;
            }
            
            Logger.debug('Google token verification successful');
            return true;
            
        } catch (error) {
            Logger.error('Google token verification error', {}, error);
            return false;
        }
    }
    
    /**
     * Get current Google token from Chrome Identity API
     * @private
     */
    async getCurrentGoogleToken() {
        return new Promise((resolve) => {
            chrome.identity.getAuthToken({
                interactive: false
            }, (token) => {
                if (chrome.runtime.lastError || !token) {
                    resolve(null);
                } else {
                    resolve(token);
                }
            });
        });
    }
    
    /**
     * Verify authentication with backend
     * @private
     */
    async verifyWithBackend(authToken) {
        try {
            Logger.debug('Verifying authentication with backend');
            
            const response = await fetch(`${CONFIG.BACKEND_URL}/users/profile`, {
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                Logger.debug('Backend verification failed', { status: response.status });
                return false;
            }
            
            const data = await response.json();
            
            if (data.success && data.data) {
                // Update stored user data
                await chrome.storage.local.set({
                    [CONFIG.STORAGE_KEYS.USER_DATA]: data.data,
                    [CONFIG.STORAGE_KEYS.USER_DATA + '_expiry']: Date.now() + CONFIG.CACHE.TTL.USER_DATA
                });
                
                this.currentUser = data.data;
                Logger.debug('Backend verification successful');
                return true;
            }
            
            return false;
            
        } catch (error) {
            Logger.error('Backend verification error', {}, error);
            return false;
        }
    }
    
    /**
     * Store authentication data in chrome.storage
     * @private
     */
    async storeAuthData(userData) {
        try {
            Logger.debug('Storing authentication data');
            
            const authData = {
                [CONFIG.STORAGE_KEYS.AUTH_TOKEN]: userData.jwtToken,
                [CONFIG.STORAGE_KEYS.TOKEN_EXPIRY]: new Date(userData.tokenExpiry).getTime(),
                [CONFIG.STORAGE_KEYS.USER_DATA]: userData,
                [CONFIG.STORAGE_KEYS.USER_DATA_EXPIRY]: Date.now() + CONFIG.CACHE.TTL.USER_DATA
            };
            
            await chrome.storage.local.set(authData);
            
            Logger.info('Authentication data stored successfully', {
                userId: userData.userId,
                expiresAt: userData.tokenExpiry
            });
            
        } catch (error) {
            Logger.error('Failed to store authentication data', {}, error);
            throw error;
        }
    }
    
    /**
     * Clear all authentication data
     */
    async clearAuthData() {
        try {
            Logger.info('Clearing authentication data');
            
            // Clear from chrome.storage
            await chrome.storage.local.remove([
                CONFIG.STORAGE_KEYS.AUTH_TOKEN,
                CONFIG.STORAGE_KEYS.TOKEN_EXPIRY,
                CONFIG.STORAGE_KEYS.USER_DATA,
                CONFIG.STORAGE_KEYS.USER_DATA_EXPIRY,
                CONFIG.STORAGE_KEYS.SELECTED_PROPERTY
            ]);
            
            // Revoke Google token
            try {
                const googleToken = await this.getCurrentGoogleToken();
                if (googleToken) {
                    chrome.identity.removeCachedAuthToken({ token: googleToken });
                }
            } catch (error) {
                Logger.warn('Failed to revoke Google token', {}, error);
            }
            
            // Reset state
            this.isAuthenticated = false;
            this.currentUser = null;
            this.authToken = null;
            this.tokenExpiry = null;
            
            Logger.info('Authentication data cleared successfully');
            
        } catch (error) {
            Logger.error('Failed to clear authentication data', {}, error);
            throw error;
        }
    }
    
    /**
     * Logout user
     */
    async logout() {
        try {
            Logger.info('Logging out user');
            
            // Notify backend
            if (this.authToken) {
                try {
                    await fetch(`${CONFIG.BACKEND_URL}/auth/logout`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.authToken}`,
                            'Content-Type': 'application/json'
                        }
                    });
                } catch (error) {
                    Logger.warn('Failed to notify backend of logout', {}, error);
                }
            }
            
            // Clear all auth data
            await this.clearAuthData();
            
            Logger.info('User logged out successfully');
            
        } catch (error) {
            Logger.error('Logout failed', {}, error);
            throw error;
        }
    }
    
    /**
     * Get current user data
     * @returns {Object|null} Current user data
     */
    getCurrentUser() {
        return this.currentUser;
    }
    
    /**
     * Get current auth token
     * @returns {string|null} Current auth token
     */
    getAuthToken() {
        return this.authToken;
    }
    
    /**
     * Check if user is authenticated
     * @returns {boolean} Authentication status
     */
    isUserAuthenticated() {
        return this.isAuthenticated && this.authToken && Date.now() < this.tokenExpiry;
    }
    
    /**
     * Generate unique request ID
     * @private
     */
    generateRequestId() {
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Convert technical errors to user-friendly messages
     * @private
     */
    getReadableErrorMessage(error) {
        const message = error.message.toLowerCase();
        
        if (message.includes('user did not approve') || message.includes('authorization')) {
            return 'Permisos denegados. Necesitamos acceso a Google Analytics para funcionar.';
        }
        
        if (message.includes('network') || message.includes('fetch')) {
            return 'Error de conexión. Verifica tu conexión a internet.';
        }
        
        if (message.includes('popup') || message.includes('blocked')) {
            return 'Popup bloqueado. Permite popups para esta extensión.';
        }
        
        if (message.includes('backend') || message.includes('server')) {
            return 'Error del servidor. Intenta de nuevo en unos momentos.';
        }
        
        return 'Error de autenticación. Por favor, intenta de nuevo.';
    }
}

// Create global Auth instance
const Auth = new AuthManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Auth;
}
