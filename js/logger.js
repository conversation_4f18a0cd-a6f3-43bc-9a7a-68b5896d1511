/**
 * logger.js - Logging System for GA4 Insights Extension
 * 
 * Provides structured logging with different levels, context,
 * and debugging capabilities for the extension.
 */

class Logger {
    constructor() {
        this.levels = CONFIG.LOGGING.LEVELS;
        this.currentLevel = this.levels[CONFIG.LOGGING.DEFAULT_LEVEL] || this.levels.INFO;
        this.maxEntries = CONFIG.LOGGING.MAX_LOG_ENTRIES;
        this.consoleEnabled = CONFIG.LOGGING.CONSOLE_ENABLED;
        this.logs = [];
        
        // Initialize logger
        this.info('Logger initialized', { 
            level: CONFIG.LOGGING.DEFAULT_LEVEL,
            maxEntries: this.maxEntries 
        });
    }
    
    /**
     * Set the logging level
     * @param {string} level - The logging level (ERROR, WARN, INFO, DEBUG, TRACE)
     */
    setLevel(level) {
        const upperLevel = level.toUpperCase();
        if (this.levels.hasOwnProperty(upperLevel)) {
            this.currentLevel = this.levels[upperLevel];
            this.info('Log level changed', { newLevel: upperLevel });
        } else {
            this.warn('Invalid log level', { attempted: level, available: Object.keys(this.levels) });
        }
    }
    
    /**
     * Log an error message
     * @param {string} message - The error message
     * @param {Object} context - Additional context
     * @param {Error} error - Optional error object
     */
    error(message, context = {}, error = null) {
        this._log('ERROR', message, {
            ...context,
            ...(error && {
                errorName: error.name,
                errorMessage: error.message,
                errorStack: error.stack
            })
        });
    }
    
    /**
     * Log a warning message
     * @param {string} message - The warning message
     * @param {Object} context - Additional context
     */
    warn(message, context = {}) {
        this._log('WARN', message, context);
    }
    
    /**
     * Log an info message
     * @param {string} message - The info message
     * @param {Object} context - Additional context
     */
    info(message, context = {}) {
        this._log('INFO', message, context);
    }
    
    /**
     * Log a debug message
     * @param {string} message - The debug message
     * @param {Object} context - Additional context
     */
    debug(message, context = {}) {
        this._log('DEBUG', message, context);
    }
    
    /**
     * Log a trace message
     * @param {string} message - The trace message
     * @param {Object} context - Additional context
     */
    trace(message, context = {}) {
        this._log('TRACE', message, context);
    }
    
    /**
     * Internal logging method
     * @private
     */
    _log(level, message, context) {
        const levelValue = this.levels[level];
        
        // Check if this level should be logged
        if (levelValue > this.currentLevel) {
            return;
        }
        
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            level,
            message,
            context: this._sanitizeContext(context),
            requestId: this._getCurrentRequestId(),
            url: this._getCurrentUrl(),
            userAgent: navigator.userAgent,
            extensionVersion: CONFIG.VERSION
        };
        
        // Add to internal log storage
        this._addToStorage(logEntry);
        
        // Console output if enabled
        if (this.consoleEnabled) {
            this._logToConsole(logEntry);
        }
        
        // Send critical errors to backend (if configured)
        if (level === 'ERROR') {
            this._sendErrorToBackend(logEntry);
        }
    }
    
    /**
     * Sanitize context to remove sensitive information
     * @private
     */
    _sanitizeContext(context) {
        const sanitized = { ...context };
        
        // Remove or mask sensitive fields
        const sensitiveFields = ['password', 'token', 'accessToken', 'refreshToken', 'apiKey'];
        
        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                if (typeof sanitized[field] === 'string' && sanitized[field].length > 10) {
                    sanitized[field] = sanitized[field].substring(0, 10) + '...';
                } else {
                    sanitized[field] = '[REDACTED]';
                }
            }
        });
        
        // Limit context size
        const contextString = JSON.stringify(sanitized);
        if (contextString.length > 1000) {
            return { 
                ...sanitized, 
                _truncated: true, 
                _originalSize: contextString.length 
            };
        }
        
        return sanitized;
    }
    
    /**
     * Get current request ID from context
     * @private
     */
    _getCurrentRequestId() {
        // Try to get from global state or generate one
        if (window.currentRequestId) {
            return window.currentRequestId;
        }
        
        // Generate a simple request ID
        return 'req_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * Get current URL being analyzed
     * @private
     */
    _getCurrentUrl() {
        try {
            // Try to get from extension context
            if (window.currentTabUrl) {
                return window.currentTabUrl;
            }
            return 'extension://popup';
        } catch (error) {
            return 'unknown';
        }
    }
    
    /**
     * Add log entry to internal storage
     * @private
     */
    _addToStorage(logEntry) {
        this.logs.push(logEntry);
        
        // Maintain max entries limit
        if (this.logs.length > this.maxEntries) {
            this.logs = this.logs.slice(-this.maxEntries);
        }
        
        // Store in chrome.storage for persistence (async)
        this._persistLogs();
    }
    
    /**
     * Persist logs to chrome.storage
     * @private
     */
    async _persistLogs() {
        try {
            const recentLogs = this.logs.slice(-50); // Keep only last 50 entries
            await chrome.storage.local.set({
                'ga4_debug_logs': recentLogs,
                'ga4_debug_logs_expiry': Date.now() + CONFIG.CACHE.TTL.DEBUG_LOGS
            });
        } catch (error) {
            console.error('[Logger] Failed to persist logs:', error);
        }
    }
    
    /**
     * Output to browser console
     * @private
     */
    _logToConsole(logEntry) {
        const { timestamp, level, message, context } = logEntry;
        const timeStr = new Date(timestamp).toLocaleTimeString();
        const prefix = `[GA4 Insights ${timeStr}]`;
        
        switch (level) {
            case 'ERROR':
                console.error(prefix, message, context);
                break;
            case 'WARN':
                console.warn(prefix, message, context);
                break;
            case 'INFO':
                console.info(prefix, message, context);
                break;
            case 'DEBUG':
                console.debug(prefix, message, context);
                break;
            case 'TRACE':
                console.trace(prefix, message, context);
                break;
            default:
                console.log(prefix, message, context);
        }
    }
    
    /**
     * Send critical errors to backend for monitoring
     * @private
     */
    async _sendErrorToBackend(logEntry) {
        try {
            // Only send if user is authenticated and has opted in
            const authToken = await chrome.storage.local.get(CONFIG.STORAGE_KEYS.AUTH_TOKEN);
            if (!authToken[CONFIG.STORAGE_KEYS.AUTH_TOKEN]) {
                return;
            }
            
            // Don't send too frequently
            const lastErrorSent = await chrome.storage.local.get('last_error_sent');
            const now = Date.now();
            if (lastErrorSent.last_error_sent && (now - lastErrorSent.last_error_sent) < 60000) {
                return; // Don't send more than once per minute
            }
            
            // Send to backend
            fetch(`${CONFIG.BACKEND_URL}/logs/error`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken[CONFIG.STORAGE_KEYS.AUTH_TOKEN]}`
                },
                body: JSON.stringify({
                    logEntry,
                    userAgent: navigator.userAgent,
                    extensionVersion: CONFIG.VERSION
                })
            }).catch(() => {
                // Silently fail - don't log errors about logging
            });
            
            // Update last sent timestamp
            await chrome.storage.local.set({ 'last_error_sent': now });
            
        } catch (error) {
            // Silently fail - don't create infinite loops
        }
    }
    
    /**
     * Get recent logs
     * @param {number} count - Number of recent logs to return
     * @returns {Array} Recent log entries
     */
    getRecentLogs(count = 50) {
        return this.logs.slice(-count);
    }
    
    /**
     * Get logs by level
     * @param {string} level - Log level to filter by
     * @returns {Array} Filtered log entries
     */
    getLogsByLevel(level) {
        return this.logs.filter(log => log.level === level.toUpperCase());
    }
    
    /**
     * Clear all logs
     */
    async clearLogs() {
        this.logs = [];
        try {
            await chrome.storage.local.remove(['ga4_debug_logs', 'ga4_debug_logs_expiry']);
            this.info('Logs cleared');
        } catch (error) {
            this.error('Failed to clear persisted logs', {}, error);
        }
    }
    
    /**
     * Export logs as JSON
     * @returns {string} JSON string of all logs
     */
    exportLogs() {
        return JSON.stringify({
            exportedAt: new Date().toISOString(),
            extensionVersion: CONFIG.VERSION,
            logCount: this.logs.length,
            logs: this.logs
        }, null, 2);
    }
    
    /**
     * Load persisted logs from storage
     */
    async loadPersistedLogs() {
        try {
            const result = await chrome.storage.local.get(['ga4_debug_logs', 'ga4_debug_logs_expiry']);
            
            if (result.ga4_debug_logs && result.ga4_debug_logs_expiry) {
                if (Date.now() < result.ga4_debug_logs_expiry) {
                    this.logs = result.ga4_debug_logs || [];
                    this.info('Persisted logs loaded', { count: this.logs.length });
                } else {
                    // Expired logs
                    await chrome.storage.local.remove(['ga4_debug_logs', 'ga4_debug_logs_expiry']);
                    this.info('Expired logs removed');
                }
            }
        } catch (error) {
            this.error('Failed to load persisted logs', {}, error);
        }
    }
    
    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether to enable debug mode
     */
    setDebugMode(enabled) {
        if (enabled) {
            this.setLevel('DEBUG');
            this.consoleEnabled = true;
        } else {
            this.setLevel('INFO');
        }
        
        this.info('Debug mode changed', { enabled });
    }
}

// Create global logger instance
const Logger = new Logger();

// Load persisted logs on initialization
Logger.loadPersistedLogs();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Logger;
}
