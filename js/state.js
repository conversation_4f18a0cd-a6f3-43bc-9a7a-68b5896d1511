/**
 * state.js - State Management for GA4 Insights Extension
 * 
 * Manages global application state, user preferences,
 * and data persistence across sessions.
 */

class StateManager {
    constructor() {
        this.state = {
            // User data
            currentUser: null,
            selectedProperty: null,
            
            // UI state
            currentView: 'page', // 'page' or 'site'
            currentPage: '/',
            selectedDimension: CONFIG.DEFAULTS.PAGE_DIMENSION,
            
            // Date ranges
            pageDateRange: CONFIG.DEFAULTS.PAGE_DATE_RANGE,
            siteDateRange: CONFIG.DEFAULTS.SITE_DATE_RANGE,
            
            // Preferences
            preferences: {
                language: CONFIG.DEFAULTS.LANGUAGE,
                theme: CONFIG.DEFAULTS.THEME,
                compactMode: CONFIG.DEFAULTS.COMPACT_MODE,
                cacheEnabled: CONFIG.DEFAULTS.CACHE_ENABLED,
                debugMode: CONFIG.DEFAULTS.DEBUG_MODE
            },
            
            // Runtime state
            isLoading: false,
            lastError: null,
            lastRefresh: null
        };
        
        Logger.info('StateManager initialized');
    }
    
    /**
     * Initialize state manager
     */
    async init() {
        try {
            Logger.debug('Initializing state manager');
            
            // Load persisted state
            await this.loadPersistedState();
            
            // Load user preferences
            await this.loadUserPreferences();
            
            Logger.info('State manager initialized successfully');
            
        } catch (error) {
            Logger.error('Failed to initialize state manager', {}, error);
            throw error;
        }
    }
    
    /**
     * Load persisted state from chrome.storage
     * @private
     */
    async loadPersistedState() {
        try {
            Logger.debug('Loading persisted state');
            
            const keys = [
                CONFIG.STORAGE_KEYS.APP_STATE,
                CONFIG.STORAGE_KEYS.APP_STATE_EXPIRY,
                CONFIG.STORAGE_KEYS.SELECTED_PROPERTY,
                CONFIG.STORAGE_KEYS.USER_DATA
            ];
            
            const stored = await chrome.storage.local.get(keys);
            
            // Load app state if not expired
            const appState = stored[CONFIG.STORAGE_KEYS.APP_STATE];
            const appStateExpiry = stored[CONFIG.STORAGE_KEYS.APP_STATE_EXPIRY];
            
            if (appState && appStateExpiry && Date.now() < appStateExpiry) {
                this.state = { ...this.state, ...appState };
                Logger.debug('App state loaded from storage');
            }
            
            // Load selected property
            const selectedProperty = stored[CONFIG.STORAGE_KEYS.SELECTED_PROPERTY];
            if (selectedProperty) {
                this.state.selectedProperty = selectedProperty;
                Logger.debug('Selected property loaded', { 
                    propertyId: selectedProperty.propertyId 
                });
            }
            
            // Load user data
            const userData = stored[CONFIG.STORAGE_KEYS.USER_DATA];
            if (userData) {
                this.state.currentUser = userData;
                Logger.debug('User data loaded', { userId: userData.userId });
            }
            
        } catch (error) {
            Logger.error('Failed to load persisted state', {}, error);
            // Don't throw - use defaults
        }
    }
    
    /**
     * Load user preferences
     * @private
     */
    async loadUserPreferences() {
        try {
            Logger.debug('Loading user preferences');
            
            const stored = await chrome.storage.local.get([
                CONFIG.STORAGE_KEYS.USER_PREFERENCES,
                CONFIG.STORAGE_KEYS.USER_PREFERENCES + '_expiry'
            ]);
            
            const preferences = stored[CONFIG.STORAGE_KEYS.USER_PREFERENCES];
            const preferencesExpiry = stored[CONFIG.STORAGE_KEYS.USER_PREFERENCES + '_expiry'];
            
            if (preferences && preferencesExpiry && Date.now() < preferencesExpiry) {
                this.state.preferences = { ...this.state.preferences, ...preferences };
                Logger.debug('User preferences loaded', preferences);
            }
            
        } catch (error) {
            Logger.error('Failed to load user preferences', {}, error);
            // Don't throw - use defaults
        }
    }
    
    /**
     * Persist current state to chrome.storage
     * @private
     */
    async persistState() {
        try {
            const stateToPersist = {
                currentView: this.state.currentView,
                currentPage: this.state.currentPage,
                selectedDimension: this.state.selectedDimension,
                pageDateRange: this.state.pageDateRange,
                siteDateRange: this.state.siteDateRange,
                lastRefresh: this.state.lastRefresh
            };
            
            await chrome.storage.local.set({
                [CONFIG.STORAGE_KEYS.APP_STATE]: stateToPersist,
                [CONFIG.STORAGE_KEYS.APP_STATE_EXPIRY]: Date.now() + CONFIG.CACHE.TTL.APP_STATE
            });
            
            Logger.debug('State persisted successfully');
            
        } catch (error) {
            Logger.error('Failed to persist state', {}, error);
        }
    }
    
    /**
     * Persist user preferences
     * @private
     */
    async persistPreferences() {
        try {
            await chrome.storage.local.set({
                [CONFIG.STORAGE_KEYS.USER_PREFERENCES]: this.state.preferences,
                [CONFIG.STORAGE_KEYS.USER_PREFERENCES + '_expiry']: Date.now() + CONFIG.CACHE.TTL.USER_PREFERENCES
            });
            
            Logger.debug('Preferences persisted successfully');
            
        } catch (error) {
            Logger.error('Failed to persist preferences', {}, error);
        }
    }
    
    // User Data Methods
    
    /**
     * Set current user data
     * @param {Object} userData - User data object
     */
    setUser(userData) {
        this.state.currentUser = userData;
        Logger.debug('User data updated', { userId: userData?.userId });
    }
    
    /**
     * Get current user data
     * @returns {Object|null} Current user data
     */
    getUserData() {
        return this.state.currentUser;
    }
    
    /**
     * Get user plan information
     * @returns {Object|null} User plan data
     */
    getUserPlan() {
        return this.state.currentUser?.plan || null;
    }
    
    /**
     * Get user usage information
     * @returns {Object|null} User usage data
     */
    getUserUsage() {
        return this.state.currentUser?.usage || null;
    }
    
    // Property Methods
    
    /**
     * Set selected GA4 property
     * @param {Object} property - Property object
     */
    async setSelectedProperty(property) {
        this.state.selectedProperty = property;
        
        // Persist to storage
        await chrome.storage.local.set({
            [CONFIG.STORAGE_KEYS.SELECTED_PROPERTY]: property
        });
        
        Logger.info('Selected property updated', { 
            propertyId: property.propertyId,
            propertyName: property.propertyName 
        });
    }
    
    /**
     * Get selected GA4 property
     * @returns {Object|null} Selected property
     */
    getSelectedProperty() {
        return this.state.selectedProperty;
    }
    
    /**
     * Get selected property ID
     * @returns {string|null} Property ID
     */
    getSelectedPropertyId() {
        return this.state.selectedProperty?.propertyId || null;
    }
    
    // Page and View Methods
    
    /**
     * Set current page path
     * @param {string} pagePath - Current page path
     */
    setCurrentPage(pagePath) {
        this.state.currentPage = pagePath;
        this.persistState();
        Logger.debug('Current page updated', { pagePath });
    }
    
    /**
     * Get current page path
     * @returns {string} Current page path
     */
    getCurrentPage() {
        return this.state.currentPage;
    }
    
    /**
     * Set current view
     * @param {string} view - View name ('page' or 'site')
     */
    setCurrentView(view) {
        if (['page', 'site'].includes(view)) {
            this.state.currentView = view;
            this.persistState();
            Logger.debug('Current view updated', { view });
        }
    }
    
    /**
     * Get current view
     * @returns {string} Current view
     */
    getCurrentView() {
        return this.state.currentView;
    }
    
    // Dimension Methods
    
    /**
     * Set selected dimension for page analysis
     * @param {string} dimension - Dimension name
     */
    setSelectedDimension(dimension) {
        if (CONFIG.PAGE_DIMENSIONS[dimension]) {
            this.state.selectedDimension = dimension;
            this.persistState();
            Logger.debug('Selected dimension updated', { dimension });
        } else {
            Logger.warn('Invalid dimension', { dimension });
        }
    }
    
    /**
     * Get selected dimension
     * @returns {string} Selected dimension
     */
    getSelectedDimension() {
        return this.state.selectedDimension;
    }
    
    /**
     * Get dimension configuration
     * @param {string} dimension - Dimension name (optional, uses current if not provided)
     * @returns {Object} Dimension configuration
     */
    getDimensionConfig(dimension = null) {
        const dim = dimension || this.state.selectedDimension;
        return CONFIG.PAGE_DIMENSIONS[dim] || CONFIG.PAGE_DIMENSIONS[CONFIG.DEFAULTS.PAGE_DIMENSION];
    }
    
    // Date Range Methods
    
    /**
     * Set page date range
     * @param {string} range - Date range key
     */
    setPageDateRange(range) {
        if (CONFIG.DATE_RANGES.PAGE_VIEW[range]) {
            this.state.pageDateRange = range;
            this.persistState();
            Logger.debug('Page date range updated', { range });
        } else {
            Logger.warn('Invalid page date range', { range });
        }
    }
    
    /**
     * Get page date range
     * @returns {string} Page date range key
     */
    getPageDateRange() {
        return this.state.pageDateRange;
    }
    
    /**
     * Get page date range configuration
     * @returns {Object} Date range configuration
     */
    getPageDateRangeConfig() {
        return CONFIG.DATE_RANGES.PAGE_VIEW[this.state.pageDateRange] || 
               CONFIG.DATE_RANGES.PAGE_VIEW[CONFIG.DEFAULTS.PAGE_DATE_RANGE];
    }
    
    /**
     * Set site date range
     * @param {string} range - Date range key
     */
    setSiteDateRange(range) {
        if (CONFIG.DATE_RANGES.SITE_VIEW[range]) {
            this.state.siteDateRange = range;
            this.persistState();
            Logger.debug('Site date range updated', { range });
        } else {
            Logger.warn('Invalid site date range', { range });
        }
    }
    
    /**
     * Get site date range
     * @returns {string} Site date range key
     */
    getSiteDateRange() {
        return this.state.siteDateRange;
    }
    
    /**
     * Get site date range configuration
     * @returns {Object} Date range configuration
     */
    getSiteDateRangeConfig() {
        return CONFIG.DATE_RANGES.SITE_VIEW[this.state.siteDateRange] || 
               CONFIG.DATE_RANGES.SITE_VIEW[CONFIG.DEFAULTS.SITE_DATE_RANGE];
    }
    
    // Preferences Methods
    
    /**
     * Update user preferences
     * @param {Object} newPreferences - Preferences to update
     */
    async updatePreferences(newPreferences) {
        this.state.preferences = { ...this.state.preferences, ...newPreferences };
        await this.persistPreferences();
        Logger.info('Preferences updated', newPreferences);
    }
    
    /**
     * Get user preferences
     * @returns {Object} User preferences
     */
    getPreferences() {
        return { ...this.state.preferences };
    }
    
    /**
     * Get specific preference
     * @param {string} key - Preference key
     * @returns {*} Preference value
     */
    getPreference(key) {
        return this.state.preferences[key];
    }
    
    /**
     * Set specific preference
     * @param {string} key - Preference key
     * @param {*} value - Preference value
     */
    async setPreference(key, value) {
        this.state.preferences[key] = value;
        await this.persistPreferences();
        Logger.debug('Preference updated', { key, value });
    }
    
    // Runtime State Methods
    
    /**
     * Set loading state
     * @param {boolean} isLoading - Loading state
     */
    setLoading(isLoading) {
        this.state.isLoading = isLoading;
        Logger.debug('Loading state updated', { isLoading });
    }
    
    /**
     * Get loading state
     * @returns {boolean} Loading state
     */
    isLoading() {
        return this.state.isLoading;
    }
    
    /**
     * Set last error
     * @param {string|null} error - Error message
     */
    setLastError(error) {
        this.state.lastError = error;
        if (error) {
            Logger.warn('Error state set', { error });
        }
    }
    
    /**
     * Get last error
     * @returns {string|null} Last error message
     */
    getLastError() {
        return this.state.lastError;
    }
    
    /**
     * Clear last error
     */
    clearLastError() {
        this.state.lastError = null;
    }
    
    /**
     * Set last refresh timestamp
     */
    setLastRefresh() {
        this.state.lastRefresh = Date.now();
        this.persistState();
        Logger.debug('Last refresh timestamp updated');
    }
    
    /**
     * Get last refresh timestamp
     * @returns {number|null} Last refresh timestamp
     */
    getLastRefresh() {
        return this.state.lastRefresh;
    }
    
    /**
     * Get complete state (for debugging)
     * @returns {Object} Complete state object
     */
    getState() {
        return { ...this.state };
    }
    
    /**
     * Reset state to defaults
     */
    async resetState() {
        Logger.info('Resetting state to defaults');
        
        // Clear storage
        await chrome.storage.local.remove([
            CONFIG.STORAGE_KEYS.APP_STATE,
            CONFIG.STORAGE_KEYS.APP_STATE_EXPIRY,
            CONFIG.STORAGE_KEYS.USER_PREFERENCES,
            CONFIG.STORAGE_KEYS.USER_PREFERENCES + '_expiry'
        ]);
        
        // Reset to defaults (keep user and property data)
        const currentUser = this.state.currentUser;
        const selectedProperty = this.state.selectedProperty;
        
        this.state = {
            currentUser,
            selectedProperty,
            currentView: 'page',
            currentPage: '/',
            selectedDimension: CONFIG.DEFAULTS.PAGE_DIMENSION,
            pageDateRange: CONFIG.DEFAULTS.PAGE_DATE_RANGE,
            siteDateRange: CONFIG.DEFAULTS.SITE_DATE_RANGE,
            preferences: {
                language: CONFIG.DEFAULTS.LANGUAGE,
                theme: CONFIG.DEFAULTS.THEME,
                compactMode: CONFIG.DEFAULTS.COMPACT_MODE,
                cacheEnabled: CONFIG.DEFAULTS.CACHE_ENABLED,
                debugMode: CONFIG.DEFAULTS.DEBUG_MODE
            },
            isLoading: false,
            lastError: null,
            lastRefresh: null
        };
        
        Logger.info('State reset completed');
    }
}

// Create global State instance
const State = new StateManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = State;
}
