/**
 * utils.js - Utility Functions for GA4 Insights Extension
 * 
 * Collection of helper functions for data formatting, validation,
 * DOM manipulation, and other common operations.
 */

const Utils = {
    
    /**
     * Format metric values based on type
     * @param {string|number} value - The value to format
     * @param {string} type - The metric type
     * @param {Object} options - Formatting options
     * @returns {string} Formatted value
     */
    formatMetricValue(value, type, options = {}) {
        const numValue = parseFloat(value) || 0;
        
        switch (type) {
            case 'number':
            case 'integer':
                return new Intl.NumberFormat('es-ES', {
                    maximumFractionDigits: options.decimals || 0
                }).format(numValue);
                
            case 'percentage':
            case 'percent':
                return new Intl.NumberFormat('es-ES', {
                    style: 'percent',
                    minimumFractionDigits: options.decimals || 1,
                    maximumFractionDigits: options.decimals || 1
                }).format(numValue / 100);
                
            case 'duration':
            case 'time':
                return this.formatDuration(numValue);
                
            case 'currency':
                return new Intl.NumberFormat('es-ES', {
                    style: 'currency',
                    currency: options.currency || 'EUR'
                }).format(numValue);
                
            case 'rate':
                return `${numValue.toFixed(options.decimals || 2)}%`;
                
            default:
                return numValue.toString();
        }
    },
    
    /**
     * Format duration in seconds to human readable format
     * @param {number} seconds - Duration in seconds
     * @returns {string} Formatted duration
     */
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${Math.round(seconds)}s`;
        }
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.round(seconds % 60);
        
        if (minutes < 60) {
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }
        
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        
        return `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    
    /**
     * Format change percentage with appropriate sign and color
     * @param {number} change - Change percentage
     * @param {boolean} inverse - Whether lower is better (e.g., bounce rate)
     * @returns {Object} Formatted change with metadata
     */
    formatChange(change, inverse = false) {
        const numChange = parseFloat(change) || 0;
        const absChange = Math.abs(numChange);
        
        let trend = 'neutral';
        let icon = '➡️';
        let sign = '';
        
        if (numChange > 0) {
            trend = inverse ? 'negative' : 'positive';
            icon = inverse ? '📉' : '📈';
            sign = '+';
        } else if (numChange < 0) {
            trend = inverse ? 'positive' : 'negative';
            icon = inverse ? '📈' : '📉';
            sign = '';
        }
        
        return {
            value: `${sign}${absChange.toFixed(1)}%`,
            trend,
            icon,
            raw: numChange
        };
    },
    
    /**
     * Escape HTML to prevent XSS
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },
    
    /**
     * Debounce function calls
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * Throttle function calls
     * @param {Function} func - Function to throttle
     * @param {number} limit - Time limit in milliseconds
     * @returns {Function} Throttled function
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * Generate unique ID
     * @param {string} prefix - Optional prefix
     * @returns {string} Unique ID
     */
    generateId(prefix = 'id') {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    },
    
    /**
     * Deep clone an object
     * @param {*} obj - Object to clone
     * @returns {*} Cloned object
     */
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        
        if (obj instanceof Array) {
            return obj.map(item => this.deepClone(item));
        }
        
        if (typeof obj === 'object') {
            const cloned = {};
            Object.keys(obj).forEach(key => {
                cloned[key] = this.deepClone(obj[key]);
            });
            return cloned;
        }
        
        return obj;
    },
    
    /**
     * Check if value is empty (null, undefined, empty string, empty array, empty object)
     * @param {*} value - Value to check
     * @returns {boolean} Whether value is empty
     */
    isEmpty(value) {
        if (value === null || value === undefined) {
            return true;
        }
        
        if (typeof value === 'string') {
            return value.trim().length === 0;
        }
        
        if (Array.isArray(value)) {
            return value.length === 0;
        }
        
        if (typeof value === 'object') {
            return Object.keys(value).length === 0;
        }
        
        return false;
    },
    
    /**
     * Validate email address
     * @param {string} email - Email to validate
     * @returns {boolean} Whether email is valid
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    /**
     * Validate URL
     * @param {string} url - URL to validate
     * @returns {boolean} Whether URL is valid
     */
    isValidUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    },
    
    /**
     * Extract domain from URL
     * @param {string} url - URL to extract domain from
     * @returns {string|null} Domain or null if invalid
     */
    extractDomain(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.hostname;
        } catch {
            return null;
        }
    },
    
    /**
     * Extract page path from URL
     * @param {string} url - URL to extract path from
     * @returns {string} Page path
     */
    extractPagePath(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.pathname;
        } catch {
            return '/';
        }
    },
    
    /**
     * Format date for display
     * @param {Date|string|number} date - Date to format
     * @param {Object} options - Formatting options
     * @returns {string} Formatted date
     */
    formatDate(date, options = {}) {
        const dateObj = new Date(date);
        
        if (isNaN(dateObj.getTime())) {
            return 'Fecha inválida';
        }
        
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            ...options
        };
        
        return new Intl.DateTimeFormat('es-ES', defaultOptions).format(dateObj);
    },
    
    /**
     * Format relative time (e.g., "hace 2 horas")
     * @param {Date|string|number} date - Date to format
     * @returns {string} Relative time string
     */
    formatRelativeTime(date) {
        const dateObj = new Date(date);
        const now = new Date();
        const diffMs = now.getTime() - dateObj.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMinutes < 1) {
            return 'Ahora mismo';
        } else if (diffMinutes < 60) {
            return `Hace ${diffMinutes} minuto${diffMinutes !== 1 ? 's' : ''}`;
        } else if (diffHours < 24) {
            return `Hace ${diffHours} hora${diffHours !== 1 ? 's' : ''}`;
        } else if (diffDays < 7) {
            return `Hace ${diffDays} día${diffDays !== 1 ? 's' : ''}`;
        } else {
            return this.formatDate(dateObj);
        }
    },
    
    /**
     * Calculate percentage
     * @param {number} value - Value
     * @param {number} total - Total
     * @param {number} decimals - Number of decimal places
     * @returns {number} Percentage
     */
    calculatePercentage(value, total, decimals = 1) {
        if (total === 0) return 0;
        return parseFloat(((value / total) * 100).toFixed(decimals));
    },
    
    /**
     * Sort array of objects by property
     * @param {Array} array - Array to sort
     * @param {string} property - Property to sort by
     * @param {boolean} ascending - Sort direction
     * @returns {Array} Sorted array
     */
    sortByProperty(array, property, ascending = true) {
        return array.sort((a, b) => {
            const aVal = a[property];
            const bVal = b[property];
            
            if (aVal < bVal) return ascending ? -1 : 1;
            if (aVal > bVal) return ascending ? 1 : -1;
            return 0;
        });
    },
    
    /**
     * Group array of objects by property
     * @param {Array} array - Array to group
     * @param {string} property - Property to group by
     * @returns {Object} Grouped object
     */
    groupByProperty(array, property) {
        return array.reduce((groups, item) => {
            const key = item[property];
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(item);
            return groups;
        }, {});
    },
    
    /**
     * Truncate text to specified length
     * @param {string} text - Text to truncate
     * @param {number} maxLength - Maximum length
     * @param {string} suffix - Suffix to add when truncated
     * @returns {string} Truncated text
     */
    truncateText(text, maxLength, suffix = '...') {
        if (text.length <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - suffix.length) + suffix;
    },
    
    /**
     * Convert camelCase to human readable
     * @param {string} camelCase - CamelCase string
     * @returns {string} Human readable string
     */
    camelToHuman(camelCase) {
        return camelCase
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
    },
    
    /**
     * Get metric type from metric name
     * @param {string} metricName - GA4 metric name
     * @returns {string} Metric type for formatting
     */
    getMetricType(metricName) {
        const metricTypes = {
            // Count metrics
            'activeUsers': 'number',
            'newUsers': 'number',
            'sessions': 'number',
            'screenPageViews': 'number',
            'eventCount': 'number',
            'conversions': 'number',
            
            // Rate metrics
            'engagementRate': 'percentage',
            'bounceRate': 'percentage',
            'conversionRate': 'percentage',
            'newUserRate': 'percentage',
            
            // Duration metrics
            'averageSessionDuration': 'duration',
            'userEngagementDuration': 'duration',
            
            // Revenue metrics
            'totalRevenue': 'currency',
            'purchaseRevenue': 'currency',
            'averagePurchaseRevenue': 'currency',
            
            // Other metrics
            'eventsPerSession': 'number',
            'screenPageViewsPerSession': 'number'
        };
        
        return metricTypes[metricName] || 'number';
    },
    
    /**
     * Get metric icon
     * @param {string} metricName - GA4 metric name
     * @returns {string} Emoji icon for metric
     */
    getMetricIcon(metricName) {
        const metricIcons = {
            'activeUsers': '👥',
            'newUsers': '🆕',
            'sessions': '📊',
            'screenPageViews': '👁️',
            'engagementRate': '⚡',
            'bounceRate': '📉',
            'averageSessionDuration': '⏱️',
            'conversions': '🎯',
            'totalRevenue': '💰',
            'eventCount': '🔢'
        };
        
        return metricIcons[metricName] || '📈';
    },
    
    /**
     * Check if device is mobile
     * @returns {boolean} Whether device is mobile
     */
    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },
    
    /**
     * Copy text to clipboard
     * @param {string} text - Text to copy
     * @returns {Promise<boolean>} Whether copy was successful
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (error) {
            Logger.warn('Failed to copy to clipboard', { error: error.message });
            return false;
        }
    },
    
    /**
     * Download data as file
     * @param {string} data - Data to download
     * @param {string} filename - Filename
     * @param {string} mimeType - MIME type
     */
    downloadAsFile(data, filename, mimeType = 'text/plain') {
        const blob = new Blob([data], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Utils;
}
