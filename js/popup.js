/**
 * popup.js - Main Controller for GA4 Insights Extension Popup
 * 
 * This is the entry point for the extension popup. It handles initialization,
 * state management, and coordinates between different modules.
 */

class PopupController {
    constructor() {
        this.currentState = 'loading';
        this.currentView = 'page'; // 'page' or 'site'
        this.isInitialized = false;
        
        Logger.info('PopupController initialized');
    }
    
    /**
     * Initialize the extension popup
     */
    async init() {
        try {
            Logger.info('Starting popup initialization');
            
            // Show loading state
            this.showState('loading');
            this.updateLoadingProgress(0, 'Iniciando extensión...');
            
            // Initialize modules
            await this.initializeModules();
            this.updateLoadingProgress(20, '<PERSON><PERSON><PERSON><PERSON> cargados...');
            
            // Setup event listeners
            this.setupEventListeners();
            this.updateLoadingProgress(30, 'Eventos configurados...');
            
            // Check authentication status
            const isAuthenticated = await this.checkAuthentication();
            this.updateLoadingProgress(50, 'Verificando autenticación...');
            
            if (!isAuthenticated) {
                Logger.info('User not authenticated, showing auth screen');
                this.showState('auth');
                return;
            }
            
            // Load user data and properties
            await this.loadUserData();
            this.updateLoadingProgress(70, 'Cargando datos del usuario...');
            
            // Check if property is selected
            const hasProperty = await this.checkSelectedProperty();
            this.updateLoadingProgress(80, 'Verificando propiedad GA4...');
            
            if (!hasProperty) {
                Logger.info('No property selected, showing property selection');
                await this.showPropertySelection();
                return;
            }
            
            // Load current page data
            await this.loadCurrentPageData();
            this.updateLoadingProgress(90, 'Cargando datos de la página...');
            
            // Show main interface
            await this.showMainInterface();
            this.updateLoadingProgress(100, 'Completado');
            
            this.isInitialized = true;
            Logger.info('Popup initialization completed successfully');
            
        } catch (error) {
            Logger.error('Failed to initialize popup', { error: error.message }, error);
            this.showError('Error de inicialización', error.message);
        }
    }
    
    /**
     * Initialize required modules
     */
    async initializeModules() {
        try {
            // Initialize cache manager
            if (typeof Cache !== 'undefined') {
                await Cache.cleanup();
                Logger.debug('Cache manager initialized');
            }
            
            // Initialize state manager
            if (typeof State !== 'undefined') {
                await State.init();
                Logger.debug('State manager initialized');
            }
            
            // Initialize UI manager
            if (typeof UI !== 'undefined') {
                UI.init();
                Logger.debug('UI manager initialized');
            }
            
            // Initialize i18n
            if (typeof I18n !== 'undefined') {
                await I18n.init();
                Logger.debug('Internationalization initialized');
            }
            
        } catch (error) {
            Logger.error('Failed to initialize modules', {}, error);
            throw error;
        }
    }
    
    /**
     * Setup event listeners for UI interactions
     */
    setupEventListeners() {
        Logger.debug('Setting up event listeners');
        
        // Authentication button
        const authBtn = document.getElementById('google-auth-btn');
        if (authBtn) {
            authBtn.addEventListener('click', () => this.handleGoogleAuth());
        }
        
        // Tab navigation
        const tabBtns = document.querySelectorAll('.tab-btn');
        tabBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.currentTarget.dataset.tab;
                this.switchTab(tab);
            });
        });
        
        // Dimension selector
        const dimensionSelector = document.getElementById('dimension-selector');
        if (dimensionSelector) {
            dimensionSelector.addEventListener('change', (e) => {
                this.handleDimensionChange(e.target.value);
            });
        }
        
        // Date range selectors
        const pageDateRange = document.getElementById('page-date-range');
        const siteDateRange = document.getElementById('site-date-range');
        
        if (pageDateRange) {
            pageDateRange.addEventListener('change', (e) => {
                this.handleDateRangeChange('page', e.target.value);
            });
        }
        
        if (siteDateRange) {
            siteDateRange.addEventListener('change', (e) => {
                this.handleDateRangeChange('site', e.target.value);
            });
        }
        
        // Action buttons
        const refreshBtn = document.getElementById('refresh-data-btn');
        const chartBtn = document.getElementById('view-chart-btn');
        const settingsBtn = document.getElementById('settings-btn');
        const closeBtn = document.getElementById('close-btn');
        
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.handleRefreshData());
        }
        
        if (chartBtn) {
            chartBtn.addEventListener('click', () => this.handleViewChart());
        }
        
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => this.handleSettings());
        }
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => window.close());
        }
        
        // Help and upgrade buttons
        const helpBtn = document.getElementById('help-permissions-btn');
        const upgradeBtn = document.getElementById('upgrade-btn');
        const closeUpgradeBtn = document.getElementById('close-upgrade-btn');
        
        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.handleHelp());
        }
        
        if (upgradeBtn) {
            upgradeBtn.addEventListener('click', () => this.handleUpgrade());
        }
        
        if (closeUpgradeBtn) {
            closeUpgradeBtn.addEventListener('click', () => this.hideUpgradePrompt());
        }
        
        Logger.debug('Event listeners setup completed');
    }
    
    /**
     * Check if user is authenticated
     */
    async checkAuthentication() {
        try {
            Logger.debug('Checking authentication status');
            
            if (typeof Auth === 'undefined') {
                Logger.warn('Auth module not available');
                return false;
            }
            
            const isAuthenticated = await Auth.checkAuthStatus();
            Logger.debug('Authentication check result', { isAuthenticated });
            
            return isAuthenticated;
            
        } catch (error) {
            Logger.error('Authentication check failed', {}, error);
            return false;
        }
    }
    
    /**
     * Load user data from storage or API
     */
    async loadUserData() {
        try {
            Logger.debug('Loading user data');
            
            const userData = await State.getUserData();
            if (userData) {
                this.updateUsageInfo(userData.usage);
                Logger.debug('User data loaded', { userId: userData.userId });
            }
            
        } catch (error) {
            Logger.error('Failed to load user data', {}, error);
            throw error;
        }
    }
    
    /**
     * Check if a GA4 property is selected
     */
    async checkSelectedProperty() {
        try {
            const selectedProperty = await State.getSelectedProperty();
            return !!selectedProperty;
        } catch (error) {
            Logger.error('Failed to check selected property', {}, error);
            return false;
        }
    }
    
    /**
     * Show property selection interface
     */
    async showPropertySelection() {
        try {
            Logger.info('Showing property selection');
            
            this.showState('property-selection');
            
            // Load available properties
            if (typeof API !== 'undefined') {
                const properties = await API.getGA4Properties();
                this.renderPropertyList(properties);
            }
            
        } catch (error) {
            Logger.error('Failed to show property selection', {}, error);
            this.showError('Error al cargar propiedades', error.message);
        }
    }
    
    /**
     * Load current page data
     */
    async loadCurrentPageData() {
        try {
            Logger.debug('Loading current page data');
            
            // Get current tab URL
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs.length > 0) {
                const currentUrl = tabs[0].url;
                const pagePath = this.extractPagePath(currentUrl);
                
                // Update UI with current page
                this.updateCurrentPageDisplay(pagePath);
                
                // Store in global state
                window.currentTabUrl = currentUrl;
                State.setCurrentPage(pagePath);
                
                Logger.debug('Current page data loaded', { url: currentUrl, pagePath });
            }
            
        } catch (error) {
            Logger.error('Failed to load current page data', {}, error);
            // Don't throw - this is not critical for initialization
        }
    }
    
    /**
     * Show main interface
     */
    async showMainInterface() {
        try {
            Logger.debug('Showing main interface');
            
            this.showState('main');
            
            // Load initial data for current view
            await this.loadDataForCurrentView();
            
        } catch (error) {
            Logger.error('Failed to show main interface', {}, error);
            throw error;
        }
    }
    
    /**
     * Load data for the current view (page or site)
     */
    async loadDataForCurrentView() {
        try {
            if (this.currentView === 'page') {
                await this.loadPageAnalysisData();
            } else {
                await this.loadSiteOverviewData();
            }
        } catch (error) {
            Logger.error('Failed to load data for current view', { view: this.currentView }, error);
            // Show error in the current view instead of failing completely
            this.showViewError(error.message);
        }
    }
    
    /**
     * Load page analysis data
     */
    async loadPageAnalysisData() {
        try {
            Logger.debug('Loading page analysis data');
            
            if (typeof Features === 'undefined') {
                throw new Error('Features module not available');
            }
            
            const currentPage = State.getCurrentPage();
            const selectedDimension = State.getSelectedDimension();
            const dateRange = State.getPageDateRange();
            
            // Load page metrics
            const pageData = await Features.getPageAnalytics(currentPage, selectedDimension, dateRange);
            this.renderPageMetrics(pageData);
            
            // Load traffic sources
            const trafficData = await Features.getPageTrafficSources(currentPage, selectedDimension, dateRange);
            this.renderTrafficSources(trafficData);
            
            // Load device data
            const deviceData = await Features.getPageDeviceData(currentPage, selectedDimension, dateRange);
            this.renderDeviceData(deviceData);
            
            Logger.debug('Page analysis data loaded successfully');
            
        } catch (error) {
            Logger.error('Failed to load page analysis data', {}, error);
            throw error;
        }
    }
    
    /**
     * Load site overview data
     */
    async loadSiteOverviewData() {
        try {
            Logger.debug('Loading site overview data');
            
            if (typeof Features === 'undefined') {
                throw new Error('Features module not available');
            }
            
            const dateRange = State.getSiteDateRange();
            
            // Load site summary
            const summaryData = await Features.getSiteSummary(dateRange);
            this.renderSiteMetrics(summaryData);
            
            // Load top pages
            const topPagesData = await Features.getTopPages(dateRange);
            this.renderTopPages(topPagesData);
            
            Logger.debug('Site overview data loaded successfully');
            
        } catch (error) {
            Logger.error('Failed to load site overview data', {}, error);
            throw error;
        }
    }
    
    // Event Handlers
    
    async handleGoogleAuth() {
        try {
            Logger.info('Starting Google authentication');
            
            this.showState('loading');
            this.updateLoadingProgress(0, 'Conectando con Google...');
            
            if (typeof Auth === 'undefined') {
                throw new Error('Auth module not available');
            }
            
            const authResult = await Auth.initiateGoogleAuth();
            
            if (authResult.success) {
                Logger.info('Authentication successful');
                await this.init(); // Restart initialization
            } else {
                throw new Error(authResult.error || 'Authentication failed');
            }
            
        } catch (error) {
            Logger.error('Google authentication failed', {}, error);
            this.showError('Error de autenticación', error.message);
        }
    }
    
    switchTab(tab) {
        Logger.debug('Switching tab', { from: this.currentView, to: tab });
        
        this.currentView = tab;
        
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tab);
        });
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tab}-analysis` || content.id === `${tab}-overview`);
        });
        
        // Load data for new tab
        this.loadDataForCurrentView();
    }
    
    handleDimensionChange(dimension) {
        Logger.debug('Dimension changed', { dimension });
        
        State.setSelectedDimension(dimension);
        this.updateDimensionHelp(dimension);
        this.loadPageAnalysisData();
    }
    
    handleDateRangeChange(view, range) {
        Logger.debug('Date range changed', { view, range });
        
        if (view === 'page') {
            State.setPageDateRange(range);
            this.loadPageAnalysisData();
        } else {
            State.setSiteDateRange(range);
            this.loadSiteOverviewData();
        }
    }
    
    async handleRefreshData() {
        try {
            Logger.info('Refreshing data');
            
            // Clear relevant cache
            if (typeof Cache !== 'undefined') {
                await Cache.cleanup();
            }
            
            // Reload current view data
            await this.loadDataForCurrentView();
            
            // Show success feedback
            this.showToast('Datos actualizados', 'success');
            
        } catch (error) {
            Logger.error('Failed to refresh data', {}, error);
            this.showToast('Error al actualizar datos', 'error');
        }
    }
    
    handleViewChart() {
        Logger.debug('View chart requested');
        // TODO: Implement chart view
        this.showToast('Función de gráficos próximamente', 'info');
    }
    
    handleSettings() {
        Logger.debug('Settings requested');
        // TODO: Implement settings view
        this.showToast('Configuración próximamente', 'info');
    }
    
    handleHelp() {
        Logger.debug('Help requested');
        chrome.tabs.create({ url: CONFIG.URLS.PERMISSIONS_HELP });
    }
    
    handleUpgrade() {
        Logger.debug('Upgrade requested');
        chrome.tabs.create({ url: CONFIG.URLS.UPGRADE });
    }
    
    // UI Helper Methods
    
    showState(state) {
        Logger.debug('Showing state', { state });
        
        this.currentState = state;
        
        // Hide all state containers
        document.querySelectorAll('.state-container').forEach(container => {
            container.classList.add('hidden');
        });
        
        // Show target state
        const targetState = document.getElementById(`${state}-state`);
        if (targetState) {
            targetState.classList.remove('hidden');
        }
    }
    
    updateLoadingProgress(percentage, message) {
        const progressFill = document.getElementById('progress-fill');
        const loadingMessage = document.getElementById('loading-message');
        
        if (progressFill) {
            progressFill.style.width = `${percentage}%`;
        }
        
        if (loadingMessage) {
            loadingMessage.textContent = message;
        }
        
        // Update loading steps
        const steps = document.querySelectorAll('.loading-steps .step');
        steps.forEach((step, index) => {
            const stepPercentage = (index + 1) * (100 / steps.length);
            step.classList.toggle('active', percentage >= stepPercentage);
        });
    }
    
    showError(title, message) {
        Logger.debug('Showing error', { title, message });
        
        this.showState('error');
        
        const errorTitle = document.getElementById('error-title');
        const errorMessage = document.getElementById('error-message');
        
        if (errorTitle) errorTitle.textContent = title;
        if (errorMessage) errorMessage.textContent = message;
    }
    
    extractPagePath(url) {
        try {
            const urlObj = new URL(url);
            return urlObj.pathname;
        } catch (error) {
            Logger.warn('Failed to extract page path', { url }, error);
            return '/';
        }
    }
    
    updateCurrentPageDisplay(pagePath) {
        const currentPageElement = document.getElementById('current-page-path');
        if (currentPageElement) {
            currentPageElement.textContent = pagePath;
        }
    }
    
    updateUsageInfo(usage) {
        const usageCount = document.getElementById('usage-count');
        const usageLimit = document.getElementById('usage-limit');
        const usageProgress = document.getElementById('usage-progress');
        
        if (usageCount) usageCount.textContent = usage.usageCount || 0;
        if (usageLimit) usageLimit.textContent = usage.usageLimit || 50;
        
        if (usageProgress && usage.usageLimit) {
            const percentage = (usage.usageCount / usage.usageLimit) * 100;
            usageProgress.style.width = `${Math.min(percentage, 100)}%`;
        }
    }
    
    showToast(message, type = 'info') {
        // TODO: Implement toast notifications
        Logger.info('Toast message', { message, type });
    }
    
    // Placeholder render methods (to be implemented with actual data)
    renderPageMetrics(data) { Logger.debug('Rendering page metrics', data); }
    renderTrafficSources(data) { Logger.debug('Rendering traffic sources', data); }
    renderDeviceData(data) { Logger.debug('Rendering device data', data); }
    renderSiteMetrics(data) { Logger.debug('Rendering site metrics', data); }
    renderTopPages(data) { Logger.debug('Rendering top pages', data); }
    renderPropertyList(properties) { Logger.debug('Rendering property list', properties); }
    updateDimensionHelp(dimension) { Logger.debug('Updating dimension help', { dimension }); }
    showViewError(message) { Logger.debug('Showing view error', { message }); }
    hideUpgradePrompt() { Logger.debug('Hiding upgrade prompt'); }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    try {
        const popup = new PopupController();
        await popup.init();
    } catch (error) {
        Logger.error('Failed to initialize popup controller', {}, error);
        console.error('GA4 Insights initialization failed:', error);
    }
});
