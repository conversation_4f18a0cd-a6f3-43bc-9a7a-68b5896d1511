/**
 * ui.js - UI Management and DOM Manipulation for GA4 Insights Extension
 * 
 * Handles all UI updates, DOM manipulation, and visual feedback
 * for the extension interface.
 */

class UIManager {
    constructor() {
        this.isInitialized = false;
        this.currentToasts = [];
        this.animationDuration = CONFIG.UI.ANIMATION_DURATION;
        
        Logger.info('UIManager initialized');
    }
    
    /**
     * Initialize UI manager
     */
    init() {
        try {
            Logger.debug('Initializing UI manager');
            
            // Setup global UI event listeners
            this.setupGlobalEventListeners();
            
            // Initialize tooltips and other UI components
            this.initializeComponents();
            
            this.isInitialized = true;
            Logger.info('UI manager initialized successfully');
            
        } catch (error) {
            Logger.error('Failed to initialize UI manager', {}, error);
            throw error;
        }
    }
    
    /**
     * Setup global event listeners
     * @private
     */
    setupGlobalEventListeners() {
        // Handle escape key for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
        
        // Handle clicks outside modals
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target);
            }
        });
        
        Logger.debug('Global UI event listeners setup');
    }
    
    /**
     * Initialize UI components
     * @private
     */
    initializeComponents() {
        // Initialize any UI components that need setup
        this.initializeTooltips();
        this.initializeProgressBars();
        
        Logger.debug('UI components initialized');
    }
    
    /**
     * Render page metrics in the UI
     * @param {Object} data - Page metrics data
     */
    renderPageMetrics(data) {
        try {
            Logger.debug('Rendering page metrics', { metricsCount: data.metrics.length });
            
            const container = document.getElementById('page-metrics-grid');
            if (!container) {
                Logger.warn('Page metrics container not found');
                return;
            }
            
            // Clear existing content
            container.innerHTML = '';
            
            // Render each metric
            data.metrics.forEach(metric => {
                const metricCard = this.createMetricCard(metric);
                container.appendChild(metricCard);
            });
            
            // Add fade-in animation
            container.classList.add('fade-in');
            
            Logger.debug('Page metrics rendered successfully');
            
        } catch (error) {
            Logger.error('Failed to render page metrics', { data }, error);
            this.showError('Error al mostrar métricas de página');
        }
    }
    
    /**
     * Render traffic sources in the UI
     * @param {Object} data - Traffic sources data
     */
    renderTrafficSources(data) {
        try {
            Logger.debug('Rendering traffic sources', { sourcesCount: data.sources.length });
            
            const container = document.getElementById('page-traffic-list');
            if (!container) {
                Logger.warn('Traffic sources container not found');
                return;
            }
            
            container.innerHTML = '';
            
            if (data.sources.length === 0) {
                container.innerHTML = this.createNoDataMessage('No hay datos de fuentes de tráfico');
                return;
            }
            
            data.sources.forEach(source => {
                const sourceItem = this.createTrafficSourceItem(source);
                container.appendChild(sourceItem);
            });
            
            container.classList.add('fade-in');
            
            Logger.debug('Traffic sources rendered successfully');
            
        } catch (error) {
            Logger.error('Failed to render traffic sources', { data }, error);
            this.showError('Error al mostrar fuentes de tráfico');
        }
    }
    
    /**
     * Render device data in the UI
     * @param {Object} data - Device data
     */
    renderDeviceData(data) {
        try {
            Logger.debug('Rendering device data', { devicesCount: data.devices.length });
            
            const container = document.getElementById('page-devices-chart');
            if (!container) {
                Logger.warn('Device data container not found');
                return;
            }
            
            container.innerHTML = '';
            
            if (data.devices.length === 0) {
                container.innerHTML = this.createNoDataMessage('No hay datos de dispositivos');
                return;
            }
            
            data.devices.forEach(device => {
                const deviceItem = this.createDeviceItem(device);
                container.appendChild(deviceItem);
            });
            
            container.classList.add('fade-in');
            
            Logger.debug('Device data rendered successfully');
            
        } catch (error) {
            Logger.error('Failed to render device data', { data }, error);
            this.showError('Error al mostrar datos de dispositivos');
        }
    }
    
    /**
     * Render site metrics in the UI
     * @param {Object} data - Site metrics data
     */
    renderSiteMetrics(data) {
        try {
            Logger.debug('Rendering site metrics', { metricsCount: data.metrics.length });
            
            const container = document.getElementById('site-metrics-grid');
            if (!container) {
                Logger.warn('Site metrics container not found');
                return;
            }
            
            container.innerHTML = '';
            
            data.metrics.forEach(metric => {
                const metricCard = this.createMetricCard(metric);
                container.appendChild(metricCard);
            });
            
            container.classList.add('fade-in');
            
            Logger.debug('Site metrics rendered successfully');
            
        } catch (error) {
            Logger.error('Failed to render site metrics', { data }, error);
            this.showError('Error al mostrar métricas del sitio');
        }
    }
    
    /**
     * Render top pages in the UI
     * @param {Object} data - Top pages data
     */
    renderTopPages(data) {
        try {
            Logger.debug('Rendering top pages', { pagesCount: data.pages.length });
            
            const container = document.getElementById('top-pages-list');
            if (!container) {
                Logger.warn('Top pages container not found');
                return;
            }
            
            container.innerHTML = '';
            
            if (data.pages.length === 0) {
                container.innerHTML = this.createNoDataMessage('No hay datos de páginas');
                return;
            }
            
            data.pages.forEach(page => {
                const pageItem = this.createPageItem(page);
                container.appendChild(pageItem);
            });
            
            container.classList.add('fade-in');
            
            Logger.debug('Top pages rendered successfully');
            
        } catch (error) {
            Logger.error('Failed to render top pages', { data }, error);
            this.showError('Error al mostrar páginas principales');
        }
    }
    
    /**
     * Render property list for selection
     * @param {Array} properties - List of GA4 properties
     */
    renderPropertyList(properties) {
        try {
            Logger.debug('Rendering property list', { count: properties.length });
            
            const container = document.getElementById('properties-list');
            const countElement = document.getElementById('properties-count');
            
            if (!container) {
                Logger.warn('Properties list container not found');
                return;
            }
            
            // Update count
            if (countElement) {
                countElement.textContent = properties.length;
            }
            
            container.innerHTML = '';
            
            if (properties.length === 0) {
                container.innerHTML = this.createNoPropertiesMessage();
                return;
            }
            
            properties.forEach(property => {
                const propertyItem = this.createPropertyItem(property);
                container.appendChild(propertyItem);
            });
            
            container.classList.add('fade-in');
            
            Logger.debug('Property list rendered successfully');
            
        } catch (error) {
            Logger.error('Failed to render property list', { properties }, error);
            this.showError('Error al mostrar propiedades');
        }
    }
    
    /**
     * Create metric card element
     * @private
     */
    createMetricCard(metric) {
        const card = document.createElement('div');
        card.className = 'metric-card';
        
        const trendClass = `trend-${metric.change.trend}`;
        
        card.innerHTML = `
            <div class="metric-header">
                <span class="metric-icon">${metric.icon}</span>
                <span class="metric-name">${Utils.escapeHtml(metric.displayName)}</span>
            </div>
            <div class="metric-value">
                <span class="value">${Utils.escapeHtml(metric.formattedValue)}</span>
                <span class="trend ${trendClass}">
                    ${metric.change.icon} ${metric.change.value}
                </span>
            </div>
        `;
        
        return card;
    }
    
    /**
     * Create traffic source item element
     * @private
     */
    createTrafficSourceItem(source) {
        const item = document.createElement('div');
        item.className = 'traffic-item';
        
        item.innerHTML = `
            <div class="traffic-source">
                <span class="icon">${source.icon}</span>
                <span class="name">${Utils.escapeHtml(source.sourceMedium)}</span>
            </div>
            <div class="traffic-metrics">
                <div class="traffic-users">${source.users}</div>
                <div class="traffic-percentage">${source.percentage}%</div>
            </div>
        `;
        
        return item;
    }
    
    /**
     * Create device item element
     * @private
     */
    createDeviceItem(device) {
        const item = document.createElement('div');
        item.className = 'device-item';
        
        item.innerHTML = `
            <div class="device-info">
                <span class="icon">${device.icon}</span>
                <span class="name">${Utils.escapeHtml(device.displayName)}</span>
            </div>
            <div class="device-stats">
                <span class="device-count">${device.users}</span>
                <span class="device-percentage">${device.percentage}%</span>
                <div class="device-bar">
                    <div class="device-bar-fill ${device.category}" style="width: ${device.percentage}%"></div>
                </div>
            </div>
        `;
        
        return item;
    }
    
    /**
     * Create page item element
     * @private
     */
    createPageItem(page) {
        const item = document.createElement('div');
        item.className = 'page-item';
        
        item.innerHTML = `
            <div class="page-info">
                <div class="page-path" title="${Utils.escapeHtml(page.pagePath)}">
                    ${Utils.escapeHtml(page.pagePath)}
                </div>
                <div class="page-title" title="${Utils.escapeHtml(page.pageTitle)}">
                    ${Utils.escapeHtml(page.pageTitle)}
                </div>
            </div>
            <div class="page-metrics">
                <div class="page-views">${page.formattedPageViews}</div>
                <div class="page-engagement">${page.formattedEngagementRate}</div>
            </div>
        `;
        
        return item;
    }
    
    /**
     * Create property item element
     * @private
     */
    createPropertyItem(property) {
        const item = document.createElement('div');
        item.className = 'property-item';
        item.setAttribute('data-property-id', property.propertyId);
        
        item.innerHTML = `
            <div class="property-header">
                <h4>${Utils.escapeHtml(property.propertyName)}</h4>
                <span class="property-id">${Utils.escapeHtml(property.propertyId)}</span>
            </div>
            <div class="property-details">
                <span>📁 ${Utils.escapeHtml(property.accountName)}</span>
                ${property.websiteUrl ? `<span>🌐 ${Utils.escapeHtml(property.websiteUrl)}</span>` : ''}
            </div>
            <div class="property-status">
                <span class="status-badge status-available">📊 Datos disponibles</span>
            </div>
        `;
        
        // Add click handler
        item.addEventListener('click', () => {
            this.selectProperty(property);
        });
        
        return item;
    }
    
    /**
     * Create no data message
     * @private
     */
    createNoDataMessage(message) {
        return `
            <div class="no-data">
                <div class="icon">📊</div>
                <h3>Sin datos</h3>
                <p>${Utils.escapeHtml(message)}</p>
            </div>
        `;
    }
    
    /**
     * Create no properties message
     * @private
     */
    createNoPropertiesMessage() {
        return `
            <div class="no-data">
                <div class="icon">📊</div>
                <h3>No se encontraron propiedades GA4</h3>
                <p>No tienes acceso a ninguna propiedad de Google Analytics 4</p>
                <button onclick="window.open('${CONFIG.URLS.GA4_SETUP_GUIDE}')" class="btn btn-primary">
                    📚 Cómo crear una propiedad GA4
                </button>
            </div>
        `;
    }
    
    /**
     * Handle property selection
     * @private
     */
    async selectProperty(property) {
        try {
            Logger.info('Property selected', { propertyId: property.propertyId });
            
            // Update visual selection
            document.querySelectorAll('.property-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            const selectedItem = document.querySelector(`[data-property-id="${property.propertyId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
            
            // Save to state
            await State.setSelectedProperty(property);
            
            // Show success and continue
            this.showToast('Propiedad seleccionada correctamente', 'success');
            
            // Trigger continuation of initialization
            setTimeout(() => {
                if (window.popupController) {
                    window.popupController.init();
                }
            }, 1000);
            
        } catch (error) {
            Logger.error('Failed to select property', { property }, error);
            this.showError('Error al seleccionar propiedad');
        }
    }
    
    /**
     * Show toast notification
     * @param {string} message - Toast message
     * @param {string} type - Toast type (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showToast(message, type = 'info', duration = CONFIG.UI.TOAST_DURATION) {
        try {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            
            // Add to DOM
            document.body.appendChild(toast);
            this.currentToasts.push(toast);
            
            // Show with animation
            setTimeout(() => toast.classList.add('show'), 10);
            
            // Auto remove
            setTimeout(() => {
                this.removeToast(toast);
            }, duration);
            
            Logger.debug('Toast shown', { message, type, duration });
            
        } catch (error) {
            Logger.error('Failed to show toast', { message, type }, error);
        }
    }
    
    /**
     * Remove toast notification
     * @private
     */
    removeToast(toast) {
        try {
            toast.classList.remove('show');
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                
                const index = this.currentToasts.indexOf(toast);
                if (index > -1) {
                    this.currentToasts.splice(index, 1);
                }
            }, this.animationDuration);
            
        } catch (error) {
            Logger.error('Failed to remove toast', {}, error);
        }
    }
    
    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        this.showToast(message, 'error');
        Logger.warn('UI error shown', { message });
    }
    
    /**
     * Show success message
     * @param {string} message - Success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
        Logger.debug('UI success shown', { message });
    }
    
    /**
     * Show upgrade prompt modal
     * @param {Object} options - Upgrade prompt options
     */
    showUpgradePrompt(options = {}) {
        try {
            const modal = document.getElementById('upgrade-prompt');
            const messageElement = document.getElementById('upgrade-message');
            
            if (!modal) {
                Logger.warn('Upgrade prompt modal not found');
                return;
            }
            
            if (messageElement && options.message) {
                messageElement.textContent = options.message;
            }
            
            modal.classList.remove('hidden');
            modal.classList.add('show');
            
            Logger.debug('Upgrade prompt shown', options);
            
        } catch (error) {
            Logger.error('Failed to show upgrade prompt', { options }, error);
        }
    }
    
    /**
     * Hide upgrade prompt modal
     */
    hideUpgradePrompt() {
        try {
            const modal = document.getElementById('upgrade-prompt');
            if (modal) {
                modal.classList.add('hidden');
                modal.classList.remove('show');
            }
            
            Logger.debug('Upgrade prompt hidden');
            
        } catch (error) {
            Logger.error('Failed to hide upgrade prompt', {}, error);
        }
    }
    
    /**
     * Close all modals
     */
    closeAllModals() {
        try {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.classList.add('hidden');
                modal.classList.remove('show');
            });
            
            Logger.debug('All modals closed');
            
        } catch (error) {
            Logger.error('Failed to close modals', {}, error);
        }
    }
    
    /**
     * Close specific modal
     * @param {Element} modal - Modal element
     */
    closeModal(modal) {
        try {
            modal.classList.add('hidden');
            modal.classList.remove('show');
            
            Logger.debug('Modal closed');
            
        } catch (error) {
            Logger.error('Failed to close modal', {}, error);
        }
    }
    
    /**
     * Update dimension help text
     * @param {string} dimension - Selected dimension
     */
    updateDimensionHelp(dimension) {
        try {
            const helpElement = document.getElementById('dimension-help');
            if (!helpElement) return;
            
            const dimensionConfig = CONFIG.PAGE_DIMENSIONS[dimension];
            if (dimensionConfig) {
                helpElement.textContent = dimensionConfig.helpText;
            }
            
            Logger.debug('Dimension help updated', { dimension });
            
        } catch (error) {
            Logger.error('Failed to update dimension help', { dimension }, error);
        }
    }
    
    /**
     * Initialize tooltips
     * @private
     */
    initializeTooltips() {
        // Simple tooltip implementation
        const tooltipElements = document.querySelectorAll('[title]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }
    
    /**
     * Initialize progress bars
     * @private
     */
    initializeProgressBars() {
        // Initialize any progress bars that need animation
        const progressBars = document.querySelectorAll('.progress-fill, .usage-progress');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    }
    
    /**
     * Show tooltip (placeholder implementation)
     * @private
     */
    showTooltip(event) {
        // Simple tooltip implementation
        const title = event.target.getAttribute('title');
        if (title) {
            event.target.setAttribute('data-original-title', title);
            event.target.removeAttribute('title');
            // Could implement custom tooltip here
        }
    }
    
    /**
     * Hide tooltip (placeholder implementation)
     * @private
     */
    hideTooltip(event) {
        const originalTitle = event.target.getAttribute('data-original-title');
        if (originalTitle) {
            event.target.setAttribute('title', originalTitle);
            event.target.removeAttribute('data-original-title');
        }
    }
}

// Create global UI instance
const UI = new UIManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UI;
}
