/**
 * api.js - API Communication Module for GA4 Insights Extension
 * 
 * Handles communication with Google Analytics 4 APIs and the backend server.
 * Includes error handling, retry logic, and response processing.
 */

class APIManager {
    constructor() {
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.rateLimitDelay = CONFIG.API.RATE_LIMIT_DELAY;
        
        Logger.info('APIManager initialized');
    }
    
    /**
     * Get GA4 account summaries (properties list)
     * @returns {Array} List of GA4 properties
     */
    async getGA4Properties() {
        try {
            Logger.info('Fetching GA4 properties');
            
            const authToken = Auth.getAuthToken();
            if (!authToken) {
                throw new Error('No authentication token available');
            }
            
            // Check cache first
            const cacheKey = Cache.generateCacheKey(
                { action: 'account_summaries' },
                'all_properties',
                'account_summaries'
            );
            
            const cachedData = await Cache.get(cacheKey);
            if (cachedData) {
                Logger.debug('GA4 properties loaded from cache');
                return cachedData;
            }
            
            // Get Google access token
            const googleToken = await this.getGoogleAccessToken();
            if (!googleToken) {
                throw new Error('Failed to get Google access token');
            }
            
            // Fetch from GA4 Admin API
            const response = await this.makeRequest(
                `${CONFIG.GA4_ADMIN_API_BASE_URL}/accountSummaries`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${googleToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            
            if (!response.ok) {
                throw new Error(`GA4 Admin API error: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            const properties = this.processAccountSummaries(data.accountSummaries || []);
            
            // Cache the results
            await Cache.set(
                cacheKey,
                properties,
                CONFIG.CACHE.TTL.ACCOUNT_SUMMARIES,
                { type: 'account_summaries' }
            );
            
            Logger.info('GA4 properties fetched successfully', { 
                count: properties.length 
            });
            
            return properties;
            
        } catch (error) {
            Logger.error('Failed to fetch GA4 properties', {}, error);
            throw this.handleAPIError(error, 'getGA4Properties');
        }
    }
    
    /**
     * Run GA4 Data API report
     * @param {Object} reportRequest - GA4 report request
     * @returns {Object} Processed report data
     */
    async runGA4Report(reportRequest) {
        try {
            Logger.debug('Running GA4 report', { 
                propertyId: reportRequest.property,
                metrics: reportRequest.metrics?.length,
                dimensions: reportRequest.dimensions?.length
            });
            
            // Check usage limits
            await this.checkUsageLimits();
            
            // Generate cache key
            const cacheKey = Cache.generateCacheKey(
                reportRequest,
                reportRequest.property,
                'ga4_report'
            );
            
            // Check cache first
            const cachedData = await Cache.get(cacheKey);
            if (cachedData) {
                Logger.debug('GA4 report loaded from cache');
                return cachedData;
            }
            
            // Get Google access token
            const googleToken = await this.getGoogleAccessToken();
            if (!googleToken) {
                throw new Error('Failed to get Google access token');
            }
            
            // Make API request
            const response = await this.makeRequest(
                `${CONFIG.GA4_DATA_API_BASE_URL}/${reportRequest.property}:runReport`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${googleToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(reportRequest)
                }
            );
            
            if (!response.ok) {
                throw new Error(`GA4 Data API error: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            const processedData = this.processGA4ReportData(data);
            
            // Determine cache TTL based on data recency
            const isRecentData = this.isRecentData(reportRequest.dateRanges);
            const cacheTTL = isRecentData ? 
                CONFIG.CACHE.TTL.RECENT_DATA : 
                CONFIG.CACHE.TTL.HISTORICAL_DATA;
            
            // Cache the results
            await Cache.set(
                cacheKey,
                processedData,
                cacheTTL,
                { 
                    type: 'ga4_report',
                    propertyId: reportRequest.property,
                    queryHash: Cache.simpleHash(JSON.stringify(reportRequest))
                }
            );
            
            // Confirm usage with backend
            await this.confirmUsage('ga4_api_call', {
                propertyId: reportRequest.property,
                reportType: 'data_api',
                metricsCount: reportRequest.metrics?.length || 0,
                dimensionsCount: reportRequest.dimensions?.length || 0
            });
            
            Logger.info('GA4 report completed successfully', {
                propertyId: reportRequest.property,
                rowCount: processedData.rows?.length || 0
            });
            
            return processedData;
            
        } catch (error) {
            Logger.error('Failed to run GA4 report', { reportRequest }, error);
            throw this.handleAPIError(error, 'runGA4Report');
        }
    }
    
    /**
     * Check usage limits with backend
     * @private
     */
    async checkUsageLimits() {
        try {
            const authToken = Auth.getAuthToken();
            if (!authToken) {
                return; // Skip check if not authenticated
            }
            
            const response = await this.makeRequest(
                `${CONFIG.BACKEND_URL}/usage/status`,
                {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            
            if (!response.ok) {
                Logger.warn('Failed to check usage limits', { status: response.status });
                return; // Don't block on usage check failure
            }
            
            const data = await response.json();
            
            if (!data.success) {
                Logger.warn('Usage check returned error', { error: data.error });
                return;
            }
            
            // Update state with current usage
            if (State && data.data.usage) {
                const currentUser = State.getUserData();
                if (currentUser) {
                    currentUser.usage = data.data.usage;
                    State.setUser(currentUser);
                }
            }
            
            Logger.debug('Usage limits checked', { 
                usage: data.data.usage 
            });
            
        } catch (error) {
            Logger.error('Usage limit check failed', {}, error);
            // Don't throw - this shouldn't block API calls
        }
    }
    
    /**
     * Confirm API usage with backend
     * @private
     */
    async confirmUsage(action, metadata = {}) {
        try {
            const authToken = Auth.getAuthToken();
            if (!authToken) {
                return; // Skip if not authenticated
            }
            
            const response = await this.makeRequest(
                `${CONFIG.BACKEND_URL}/usage/confirm`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action,
                        metadata: {
                            ...metadata,
                            timestamp: Date.now(),
                            userAgent: navigator.userAgent,
                            extensionVersion: CONFIG.VERSION
                        }
                    })
                }
            );
            
            if (!response.ok) {
                Logger.warn('Failed to confirm usage', { 
                    status: response.status,
                    action 
                });
                return;
            }
            
            const data = await response.json();
            
            if (data.success && data.data.usage) {
                // Update usage in state
                const currentUser = State.getUserData();
                if (currentUser) {
                    currentUser.usage = data.data.usage;
                    State.setUser(currentUser);
                }
                
                Logger.debug('Usage confirmed', { 
                    action,
                    remaining: data.data.usage.remaining 
                });
            }
            
        } catch (error) {
            Logger.error('Failed to confirm usage', { action }, error);
            // Don't throw - this shouldn't block functionality
        }
    }
    
    /**
     * Get current Google access token
     * @private
     */
    async getGoogleAccessToken() {
        return new Promise((resolve) => {
            chrome.identity.getAuthToken({
                interactive: false
            }, (token) => {
                if (chrome.runtime.lastError || !token) {
                    Logger.warn('Failed to get Google access token', {
                        error: chrome.runtime.lastError?.message
                    });
                    resolve(null);
                } else {
                    resolve(token);
                }
            });
        });
    }
    
    /**
     * Make HTTP request with retry logic
     * @private
     */
    async makeRequest(url, options, retryCount = 0) {
        try {
            // Add to request queue for rate limiting
            await this.addToRequestQueue();
            
            const response = await fetch(url, {
                ...options,
                timeout: CONFIG.API.TIMEOUT
            });
            
            // Handle rate limiting
            if (response.status === 429) {
                if (retryCount < CONFIG.API.RETRY_ATTEMPTS) {
                    const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
                    Logger.warn('Rate limited, retrying', { 
                        url, 
                        retryCount, 
                        delay 
                    });
                    await this.sleep(delay);
                    return this.makeRequest(url, options, retryCount + 1);
                } else {
                    throw new Error('Rate limit exceeded, max retries reached');
                }
            }
            
            return response;
            
        } catch (error) {
            if (retryCount < CONFIG.API.RETRY_ATTEMPTS && this.isRetryableError(error)) {
                const delay = Math.pow(2, retryCount) * CONFIG.API.RETRY_DELAY;
                Logger.warn('Request failed, retrying', { 
                    url, 
                    error: error.message, 
                    retryCount, 
                    delay 
                });
                await this.sleep(delay);
                return this.makeRequest(url, options, retryCount + 1);
            }
            
            throw error;
        }
    }
    
    /**
     * Add request to queue for rate limiting
     * @private
     */
    async addToRequestQueue() {
        return new Promise((resolve) => {
            this.requestQueue.push(resolve);
            this.processRequestQueue();
        });
    }
    
    /**
     * Process request queue with rate limiting
     * @private
     */
    async processRequestQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.requestQueue.length > 0) {
            const resolve = this.requestQueue.shift();
            resolve();
            
            if (this.requestQueue.length > 0) {
                await this.sleep(this.rateLimitDelay);
            }
        }
        
        this.isProcessingQueue = false;
    }
    
    /**
     * Process GA4 account summaries
     * @private
     */
    processAccountSummaries(accountSummaries) {
        const ga4Properties = [];
        
        accountSummaries.forEach(account => {
            if (account.propertySummaries) {
                account.propertySummaries.forEach(property => {
                    // Only GA4 properties (not Universal Analytics)
                    if (property.propertyType === 'PROPERTY_TYPE_ORDINARY') {
                        ga4Properties.push({
                            accountId: account.account.split('/')[1],
                            accountName: account.displayName,
                            propertyId: property.property,
                            propertyName: property.displayName,
                            websiteUrl: property.websiteUrl || null,
                            createTime: property.createTime || null,
                            timeZone: property.timeZone || null,
                            currencyCode: property.currencyCode || null
                        });
                    }
                });
            }
        });
        
        Logger.debug('Account summaries processed', { 
            totalAccounts: accountSummaries.length,
            ga4Properties: ga4Properties.length 
        });
        
        return ga4Properties;
    }
    
    /**
     * Process GA4 report data
     * @private
     */
    processGA4ReportData(apiResponse) {
        const processed = {
            dimensionHeaders: apiResponse.dimensionHeaders || [],
            metricHeaders: apiResponse.metricHeaders || [],
            rows: [],
            totals: apiResponse.totals || [],
            maximums: apiResponse.maximums || [],
            minimums: apiResponse.minimums || [],
            rowCount: apiResponse.rowCount || 0,
            metadata: apiResponse.metadata || {}
        };
        
        // Process rows
        if (apiResponse.rows) {
            processed.rows = apiResponse.rows.map(row => ({
                dimensionValues: row.dimensionValues?.map(dv => dv.value) || [],
                metricValues: row.metricValues?.map(mv => ({
                    value: mv.value,
                    oneValue: mv.oneValue
                })) || []
            }));
        }
        
        Logger.debug('GA4 report data processed', {
            rowCount: processed.rowCount,
            dimensionCount: processed.dimensionHeaders.length,
            metricCount: processed.metricHeaders.length
        });
        
        return processed;
    }
    
    /**
     * Check if data is recent (affects cache TTL)
     * @private
     */
    isRecentData(dateRanges) {
        if (!dateRanges || dateRanges.length === 0) {
            return true;
        }
        
        const endDate = dateRanges[0].endDate;
        return endDate === 'today' || endDate === 'yesterday';
    }
    
    /**
     * Check if error is retryable
     * @private
     */
    isRetryableError(error) {
        const retryableErrors = [
            'network error',
            'timeout',
            'connection',
            'temporary',
            'service unavailable'
        ];
        
        const errorMessage = error.message.toLowerCase();
        return retryableErrors.some(retryable => errorMessage.includes(retryable));
    }
    
    /**
     * Handle API errors and convert to user-friendly messages
     * @private
     */
    handleAPIError(error, context) {
        Logger.error('API error occurred', { context, error: error.message }, error);
        
        const message = error.message.toLowerCase();
        
        if (message.includes('403') || message.includes('forbidden')) {
            return new Error(CONFIG.ERRORS.PROPERTY_ACCESS_DENIED);
        }
        
        if (message.includes('429') || message.includes('rate limit')) {
            return new Error(CONFIG.ERRORS.API_LIMIT_EXCEEDED);
        }
        
        if (message.includes('network') || message.includes('fetch')) {
            return new Error(CONFIG.ERRORS.NETWORK_ERROR);
        }
        
        if (message.includes('ga4') || message.includes('analytics')) {
            return new Error(CONFIG.ERRORS.GA4_API_ERROR);
        }
        
        return new Error(CONFIG.ERRORS.UNKNOWN_ERROR);
    }
    
    /**
     * Sleep utility for delays
     * @private
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Create global API instance
const API = new APIManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = API;
}
