/**
 * i18n.js - Internationalization Module for GA4 Insights Extension
 * 
 * Handles text translations and localization for the extension.
 * Currently supports Spanish (default) with framework for additional languages.
 */

class I18nManager {
    constructor() {
        this.currentLanguage = 'es';
        this.fallbackLanguage = 'es';
        this.translations = {};
        this.isInitialized = false;
        
        Logger.info('I18nManager initialized');
    }
    
    /**
     * Initialize i18n system
     */
    async init() {
        try {
            Logger.debug('Initializing i18n system');
            
            // Load user's preferred language
            await this.loadUserLanguage();
            
            // Load translations
            await this.loadTranslations();
            
            // Apply translations to DOM
            this.applyTranslations();
            
            this.isInitialized = true;
            Logger.info('I18n system initialized', { language: this.currentLanguage });
            
        } catch (error) {
            Logger.error('Failed to initialize i18n system', {}, error);
            // Continue with default language
            this.isInitialized = true;
        }
    }
    
    /**
     * Load user's preferred language from storage
     * @private
     */
    async loadUserLanguage() {
        try {
            const preferences = await chrome.storage.local.get('ga4_user_preferences');
            const userPrefs = preferences.ga4_user_preferences;
            
            if (userPrefs && userPrefs.language) {
                this.currentLanguage = userPrefs.language;
                Logger.debug('User language loaded', { language: this.currentLanguage });
            } else {
                // Detect browser language
                const browserLang = navigator.language.split('-')[0];
                if (this.getSupportedLanguages().includes(browserLang)) {
                    this.currentLanguage = browserLang;
                    Logger.debug('Browser language detected', { language: browserLang });
                }
            }
            
        } catch (error) {
            Logger.warn('Failed to load user language, using default', {}, error);
        }
    }
    
    /**
     * Load translations for current language
     * @private
     */
    async loadTranslations() {
        try {
            this.translations = this.getTranslations(this.currentLanguage);
            Logger.debug('Translations loaded', { 
                language: this.currentLanguage,
                keyCount: Object.keys(this.translations).length 
            });
            
        } catch (error) {
            Logger.error('Failed to load translations', { language: this.currentLanguage }, error);
            // Fallback to default language
            this.translations = this.getTranslations(this.fallbackLanguage);
        }
    }
    
    /**
     * Get translations for a specific language
     * @private
     */
    getTranslations(language) {
        const translations = {
            es: {
                // Extension name and basic info
                extension_name: 'GA4 Insights',
                
                // Loading states
                loading_title: 'Cargando...',
                loading_connecting: 'Conectando con Google Analytics',
                loading_verifying: 'Verificando credenciales...',
                loading_properties: 'Obteniendo propiedades GA4...',
                loading_data: 'Cargando datos...',
                
                // Authentication
                welcome_title: '¡Bienvenido a GA4 Insights!',
                welcome_description: 'Analiza el rendimiento de tu sitio web directamente desde cualquier página',
                connect_google: 'Conectar con Google Analytics',
                secure_connection: 'Conexión segura OAuth 2.0',
                readonly_access: 'Solo lectura de Analytics',
                no_password: 'No almacenamos tu contraseña',
                what_permissions: '¿Qué permisos necesitamos?',
                
                // Property selection
                select_property: 'Selecciona tu propiedad GA4',
                properties_found: 'Hemos encontrado {count} propiedades con acceso de lectura:',
                refresh_list: 'Actualizar lista',
                
                // Main interface
                this_page: 'Esta Página',
                full_site: 'Sitio Completo',
                
                // Page analysis
                analyze_as: 'Analizar como:',
                page_path: 'Ruta de Página',
                landing_page: 'Página de Destino',
                exit_page: 'Página de Salida',
                page_with_params: 'Página + Parámetros',
                period: 'Período:',
                yesterday: 'Ayer',
                last_7_days: 'Últimos 7 días',
                last_30_days: 'Últimos 30 días',
                last_28_days: 'Últimos 28 días',
                last_90_days: 'Últimos 90 días',
                
                // Metrics
                key_metrics: 'MÉTRICAS CLAVE',
                general_summary: 'RESUMEN GENERAL',
                traffic_sources: 'FUENTES DE TRÁFICO',
                devices: 'DISPOSITIVOS',
                top_pages: 'TOP PÁGINAS',
                
                // Metric names
                active_users: 'Usuarios Activos',
                new_users: 'Usuarios Nuevos',
                sessions: 'Sesiones',
                page_views: 'Vistas de Página',
                engagement_rate: 'Tasa de Interacción',
                bounce_rate: 'Tasa de Rebote',
                avg_session_duration: 'Duración Promedio',
                conversions: 'Conversiones',
                revenue: 'Ingresos',
                
                // Actions
                view_chart: 'Ver gráfico',
                refresh: 'Actualizar',
                export: 'Exportar',
                settings: 'Configuración',
                
                // Usage
                usage: 'Uso:',
                queries_this_month: 'consultas este mes',
                
                // Errors
                error_title: 'Error',
                error_occurred: 'Ha ocurrido un error',
                retry: 'Reintentar',
                get_help: 'Obtener ayuda',
                
                // Upgrade
                limit_reached: 'Límite Alcanzado',
                upgrade_message: 'Has alcanzado el límite de tu plan gratuito',
                unlimited_queries: 'Consultas ilimitadas',
                period_comparison: 'Comparación de períodos',
                data_export: 'Exportación de datos',
                advanced_filters: 'Filtros avanzados',
                upgrade_premium: 'Actualizar a Premium - $9.99',
                
                // Device types
                mobile: 'Móvil',
                desktop: 'Escritorio',
                tablet: 'Tablet',
                
                // Traffic sources
                organic: 'Búsqueda orgánica',
                direct: 'Directo',
                referral: 'Referencia',
                social: 'Redes sociales',
                email: 'Email',
                paid: 'Publicidad pagada',
                
                // Time periods
                today: 'Hoy',
                this_week: 'Esta semana',
                this_month: 'Este mes',
                
                // Status messages
                no_data: 'Sin datos disponibles',
                loading_failed: 'Error al cargar datos',
                cache_cleared: 'Cache limpiado',
                data_refreshed: 'Datos actualizados',
                
                // Help text
                page_path_help: 'Muestra datos para usuarios que visitaron exactamente esta URL',
                landing_page_help: 'Muestra datos para sesiones que empezaron en esta página',
                exit_page_help: 'Muestra datos para sesiones que terminaron en esta página',
                page_params_help: 'Incluye parámetros de consulta en el análisis (ej: ?utm_source=google)'
            },
            
            en: {
                // Basic English translations (can be expanded)
                extension_name: 'GA4 Insights',
                loading_title: 'Loading...',
                welcome_title: 'Welcome to GA4 Insights!',
                connect_google: 'Connect with Google Analytics',
                this_page: 'This Page',
                full_site: 'Full Site',
                active_users: 'Active Users',
                page_views: 'Page Views',
                refresh: 'Refresh',
                settings: 'Settings'
            }
        };
        
        return translations[language] || translations[this.fallbackLanguage];
    }
    
    /**
     * Get supported languages
     * @returns {Array} Array of supported language codes
     */
    getSupportedLanguages() {
        return ['es', 'en'];
    }
    
    /**
     * Get translated text
     * @param {string} key - Translation key
     * @param {Object} params - Parameters for interpolation
     * @returns {string} Translated text
     */
    t(key, params = {}) {
        if (!this.isInitialized) {
            Logger.warn('I18n not initialized, returning key', { key });
            return key;
        }
        
        let translation = this.translations[key];
        
        if (!translation) {
            Logger.warn('Translation not found', { key, language: this.currentLanguage });
            return key;
        }
        
        // Handle parameter interpolation
        if (Object.keys(params).length > 0) {
            translation = this.interpolate(translation, params);
        }
        
        return translation;
    }
    
    /**
     * Interpolate parameters in translation string
     * @private
     */
    interpolate(text, params) {
        return text.replace(/\{(\w+)\}/g, (match, key) => {
            return params[key] !== undefined ? params[key] : match;
        });
    }
    
    /**
     * Apply translations to DOM elements with data-i18n attribute
     */
    applyTranslations() {
        try {
            const elements = document.querySelectorAll('[data-i18n]');
            
            elements.forEach(element => {
                const key = element.getAttribute('data-i18n');
                const translation = this.t(key);
                
                if (translation !== key) {
                    // Check if element has specific attribute to translate
                    const attr = element.getAttribute('data-i18n-attr');
                    if (attr) {
                        element.setAttribute(attr, translation);
                    } else {
                        element.textContent = translation;
                    }
                }
            });
            
            Logger.debug('DOM translations applied', { 
                elementCount: elements.length 
            });
            
        } catch (error) {
            Logger.error('Failed to apply DOM translations', {}, error);
        }
    }
    
    /**
     * Change language
     * @param {string} language - Language code
     */
    async changeLanguage(language) {
        try {
            if (!this.getSupportedLanguages().includes(language)) {
                throw new Error(`Unsupported language: ${language}`);
            }
            
            Logger.info('Changing language', { 
                from: this.currentLanguage, 
                to: language 
            });
            
            this.currentLanguage = language;
            
            // Reload translations
            await this.loadTranslations();
            
            // Reapply to DOM
            this.applyTranslations();
            
            // Save preference
            await this.saveLanguagePreference(language);
            
            Logger.info('Language changed successfully', { language });
            
        } catch (error) {
            Logger.error('Failed to change language', { language }, error);
            throw error;
        }
    }
    
    /**
     * Save language preference
     * @private
     */
    async saveLanguagePreference(language) {
        try {
            const preferences = await chrome.storage.local.get('ga4_user_preferences');
            const userPrefs = preferences.ga4_user_preferences || {};
            
            userPrefs.language = language;
            
            await chrome.storage.local.set({
                'ga4_user_preferences': userPrefs,
                'ga4_user_preferences_expiry': Date.now() + (30 * 24 * 60 * 60 * 1000) // 30 days
            });
            
            Logger.debug('Language preference saved', { language });
            
        } catch (error) {
            Logger.error('Failed to save language preference', { language }, error);
        }
    }
    
    /**
     * Get current language
     * @returns {string} Current language code
     */
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    /**
     * Format number according to current locale
     * @param {number} number - Number to format
     * @param {Object} options - Formatting options
     * @returns {string} Formatted number
     */
    formatNumber(number, options = {}) {
        const locale = this.currentLanguage === 'es' ? 'es-ES' : 'en-US';
        return new Intl.NumberFormat(locale, options).format(number);
    }
    
    /**
     * Format date according to current locale
     * @param {Date} date - Date to format
     * @param {Object} options - Formatting options
     * @returns {string} Formatted date
     */
    formatDate(date, options = {}) {
        const locale = this.currentLanguage === 'es' ? 'es-ES' : 'en-US';
        return new Intl.DateTimeFormat(locale, options).format(date);
    }
    
    /**
     * Get localized metric name
     * @param {string} metricName - GA4 metric name
     * @returns {string} Localized metric name
     */
    getMetricName(metricName) {
        const metricKeys = {
            'activeUsers': 'active_users',
            'newUsers': 'new_users',
            'sessions': 'sessions',
            'screenPageViews': 'page_views',
            'engagementRate': 'engagement_rate',
            'bounceRate': 'bounce_rate',
            'averageSessionDuration': 'avg_session_duration',
            'conversions': 'conversions',
            'totalRevenue': 'revenue'
        };
        
        const key = metricKeys[metricName];
        return key ? this.t(key) : metricName;
    }
}

// Create global I18n instance
const I18n = new I18nManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = I18n;
}
