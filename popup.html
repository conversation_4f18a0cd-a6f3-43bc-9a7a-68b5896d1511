<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GA4 Insights</title>
    <link rel="stylesheet" href="css/popup.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <div id="app" class="popup-container">
        <!-- Header -->
        <header class="popup-header">
            <div class="header-content">
                <div class="logo-section">
                    <img src="icons/icon32.png" alt="GA4 Insights" class="logo">
                    <h1 class="app-title" data-i18n="extension_name">GA4 Insights</h1>
                </div>
                <div class="header-actions">
                    <button id="settings-btn" class="icon-btn" title="Configuración">
                        <span class="icon">⚙️</span>
                    </button>
                    <button id="close-btn" class="icon-btn" title="Cerrar">
                        <span class="icon">❌</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Loading State -->
        <div id="loading-state" class="state-container hidden">
            <div class="loading-content">
                <div class="spinner"></div>
                <h3 data-i18n="loading_title">Cargando...</h3>
                <p id="loading-message" data-i18n="loading_connecting">Conectando con Google Analytics</p>
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
                <div id="loading-steps" class="loading-steps">
                    <div class="step" data-step="auth">• Verificando credenciales...</div>
                    <div class="step" data-step="properties">• Obteniendo propiedades GA4...</div>
                    <div class="step" data-step="data">• Cargando datos...</div>
                </div>
            </div>
        </div>

        <!-- Authentication State -->
        <div id="auth-state" class="state-container hidden">
            <div class="auth-content">
                <div class="auth-icon">📊</div>
                <h2 data-i18n="welcome_title">¡Bienvenido a GA4 Insights!</h2>
                <p data-i18n="welcome_description">
                    Analiza el rendimiento de tu sitio web directamente desde cualquier página
                </p>
                
                <button id="google-auth-btn" class="btn btn-primary btn-large">
                    <span class="btn-icon">🔵</span>
                    <span data-i18n="connect_google">Conectar con Google Analytics</span>
                </button>
                
                <div class="auth-security">
                    <div class="security-item">
                        <span class="icon">🔒</span>
                        <span data-i18n="secure_connection">Conexión segura OAuth 2.0</span>
                    </div>
                    <div class="security-item">
                        <span class="icon">✅</span>
                        <span data-i18n="readonly_access">Solo lectura de Analytics</span>
                    </div>
                    <div class="security-item">
                        <span class="icon">✅</span>
                        <span data-i18n="no_password">No almacenamos tu contraseña</span>
                    </div>
                </div>
                
                <button id="help-permissions-btn" class="btn btn-link">
                    <span data-i18n="what_permissions">¿Qué permisos necesitamos?</span>
                </button>
            </div>
        </div>

        <!-- Property Selection State -->
        <div id="property-selection-state" class="state-container hidden">
            <div class="property-selection-content">
                <h2 data-i18n="select_property">Selecciona tu propiedad GA4</h2>
                <p id="properties-found-text">Hemos encontrado <span id="properties-count">0</span> propiedades con acceso de lectura:</p>
                
                <div id="properties-list" class="properties-list">
                    <!-- Properties will be populated dynamically -->
                </div>
                
                <div class="property-actions">
                    <button id="refresh-properties-btn" class="btn btn-secondary">
                        <span class="icon">🔄</span>
                        <span data-i18n="refresh_list">Actualizar lista</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Application State -->
        <div id="main-state" class="state-container hidden">
            <!-- Navigation Tabs -->
            <nav class="tab-navigation">
                <button id="page-tab" class="tab-btn active" data-tab="page">
                    <span class="icon">📄</span>
                    <span data-i18n="this_page">Esta Página</span>
                </button>
                <button id="site-tab" class="tab-btn" data-tab="site">
                    <span class="icon">🌐</span>
                    <span data-i18n="full_site">Sitio Completo</span>
                </button>
            </nav>

            <!-- Page Analysis Tab -->
            <div id="page-analysis" class="tab-content active">
                <!-- Page Info -->
                <div class="page-info">
                    <div class="current-page">
                        <span class="icon">📍</span>
                        <span id="current-page-path">/</span>
                    </div>
                    
                    <div class="analysis-controls">
                        <div class="control-group">
                            <label for="dimension-selector" data-i18n="analyze_as">Analizar como:</label>
                            <select id="dimension-selector" class="form-select">
                                <option value="pagePath" data-i18n="page_path">Ruta de Página</option>
                                <option value="landingPage" data-i18n="landing_page">Página de Destino</option>
                                <option value="exitPage" data-i18n="exit_page">Página de Salida</option>
                                <option value="pagePathPlusQueryString" data-i18n="page_with_params">Página + Parámetros</option>
                            </select>
                            <small id="dimension-help" class="help-text">Analiza esta página específica por su URL</small>
                        </div>
                        
                        <div class="control-group">
                            <label for="page-date-range" data-i18n="period">Período:</label>
                            <select id="page-date-range" class="form-select">
                                <option value="yesterday" data-i18n="yesterday">Ayer</option>
                                <option value="7daysAgo" selected data-i18n="last_7_days">Últimos 7 días</option>
                                <option value="30daysAgo" data-i18n="last_30_days">Últimos 30 días</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Key Metrics -->
                <div id="page-metrics" class="metrics-section">
                    <h3 data-i18n="key_metrics">MÉTRICAS CLAVE</h3>
                    <div id="page-metrics-grid" class="metrics-grid">
                        <!-- Metrics will be populated dynamically -->
                    </div>
                </div>

                <!-- Traffic Sources -->
                <div id="page-traffic" class="section">
                    <h3 data-i18n="traffic_sources">FUENTES DE TRÁFICO</h3>
                    <div id="page-traffic-list" class="traffic-list">
                        <!-- Traffic sources will be populated dynamically -->
                    </div>
                </div>

                <!-- Devices -->
                <div id="page-devices" class="section">
                    <h3 data-i18n="devices">DISPOSITIVOS</h3>
                    <div id="page-devices-chart" class="devices-chart">
                        <!-- Device breakdown will be populated dynamically -->
                    </div>
                </div>
            </div>

            <!-- Site Overview Tab -->
            <div id="site-overview" class="tab-content">
                <!-- Site Info -->
                <div class="site-info">
                    <div class="property-name">
                        <span class="icon">🏠</span>
                        <span id="selected-property-name">Mi Sitio Web</span>
                    </div>
                    
                    <div class="control-group">
                        <label for="site-date-range" data-i18n="period">Período:</label>
                        <select id="site-date-range" class="form-select">
                            <option value="7daysAgo" data-i18n="last_7_days">Últimos 7 días</option>
                            <option value="28daysAgo" selected data-i18n="last_28_days">Últimos 28 días</option>
                            <option value="90daysAgo" data-i18n="last_90_days">Últimos 90 días</option>
                        </select>
                    </div>
                </div>

                <!-- General Summary -->
                <div id="site-summary" class="metrics-section">
                    <h3 data-i18n="general_summary">RESUMEN GENERAL</h3>
                    <div id="site-metrics-grid" class="metrics-grid">
                        <!-- Site metrics will be populated dynamically -->
                    </div>
                </div>

                <!-- Top Pages -->
                <div id="top-pages" class="section">
                    <h3 data-i18n="top_pages">TOP PÁGINAS</h3>
                    <div id="top-pages-list" class="pages-list">
                        <!-- Top pages will be populated dynamically -->
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button id="view-chart-btn" class="btn btn-secondary">
                    <span class="icon">📊</span>
                    <span data-i18n="view_chart">Ver gráfico</span>
                </button>
                <button id="refresh-data-btn" class="btn btn-primary">
                    <span class="icon">🔄</span>
                    <span data-i18n="refresh">Actualizar</span>
                </button>
            </div>

            <!-- Usage Info -->
            <div id="usage-info" class="usage-info">
                <div class="usage-text">
                    <span data-i18n="usage">Uso:</span>
                    <span id="usage-count">0</span>/<span id="usage-limit">50</span>
                    <span data-i18n="queries_this_month">consultas este mes</span>
                </div>
                <div class="usage-bar">
                    <div id="usage-progress" class="usage-progress" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- Error State -->
        <div id="error-state" class="state-container hidden">
            <div class="error-content">
                <div class="error-icon">⚠️</div>
                <h3 id="error-title">Error</h3>
                <p id="error-message">Ha ocurrido un error inesperado</p>
                
                <div class="error-actions">
                    <button id="retry-btn" class="btn btn-primary">
                        <span data-i18n="retry">Reintentar</span>
                    </button>
                    <button id="help-btn" class="btn btn-secondary">
                        <span data-i18n="get_help">Obtener ayuda</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Upgrade Prompt -->
        <div id="upgrade-prompt" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 data-i18n="limit_reached">Límite Alcanzado</h3>
                    <button id="close-upgrade-btn" class="close-btn">❌</button>
                </div>
                <div class="modal-body">
                    <p id="upgrade-message">Has alcanzado el límite de tu plan gratuito</p>
                    <div class="upgrade-benefits">
                        <div class="benefit">✅ <span data-i18n="unlimited_queries">Consultas ilimitadas</span></div>
                        <div class="benefit">✅ <span data-i18n="period_comparison">Comparación de períodos</span></div>
                        <div class="benefit">✅ <span data-i18n="data_export">Exportación de datos</span></div>
                        <div class="benefit">✅ <span data-i18n="advanced_filters">Filtros avanzados</span></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="upgrade-btn" class="btn btn-primary">
                        <span class="icon">⭐</span>
                        <span data-i18n="upgrade_premium">Actualizar a Premium - $9.99</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="lib/chart.min.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/config.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/cache.js"></script>
    <script src="js/state.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>
    <script src="js/dimensions.js"></script>
    <script src="js/features.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/popup.js"></script>
</body>
</html>
