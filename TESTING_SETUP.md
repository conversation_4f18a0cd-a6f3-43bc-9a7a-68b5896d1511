# GA4 Insights - Configuración para Testing Público

## 🎯 **CONFIGURACIÓN ACTUAL**

### **Credenciales Google OAuth**
- **Chrome Extension Client ID**: `308478830904-d37c5kjjba68kpa3boog6r8i464gf70a.apps.googleusercontent.com`
- **Web Application Client ID**: `308478830904-7pt651p5lepi60agv9afnmsvmcq2ic4i.apps.googleusercontent.com`
- **Client Secret**: `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`

### **URLs Configuradas**
- **Backend**: `https://pietramundodigital.com/api/v1`
- **Redirect URI**: `https://pietramundodigital.com/api/v1/auth/google/callback`

---

## 🚀 **PASOS PARA TESTING PÚBLICO**

### **1. Configurar Google Cloud Console**

#### **1.1 Chrome Extension OAuth Client**
```
Client ID: 308478830904-d37c5kjjba68kpa3boog6r8i464gf70a.apps.googleusercontent.com

Authorized JavaScript origins:
- chrome-extension://[EXTENSION-ID-GENERADO]

Nota: El Extension ID se genera automáticamente cuando cargas la extensión
```

#### **1.2 Web Application OAuth Client**
```
Client ID: 308478830904-7pt651p5lepi60agv9afnmsvmcq2ic4i.apps.googleusercontent.com

Authorized redirect URIs:
- https://pietramundodigital.com/api/v1/auth/google/callback
- https://pietramundodigital.com/auth/callback
```

### **2. Configurar Backend en Producción**

#### **2.1 Variables de Entorno (.env)**
```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=308478830904-7pt651p5lepi60agv9afnmsvmcq2ic4i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=https://pietramundodigital.com/api/v1/auth/google/callback

# Chrome Extension OAuth Configuration
CHROME_EXTENSION_CLIENT_ID=308478830904-d37c5kjjba68kpa3boog6r8i464gf70a.apps.googleusercontent.com

# CORS para extensión
CORS_ALLOWED_ORIGINS="chrome-extension://*,https://pietramundodigital.com"

# Límites para testing
FREE_PLAN_MONTHLY_LIMIT=100
RATE_LIMIT_PER_MINUTE=120
```

#### **2.2 Comandos de Deployment**
```bash
# En el servidor
cd /path/to/backend
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan migrate
php artisan db:seed --class=PlansSeeder
```

### **3. Obtener Extension ID**

#### **3.1 Cargar Extensión en Chrome**
```bash
1. Abrir Chrome
2. Ir a chrome://extensions/
3. Activar "Modo de desarrollador"
4. Clic en "Cargar extensión sin empaquetar"
5. Seleccionar carpeta del proyecto
6. Copiar el Extension ID generado (ej: abcdefghijklmnopqrstuvwxyz123456)
```

#### **3.2 Actualizar Google Cloud Console**
```bash
1. Ir a Google Cloud Console
2. APIs & Services > Credentials
3. Editar Chrome Extension OAuth Client
4. Añadir a Authorized JavaScript origins:
   chrome-extension://[EXTENSION-ID-COPIADO]
5. Guardar cambios
```

### **4. Distribuir Extensión para Testing**

#### **4.1 Empaquetar Extensión**
```bash
1. En chrome://extensions/
2. Clic en "Empaquetar extensión"
3. Seleccionar directorio de la extensión
4. Generar archivo .crx y .pem
5. Guardar ambos archivos de forma segura
```

#### **4.2 Instalar en Otros Dispositivos**
```bash
Método 1 - Archivo CRX:
1. Enviar archivo .crx a testers
2. Arrastrar archivo a chrome://extensions/
3. Confirmar instalación

Método 2 - Carpeta Descomprimida:
1. Comprimir carpeta del proyecto
2. Enviar a testers
3. Descomprimir y cargar como "extensión sin empaquetar"
```

---

## 🧪 **TESTING CHECKLIST**

### **✅ Pre-Testing**
- [ ] Backend deployado en pietramundodigital.com
- [ ] Base de datos configurada y migrada
- [ ] Variables de entorno configuradas
- [ ] SSL certificado válido
- [ ] CORS configurado para extensiones

### **✅ Google OAuth Setup**
- [ ] Ambos Client IDs configurados en Google Cloud
- [ ] Extension ID añadido a JavaScript origins
- [ ] Redirect URIs configurados correctamente
- [ ] APIs habilitadas (Analytics Data, Admin, OAuth2)

### **✅ Extensión**
- [ ] Client ID correcto en manifest.json
- [ ] Backend URL apunta a pietramundodigital.com
- [ ] Extensión cargada en Chrome
- [ ] Extension ID copiado y configurado

### **✅ Testing Funcional**
- [ ] Login con Google funciona
- [ ] Lista de propiedades GA4 se carga
- [ ] Selección de propiedad funciona
- [ ] Métricas de página se muestran
- [ ] Métricas de sitio se muestran
- [ ] Cache funciona correctamente
- [ ] Logout limpia datos

---

## 🔧 **TROUBLESHOOTING**

### **Error: "redirect_uri_mismatch"**
```
Solución:
1. Verificar que el redirect URI en Google Cloud coincida exactamente
2. Asegurar que no hay espacios o caracteres extra
3. Verificar protocolo HTTPS
```

### **Error: "origin_mismatch"**
```
Solución:
1. Verificar Extension ID en Google Cloud Console
2. Asegurar formato: chrome-extension://[EXTENSION-ID]
3. Recargar extensión si cambió el ID
```

### **Error: "invalid_client"**
```
Solución:
1. Verificar Client ID en manifest.json
2. Verificar Client ID en backend config
3. Asegurar que el Client ID es del tipo correcto (Chrome Extension)
```

### **Error: CORS**
```
Solución:
1. Verificar CORS_ALLOWED_ORIGINS incluye chrome-extension://*
2. Reiniciar servidor backend
3. Verificar headers en respuestas del servidor
```

---

## 📊 **TESTING CON DATOS REALES**

### **Cuentas GA4 Recomendadas**
```
1. Tu cuenta personal de Google Analytics
2. Cuenta de pietramundodigital.com (si tienes GA4)
3. Cuenta de demostración con datos históricos
```

### **Métricas a Verificar**
```
- Usuarios activos > 0
- Sesiones > 0  
- Vistas de página > 0
- Tasa de interacción entre 0-100%
- Fuentes de tráfico variadas
- Dispositivos (móvil, escritorio, tablet)
```

### **Páginas de Prueba**
```
1. Página principal del sitio
2. Páginas con tráfico alto
3. Páginas con tráfico bajo
4. Páginas con parámetros UTM
```

---

## 🎯 **DISTRIBUCIÓN PARA TESTERS**

### **Instrucciones para Testers**
```
1. Descargar archivo .crx o carpeta comprimida
2. Abrir Chrome
3. Ir a chrome://extensions/
4. Activar "Modo de desarrollador"
5. Arrastrar archivo .crx O cargar carpeta descomprimida
6. Confirmar instalación
7. Hacer login con cuenta de Google que tenga acceso a GA4
8. Probar funcionalidades básicas
9. Reportar cualquier error o problema
```

### **Datos a Recopilar de Testers**
```
- Versión de Chrome
- Sistema operativo
- ¿Login exitoso? (Sí/No)
- ¿Se cargan las propiedades GA4? (Sí/No)
- ¿Se muestran las métricas? (Sí/No)
- Errores encontrados (capturas de pantalla)
- Sugerencias de mejora
```

---

## 🚀 **PRÓXIMOS PASOS**

### **Después del Testing Exitoso**
1. **Pulir UI/UX** basado en feedback
2. **Optimizar performance** 
3. **Añadir funciones premium** (sin Stripe por ahora)
4. **Preparar para Chrome Web Store**
5. **Crear documentación de usuario**
6. **Configurar analytics de uso**

### **Para Publicación en Chrome Web Store**
1. **Crear cuenta de desarrollador** ($5 USD)
2. **Preparar assets** (iconos, capturas, descripción)
3. **Completar formulario** de publicación
4. **Pasar revisión** de Google (1-3 días)
5. **Publicar** y monitorear

---

## 📞 **CONTACTO PARA TESTING**

Si encuentras problemas durante el testing:
1. **Revisar logs** en DevTools de la extensión
2. **Revisar logs** del backend en pietramundodigital.com
3. **Documentar error** con capturas de pantalla
4. **Reportar** con detalles específicos

¡La extensión está lista para testing público! 🎉
