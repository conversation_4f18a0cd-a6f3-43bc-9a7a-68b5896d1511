/**
 * test-extension.js - Script de Testing Rápido para GA4 Insights
 * 
 * Ejecutar en DevTools Console de la extensión para testing rápido
 */

// Configuración de testing
const TEST_CONFIG = {
    BACKEND_URL: 'https://pietramundodigital.com/api/v1',
    GOOGLE_CLIENT_ID: '308478830904-d37c5kjjba68kpa3boog6r8i464gf70a.apps.googleusercontent.com',
    TEST_MODE: true
};

// Función para testing de autenticación
async function testAuthentication() {
    console.log('🔐 Testing Authentication...');
    
    try {
        // Verificar si hay token almacenado
        const authData = await chrome.storage.local.get(['ga4_auth_token', 'ga4_user_data']);
        
        if (authData.ga4_auth_token) {
            console.log('✅ Auth token found:', authData.ga4_auth_token.substring(0, 20) + '...');
            console.log('✅ User data:', authData.ga4_user_data);
            return true;
        } else {
            console.log('❌ No auth token found');
            return false;
        }
    } catch (error) {
        console.error('❌ Auth test failed:', error);
        return false;
    }
}

// Función para testing de backend
async function testBackend() {
    console.log('🌐 Testing Backend Connection...');
    
    try {
        const response = await fetch(`${TEST_CONFIG.BACKEND_URL.replace('/v1', '')}/health`);
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ Backend health check passed:', data);
            return true;
        } else {
            console.log('❌ Backend health check failed:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ Backend connection failed:', error);
        return false;
    }
}

// Función para testing de Google OAuth
async function testGoogleOAuth() {
    console.log('🔑 Testing Google OAuth...');
    
    try {
        // Intentar obtener token de Google
        chrome.identity.getAuthToken({ interactive: false }, (token) => {
            if (chrome.runtime.lastError) {
                console.log('❌ Google OAuth failed:', chrome.runtime.lastError.message);
                return false;
            }
            
            if (token) {
                console.log('✅ Google token obtained:', token.substring(0, 20) + '...');
                return true;
            } else {
                console.log('❌ No Google token available');
                return false;
            }
        });
    } catch (error) {
        console.error('❌ Google OAuth test failed:', error);
        return false;
    }
}

// Función para testing de storage
async function testStorage() {
    console.log('💾 Testing Storage...');
    
    try {
        // Test write
        await chrome.storage.local.set({ 'test_key': 'test_value', 'test_timestamp': Date.now() });
        
        // Test read
        const result = await chrome.storage.local.get(['test_key', 'test_timestamp']);
        
        if (result.test_key === 'test_value') {
            console.log('✅ Storage read/write test passed');
            
            // Cleanup
            await chrome.storage.local.remove(['test_key', 'test_timestamp']);
            return true;
        } else {
            console.log('❌ Storage test failed');
            return false;
        }
    } catch (error) {
        console.error('❌ Storage test failed:', error);
        return false;
    }
}

// Función para testing de cache
async function testCache() {
    console.log('🗄️ Testing Cache System...');
    
    try {
        const testData = {
            data: { test: 'cache_data', timestamp: Date.now() },
            metadata: {
                createdAt: Date.now(),
                expiresAt: Date.now() + 300000, // 5 minutes
                version: '1.0.0'
            }
        };
        
        // Test cache write
        await chrome.storage.local.set({ 'ga4_cache_test': testData });
        
        // Test cache read
        const cached = await chrome.storage.local.get('ga4_cache_test');
        
        if (cached.ga4_cache_test && cached.ga4_cache_test.data.test === 'cache_data') {
            console.log('✅ Cache system test passed');
            
            // Cleanup
            await chrome.storage.local.remove('ga4_cache_test');
            return true;
        } else {
            console.log('❌ Cache system test failed');
            return false;
        }
    } catch (error) {
        console.error('❌ Cache test failed:', error);
        return false;
    }
}

// Función para testing de configuración
function testConfiguration() {
    console.log('⚙️ Testing Configuration...');
    
    try {
        // Verificar que CONFIG existe
        if (typeof CONFIG === 'undefined') {
            console.log('❌ CONFIG object not found');
            return false;
        }
        
        // Verificar configuraciones críticas
        const criticalConfigs = [
            'BACKEND_URL',
            'GOOGLE_CLIENT_ID',
            'API.TIMEOUT',
            'CACHE.TTL'
        ];
        
        let allConfigsValid = true;
        
        criticalConfigs.forEach(configPath => {
            const value = configPath.split('.').reduce((obj, key) => obj?.[key], CONFIG);
            if (!value) {
                console.log(`❌ Missing config: ${configPath}`);
                allConfigsValid = false;
            } else {
                console.log(`✅ Config found: ${configPath} = ${value}`);
            }
        });
        
        return allConfigsValid;
    } catch (error) {
        console.error('❌ Configuration test failed:', error);
        return false;
    }
}

// Función para testing de permisos
function testPermissions() {
    console.log('🔒 Testing Permissions...');
    
    try {
        const requiredPermissions = ['identity', 'storage', 'activeTab'];
        let allPermissionsGranted = true;
        
        requiredPermissions.forEach(permission => {
            chrome.permissions.contains({ permissions: [permission] }, (result) => {
                if (result) {
                    console.log(`✅ Permission granted: ${permission}`);
                } else {
                    console.log(`❌ Permission missing: ${permission}`);
                    allPermissionsGranted = false;
                }
            });
        });
        
        return allPermissionsGranted;
    } catch (error) {
        console.error('❌ Permissions test failed:', error);
        return false;
    }
}

// Función para limpiar datos de testing
async function cleanupTestData() {
    console.log('🧹 Cleaning up test data...');
    
    try {
        const keysToRemove = [];
        const allData = await chrome.storage.local.get(null);
        
        Object.keys(allData).forEach(key => {
            if (key.includes('test_') || key.includes('_test')) {
                keysToRemove.push(key);
            }
        });
        
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            console.log(`✅ Cleaned up ${keysToRemove.length} test keys`);
        } else {
            console.log('✅ No test data to clean up');
        }
    } catch (error) {
        console.error('❌ Cleanup failed:', error);
    }
}

// Función principal de testing
async function runAllTests() {
    console.log('🚀 Starting GA4 Insights Extension Tests...');
    console.log('================================================');
    
    const results = {
        configuration: testConfiguration(),
        permissions: testPermissions(),
        storage: await testStorage(),
        cache: await testCache(),
        backend: await testBackend(),
        authentication: await testAuthentication()
    };
    
    console.log('================================================');
    console.log('📊 Test Results Summary:');
    
    let passedTests = 0;
    let totalTests = Object.keys(results).length;
    
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} - ${test}`);
        if (passed) passedTests++;
    });
    
    console.log('================================================');
    console.log(`🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Extension is ready for testing.');
    } else {
        console.log('⚠️ Some tests failed. Check the issues above.');
    }
    
    // Cleanup
    await cleanupTestData();
    
    return results;
}

// Función para testing manual de autenticación
async function testManualAuth() {
    console.log('🔐 Starting manual authentication test...');
    
    try {
        // Forzar login interactivo
        chrome.identity.getAuthToken({ interactive: true }, async (token) => {
            if (chrome.runtime.lastError) {
                console.error('❌ Manual auth failed:', chrome.runtime.lastError.message);
                return;
            }
            
            console.log('✅ Google token obtained:', token.substring(0, 20) + '...');
            
            // Verificar token con backend
            try {
                const response = await fetch(`${TEST_CONFIG.BACKEND_URL}/auth/verify-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        googleAccessToken: token
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    console.log('✅ Backend authentication successful:', data.data);
                } else {
                    console.error('❌ Backend authentication failed:', data);
                }
            } catch (error) {
                console.error('❌ Backend verification failed:', error);
            }
        });
    } catch (error) {
        console.error('❌ Manual auth test failed:', error);
    }
}

// Exportar funciones para uso manual
window.GA4_TEST = {
    runAllTests,
    testAuthentication,
    testBackend,
    testGoogleOAuth,
    testStorage,
    testCache,
    testConfiguration,
    testPermissions,
    testManualAuth,
    cleanupTestData
};

console.log('🧪 GA4 Insights Test Suite Loaded!');
console.log('📝 Available commands:');
console.log('   GA4_TEST.runAllTests() - Run all tests');
console.log('   GA4_TEST.testManualAuth() - Test manual authentication');
console.log('   GA4_TEST.cleanupTestData() - Clean up test data');
console.log('');
console.log('🚀 Run GA4_TEST.runAllTests() to start testing!');
