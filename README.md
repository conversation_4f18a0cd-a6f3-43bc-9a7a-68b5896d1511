# GA4 Insights - Chrome Extension

Una extensión de Chrome que permite analizar el rendimiento de Google Analytics 4 directamente desde cualquier página web.

## 🚀 Características

### ✨ Funcionalidades Principales
- **Análisis de página actual**: Métricas específicas de la página que estás visitando
- **Resumen del sitio completo**: Vista general de todo el sitio web
- **Dimensiones personalizables**: Analiza por ruta de página, página de destino, página de salida, etc.
- **Fuentes de tráfico**: Visualiza de dónde vienen tus visitantes
- **Análisis de dispositivos**: Desglose por móvil, escritorio y tablet
- **Métricas en tiempo real**: Datos actualizados de Google Analytics 4

### 🎯 Métricas Disponibles
- Usuarios activos
- Usuarios nuevos
- Sesiones
- Vistas de página
- Tasa de interacción
- Tasa de rebote
- Duración promedio de sesión
- Conversiones
- Ingresos

### 📊 Planes Disponibles
- **Plan Gratuito**: 50 consultas por mes
- **Plan Premium**: Consultas ilimitadas + funciones avanzadas

## 🛠️ Instalación

### Requisitos Previos
1. Google Chrome (versión 88 o superior)
2. Cuenta de Google Analytics 4 configurada
3. Permisos de lectura en las propiedades GA4

### Instalación desde Chrome Web Store
1. Visita [Chrome Web Store - GA4 Insights](https://chrome.google.com/webstore)
2. Haz clic en "Agregar a Chrome"
3. Confirma los permisos necesarios
4. ¡Listo! El icono aparecerá en tu barra de herramientas

### Instalación para Desarrollo
1. Clona este repositorio:
   ```bash
   git clone https://github.com/tu-usuario/ga4-insights-extension.git
   cd ga4-insights-extension
   ```

2. Abre Chrome y ve a `chrome://extensions/`

3. Activa el "Modo de desarrollador" (esquina superior derecha)

4. Haz clic en "Cargar extensión sin empaquetar"

5. Selecciona la carpeta del proyecto

6. La extensión se cargará y aparecerá en tu barra de herramientas

## ⚙️ Configuración

### Primera Configuración
1. Haz clic en el icono de GA4 Insights en tu barra de herramientas
2. Haz clic en "Conectar con Google Analytics"
3. Autoriza los permisos necesarios (solo lectura)
4. Selecciona la propiedad GA4 que deseas analizar
5. ¡Comienza a analizar!

### Configuración de Google OAuth
Para desarrolladores que quieran configurar su propia instancia:

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la API de Google Analytics Data
4. Crea credenciales OAuth 2.0:
   - Tipo: Aplicación web
   - Orígenes autorizados: `chrome-extension://[tu-extension-id]`
5. Copia el Client ID y actualiza `manifest.json`

## 🎮 Uso

### Análisis de Página Actual
1. Navega a cualquier página de tu sitio web
2. Haz clic en el icono de GA4 Insights
3. Selecciona la pestaña "Esta Página"
4. Elige cómo analizar la página:
   - **Ruta de Página**: Análisis específico de esta URL
   - **Página de Destino**: Sesiones que empezaron aquí
   - **Página de Salida**: Sesiones que terminaron aquí
5. Selecciona el período de tiempo
6. Visualiza las métricas y fuentes de tráfico

### Análisis del Sitio Completo
1. Haz clic en la pestaña "Sitio Completo"
2. Selecciona el período de tiempo
3. Visualiza:
   - Resumen general de métricas
   - Top páginas más visitadas
   - Tendencias temporales

### Funciones Avanzadas (Plan Premium)
- Comparación de períodos
- Exportación de datos
- Filtros avanzados
- Reportes por email

## 🔧 Desarrollo

### Estructura del Proyecto
```
ga4-insights-extension/
├── manifest.json              # Configuración de la extensión
├── popup.html                 # Interfaz principal
├── background.js              # Service worker
├── css/
│   ├── popup.css             # Estilos principales
│   └── components.css        # Componentes reutilizables
├── js/
│   ├── popup.js              # Controlador principal
│   ├── auth.js               # Autenticación OAuth
│   ├── api.js                # Comunicación con APIs
│   ├── state.js              # Gestión de estado
│   ├── cache.js              # Sistema de cache
│   ├── features.js           # Procesamiento de datos GA4
│   ├── ui.js                 # Manipulación de DOM
│   ├── utils.js              # Utilidades
│   ├── config.js             # Configuración
│   ├── logger.js             # Sistema de logging
│   ├── i18n.js               # Internacionalización
│   └── dimensions.js         # Gestión de dimensiones
├── icons/                    # Iconos de la extensión
├── docs/                     # Documentación
└── README.md
```

### Scripts de Desarrollo
```bash
# Instalar dependencias (si las hay)
npm install

# Ejecutar tests
npm test

# Construir para producción
npm run build

# Empaquetar extensión
npm run package
```

### APIs Utilizadas
- **Google Analytics Data API v1**: Para obtener datos de GA4
- **Google Analytics Admin API v1**: Para listar propiedades
- **Chrome Identity API**: Para autenticación OAuth
- **Chrome Storage API**: Para persistencia local

## 🔒 Privacidad y Seguridad

### Datos Recopilados
- **Tokens de autenticación**: Almacenados localmente, nunca enviados a terceros
- **Datos de GA4**: Solo lectura, procesados localmente
- **Logs de uso**: Solo para debugging, anonimizados

### Permisos Necesarios
- `identity`: Para autenticación con Google
- `storage`: Para cache local
- `activeTab`: Para obtener URL de la página actual
- `https://*.googleapis.com/*`: Para APIs de Google Analytics

### Seguridad
- Comunicación HTTPS exclusivamente
- Tokens JWT con expiración automática
- Validación de entrada en todas las APIs
- Sanitización de datos para prevenir XSS

## 🐛 Solución de Problemas

### Problemas Comunes

**Error: "No se encontraron propiedades GA4"**
- Verifica que tengas acceso de lectura a propiedades GA4
- Asegúrate de que las propiedades sean GA4 (no Universal Analytics)

**Error: "Límite de consultas alcanzado"**
- Has alcanzado el límite de tu plan gratuito (50 consultas/mes)
- Considera actualizar al plan premium

**Error: "Error de autenticación"**
- Cierra sesión y vuelve a autenticarte
- Verifica que los permisos estén otorgados correctamente

**La extensión no muestra datos**
- Verifica que estés en una página de tu sitio web
- Asegúrate de que GA4 esté configurado correctamente
- Comprueba que haya datos en el período seleccionado

### Debugging
1. Abre las herramientas de desarrollador (F12)
2. Ve a la pestaña "Console"
3. Busca mensajes de "[GA4 Insights]"
4. Reporta cualquier error en GitHub Issues

## 🤝 Contribuir

### Cómo Contribuir
1. Fork el repositorio
2. Crea una rama para tu feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit tus cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crea un Pull Request

### Guías de Contribución
- Sigue las convenciones de código existentes
- Agrega tests para nuevas funcionalidades
- Actualiza la documentación según sea necesario
- Asegúrate de que todos los tests pasen

## 📄 Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 🆘 Soporte

### Documentación
- [Guía de Usuario Completa](https://pietramundodigital.com/ga4-insights/docs)
- [API Reference](https://pietramundodigital.com/ga4-insights/api)
- [FAQ](https://pietramundodigital.com/ga4-insights/faq)

### Contacto
- **Email**: <EMAIL>
- **GitHub Issues**: [Reportar un problema](https://github.com/tu-usuario/ga4-insights-extension/issues)
- **Sitio Web**: [https://pietramundodigital.com/ga4-insights](https://pietramundodigital.com/ga4-insights)

## 🎉 Agradecimientos

- Google Analytics Team por las APIs
- Chrome Extensions Team por la plataforma
- Comunidad de desarrolladores por feedback y contribuciones

---

**¿Te gusta GA4 Insights?** ⭐ ¡Danos una estrella en GitHub y compártelo con otros desarrolladores!
