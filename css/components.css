/* GA4 Insights - Component Styles */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-sm);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  min-height: 36px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.btn-primary:hover:not(:disabled) {
  background-color: #1557b0;
  border-color: #1557b0;
}

.btn-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-secondary);
}

.btn-success {
  background-color: var(--success-green);
  color: white;
  border-color: var(--success-green);
}

.btn-success:hover:not(:disabled) {
  background-color: #2d8f47;
}

.btn-warning {
  background-color: var(--warning-orange);
  color: var(--text-primary);
  border-color: var(--warning-orange);
}

.btn-warning:hover:not(:disabled) {
  background-color: #e8ab02;
}

.btn-danger {
  background-color: var(--error-red);
  color: white;
  border-color: var(--error-red);
}

.btn-danger:hover:not(:disabled) {
  background-color: #d33b2c;
}

.btn-link {
  background: none;
  color: var(--primary-blue);
  border: none;
  text-decoration: underline;
  padding: var(--spacing-xs);
}

.btn-link:hover:not(:disabled) {
  color: #1557b0;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 16px;
  min-height: 44px;
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 12px;
  min-height: 28px;
}

.btn-icon {
  font-size: 16px;
}

/* Property Items */
.property-item {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.property-item:hover {
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-sm);
}

.property-item.selected {
  border-color: var(--primary-blue);
  background-color: rgba(26, 115, 232, 0.05);
}

.property-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.property-header h4 {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.property-id {
  font-size: 11px;
  color: var(--text-muted);
  font-family: monospace;
  background-color: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.property-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.property-details span {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.property-status {
  display: flex;
  justify-content: flex-end;
}

.status-badge {
  font-size: 11px;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.status-available {
  background-color: rgba(52, 168, 83, 0.1);
  color: var(--success-green);
}

.status-limited {
  background-color: rgba(251, 188, 4, 0.1);
  color: var(--warning-orange);
}

.status-error {
  background-color: rgba(234, 67, 53, 0.1);
  color: var(--error-red);
}

/* Traffic Sources */
.traffic-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.traffic-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
}

.traffic-source {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex: 1;
}

.traffic-source .icon {
  font-size: 14px;
}

.traffic-source .name {
  font-size: 13px;
  color: var(--text-primary);
  font-weight: 500;
}

.traffic-metrics {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.traffic-users {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.traffic-percentage {
  font-size: 11px;
  color: var(--text-secondary);
}

.traffic-bar {
  width: 100px;
  height: 4px;
  background-color: var(--border-light);
  border-radius: 2px;
  overflow: hidden;
  margin-top: var(--spacing-xs);
}

.traffic-bar-fill {
  height: 100%;
  background-color: var(--primary-blue);
  border-radius: 2px;
  transition: width var(--transition-normal);
}

/* Device Chart */
.devices-chart {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
}

.device-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.device-info .icon {
  font-size: 16px;
}

.device-info .name {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.device-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.device-count {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 40px;
  text-align: right;
}

.device-percentage {
  font-size: 12px;
  color: var(--text-secondary);
  min-width: 35px;
  text-align: right;
}

.device-bar {
  width: 80px;
  height: 6px;
  background-color: var(--border-light);
  border-radius: 3px;
  overflow: hidden;
}

.device-bar-fill {
  height: 100%;
  border-radius: 3px;
  transition: width var(--transition-normal);
}

.device-bar-fill.mobile {
  background-color: var(--primary-blue);
}

.device-bar-fill.desktop {
  background-color: var(--success-green);
}

.device-bar-fill.tablet {
  background-color: var(--warning-orange);
}

/* Pages List */
.pages-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.page-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.page-item:hover {
  background-color: var(--bg-secondary);
}

.page-info {
  flex: 1;
  min-width: 0;
}

.page-path {
  font-size: 13px;
  color: var(--text-primary);
  font-weight: 500;
  font-family: monospace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.page-title {
  font-size: 11px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.page-metrics {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.page-views {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
}

.page-engagement {
  font-size: 11px;
  color: var(--text-secondary);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal.hidden {
  display: none;
}

.modal-content {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  width: 90%;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.close-btn:hover {
  background-color: var(--bg-secondary);
}

.modal-body {
  padding: var(--spacing-md);
}

.modal-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

/* Upgrade Benefits */
.upgrade-benefits {
  margin: var(--spacing-md) 0;
}

.benefit {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin: var(--spacing-xs) 0;
  font-size: 13px;
  color: var(--text-secondary);
}

/* No Data State */
.no-data {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.no-data .icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.no-data h3 {
  font-size: 16px;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.no-data p {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}
