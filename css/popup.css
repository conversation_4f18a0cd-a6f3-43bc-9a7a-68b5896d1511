/* GA4 Insights - Main Popup Styles */

/* CSS Variables */
:root {
  /* Colors */
  --primary-blue: #1a73e8;
  --success-green: #34a853;
  --warning-orange: #fbbc04;
  --error-red: #ea4335;
  
  /* Metric colors */
  --metric-positive: #34a853;
  --metric-negative: #ea4335;
  --metric-neutral: #5f6368;
  
  /* Background colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-card: #ffffff;
  --border-light: #e8eaed;
  --border-medium: #dadce0;
  
  /* Text colors */
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-muted: #9aa0a6;
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* Border radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  
  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
  
  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  overflow-x: hidden;
}

/* Popup Container */
.popup-container {
  width: 420px;
  min-height: 500px;
  max-height: 700px;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

/* Header */
.popup-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-md);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo {
  width: 24px;
  height: 24px;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.icon-btn {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.icon-btn:hover {
  background-color: var(--bg-secondary);
}

.icon-btn .icon {
  font-size: 16px;
}

/* State Containers */
.state-container {
  padding: var(--spacing-lg);
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.state-container.hidden {
  display: none;
}

/* Loading State */
.loading-content {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: var(--border-light);
  border-radius: 2px;
  margin: var(--spacing-md) 0;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-blue);
  border-radius: 2px;
  transition: width var(--transition-normal);
  width: 0%;
}

.loading-steps {
  text-align: left;
  margin-top: var(--spacing-md);
}

.loading-steps .step {
  color: var(--text-secondary);
  font-size: 12px;
  margin: var(--spacing-xs) 0;
  opacity: 0.6;
  transition: opacity var(--transition-fast);
}

.loading-steps .step.active {
  opacity: 1;
  color: var(--primary-blue);
}

/* Authentication State */
.auth-content {
  text-align: center;
}

.auth-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.auth-content h2 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.auth-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  line-height: 1.5;
}

.auth-security {
  margin: var(--spacing-lg) 0;
  text-align: left;
}

.security-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: var(--spacing-sm) 0;
  font-size: 13px;
  color: var(--text-secondary);
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.tab-btn {
  flex: 1;
  padding: var(--spacing-md);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  font-size: 14px;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.tab-btn.active {
  background-color: var(--bg-primary);
  color: var(--primary-blue);
  border-bottom-color: var(--primary-blue);
}

.tab-btn .icon {
  font-size: 16px;
}

/* Tab Content */
.tab-content {
  display: none;
  padding: var(--spacing-md);
}

.tab-content.active {
  display: block;
}

/* Page Info */
.page-info {
  margin-bottom: var(--spacing-lg);
}

.current-page {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-sm);
  font-family: monospace;
  font-size: 13px;
}

.analysis-controls {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.control-group label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-select {
  padding: var(--spacing-sm);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-sm);
  background-color: var(--bg-primary);
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.form-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.help-text {
  font-size: 11px;
  color: var(--text-muted);
  line-height: 1.3;
}

/* Sections */
.section {
  margin-bottom: var(--spacing-lg);
}

.section h3 {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-light);
}

/* Metrics */
.metrics-section {
  margin-bottom: var(--spacing-lg);
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
}

.metric-card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: box-shadow var(--transition-fast);
}

.metric-card:hover {
  box-shadow: var(--shadow-sm);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.metric-icon {
  font-size: 16px;
}

.metric-name {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.metric-value {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-value .value {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.trend {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.trend-positive {
  color: var(--metric-positive);
  background-color: rgba(52, 168, 83, 0.1);
}

.trend-negative {
  color: var(--metric-negative);
  background-color: rgba(234, 67, 53, 0.1);
}

.trend-neutral {
  color: var(--metric-neutral);
  background-color: rgba(95, 99, 104, 0.1);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
  margin: var(--spacing-lg) 0;
  padding: 0 var(--spacing-md);
}

/* Usage Info */
.usage-info {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
}

.usage-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.usage-bar {
  height: 4px;
  background-color: var(--border-light);
  border-radius: 2px;
  overflow: hidden;
}

.usage-progress {
  height: 100%;
  background-color: var(--primary-blue);
  border-radius: 2px;
  transition: width var(--transition-normal);
}

/* Error State */
.error-content {
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 400px) {
  .popup-container {
    width: 380px;
  }
  
  .analysis-controls {
    flex-direction: column;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
