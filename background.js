/**
 * background.js - Service Worker for GA4 Insights Extension
 * 
 * Handles background tasks, alarms, and extension lifecycle events.
 * Manages cache cleanup, token refresh, and other maintenance tasks.
 */

// Extension installation and update handling
chrome.runtime.onInstalled.addListener((details) => {
    console.log('[GA4 Insights] Extension installed/updated', details);
    
    if (details.reason === 'install') {
        handleFirstInstall();
    } else if (details.reason === 'update') {
        handleUpdate(details.previousVersion);
    }
});

// Extension startup
chrome.runtime.onStartup.addListener(() => {
    console.log('[GA4 Insights] Extension started');
    initializeBackground();
});

/**
 * Handle first installation
 */
async function handleFirstInstall() {
    try {
        console.log('[GA4 Insights] First installation detected');
        
        // Set up default preferences
        await chrome.storage.local.set({
            'ga4_first_install': Date.now(),
            'ga4_version': chrome.runtime.getManifest().version
        });
        
        // Create welcome alarm
        chrome.alarms.create('welcome_reminder', {
            delayInMinutes: 1
        });
        
        // Set up periodic maintenance
        setupMaintenanceAlarms();
        
        console.log('[GA4 Insights] First installation setup completed');
        
    } catch (error) {
        console.error('[GA4 Insights] First installation setup failed:', error);
    }
}

/**
 * Handle extension update
 */
async function handleUpdate(previousVersion) {
    try {
        console.log('[GA4 Insights] Extension updated', { 
            from: previousVersion, 
            to: chrome.runtime.getManifest().version 
        });
        
        // Update version in storage
        await chrome.storage.local.set({
            'ga4_last_update': Date.now(),
            'ga4_previous_version': previousVersion,
            'ga4_version': chrome.runtime.getManifest().version
        });
        
        // Perform migration if needed
        await performMigration(previousVersion);
        
        // Clear old cache after update
        await clearOldCache();
        
        console.log('[GA4 Insights] Update handling completed');
        
    } catch (error) {
        console.error('[GA4 Insights] Update handling failed:', error);
    }
}

/**
 * Initialize background service worker
 */
async function initializeBackground() {
    try {
        console.log('[GA4 Insights] Initializing background service worker');
        
        // Set up maintenance alarms
        setupMaintenanceAlarms();
        
        // Clean up expired data
        await performMaintenance();
        
        console.log('[GA4 Insights] Background initialization completed');
        
    } catch (error) {
        console.error('[GA4 Insights] Background initialization failed:', error);
    }
}

/**
 * Set up periodic maintenance alarms
 */
function setupMaintenanceAlarms() {
    // Cache cleanup every hour
    chrome.alarms.create('cache_cleanup', {
        periodInMinutes: 60
    });
    
    // Token refresh check every 30 minutes
    chrome.alarms.create('token_refresh', {
        periodInMinutes: 30
    });
    
    // Usage reset check daily
    chrome.alarms.create('usage_check', {
        periodInMinutes: 24 * 60 // 24 hours
    });
    
    // General maintenance every 6 hours
    chrome.alarms.create('general_maintenance', {
        periodInMinutes: 6 * 60 // 6 hours
    });
    
    console.log('[GA4 Insights] Maintenance alarms set up');
}

/**
 * Handle alarm events
 */
chrome.alarms.onAlarm.addListener(async (alarm) => {
    console.log('[GA4 Insights] Alarm triggered:', alarm.name);
    
    try {
        switch (alarm.name) {
            case 'cache_cleanup':
                await performCacheCleanup();
                break;
                
            case 'token_refresh':
                await checkTokenRefresh();
                break;
                
            case 'usage_check':
                await checkUsageReset();
                break;
                
            case 'general_maintenance':
                await performMaintenance();
                break;
                
            case 'welcome_reminder':
                await showWelcomeNotification();
                break;
                
            default:
                console.log('[GA4 Insights] Unknown alarm:', alarm.name);
        }
    } catch (error) {
        console.error('[GA4 Insights] Alarm handler failed:', alarm.name, error);
    }
});

/**
 * Perform cache cleanup
 */
async function performCacheCleanup() {
    try {
        console.log('[GA4 Insights] Starting cache cleanup');
        
        const allData = await chrome.storage.local.get(null);
        const now = Date.now();
        const keysToRemove = [];
        
        // Find expired cache entries
        Object.keys(allData).forEach(key => {
            if (key.startsWith('ga4_cache_')) {
                const entry = allData[key];
                if (entry && entry.metadata && now > entry.metadata.expiresAt) {
                    keysToRemove.push(key);
                }
            }
        });
        
        // Remove expired entries
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            console.log('[GA4 Insights] Cache cleanup completed:', keysToRemove.length, 'entries removed');
        } else {
            console.log('[GA4 Insights] No expired cache entries found');
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Cache cleanup failed:', error);
    }
}

/**
 * Check if token needs refresh
 */
async function checkTokenRefresh() {
    try {
        console.log('[GA4 Insights] Checking token refresh');
        
        const authData = await chrome.storage.local.get([
            'ga4_auth_token',
            'ga4_token_expiry'
        ]);
        
        const token = authData.ga4_auth_token;
        const expiry = authData.ga4_token_expiry;
        
        if (!token || !expiry) {
            console.log('[GA4 Insights] No auth token found');
            return;
        }
        
        const now = Date.now();
        const timeUntilExpiry = expiry - now;
        const oneHour = 60 * 60 * 1000;
        
        // If token expires within an hour, try to refresh
        if (timeUntilExpiry < oneHour && timeUntilExpiry > 0) {
            console.log('[GA4 Insights] Token expires soon, attempting refresh');
            await attemptTokenRefresh();
        } else if (timeUntilExpiry <= 0) {
            console.log('[GA4 Insights] Token expired, clearing auth data');
            await clearExpiredAuth();
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Token refresh check failed:', error);
    }
}

/**
 * Attempt to refresh authentication token
 */
async function attemptTokenRefresh() {
    try {
        // Try to get a fresh Google token
        chrome.identity.getAuthToken({ interactive: false }, async (token) => {
            if (chrome.runtime.lastError || !token) {
                console.log('[GA4 Insights] Could not refresh token silently');
                return;
            }
            
            console.log('[GA4 Insights] Google token refreshed successfully');
            
            // Note: In a real implementation, you would verify this token
            // with your backend and update the stored JWT token
        });
        
    } catch (error) {
        console.error('[GA4 Insights] Token refresh failed:', error);
    }
}

/**
 * Clear expired authentication data
 */
async function clearExpiredAuth() {
    try {
        await chrome.storage.local.remove([
            'ga4_auth_token',
            'ga4_token_expiry',
            'ga4_user_data',
            'ga4_user_data_expiry'
        ]);
        
        console.log('[GA4 Insights] Expired auth data cleared');
        
    } catch (error) {
        console.error('[GA4 Insights] Failed to clear expired auth:', error);
    }
}

/**
 * Check if usage counters need reset
 */
async function checkUsageReset() {
    try {
        console.log('[GA4 Insights] Checking usage reset');
        
        const userData = await chrome.storage.local.get('ga4_user_data');
        const user = userData.ga4_user_data;
        
        if (!user || !user.usage) {
            console.log('[GA4 Insights] No user usage data found');
            return;
        }
        
        const now = new Date();
        const resetDate = new Date(user.usage.resetDate);
        
        if (now >= resetDate) {
            console.log('[GA4 Insights] Usage reset date reached');
            // Note: In a real implementation, this would sync with the backend
            // to reset usage counters
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Usage reset check failed:', error);
    }
}

/**
 * Perform general maintenance
 */
async function performMaintenance() {
    try {
        console.log('[GA4 Insights] Starting general maintenance');
        
        // Clean up old logs
        await cleanupOldLogs();
        
        // Clean up temporary data
        await cleanupTempData();
        
        // Check storage usage
        await checkStorageUsage();
        
        console.log('[GA4 Insights] General maintenance completed');
        
    } catch (error) {
        console.error('[GA4 Insights] General maintenance failed:', error);
    }
}

/**
 * Clean up old debug logs
 */
async function cleanupOldLogs() {
    try {
        const logData = await chrome.storage.local.get([
            'ga4_debug_logs',
            'ga4_debug_logs_expiry'
        ]);
        
        const logs = logData.ga4_debug_logs;
        const expiry = logData.ga4_debug_logs_expiry;
        
        if (logs && expiry && Date.now() > expiry) {
            await chrome.storage.local.remove([
                'ga4_debug_logs',
                'ga4_debug_logs_expiry'
            ]);
            console.log('[GA4 Insights] Old debug logs cleaned up');
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Log cleanup failed:', error);
    }
}

/**
 * Clean up temporary data
 */
async function cleanupTempData() {
    try {
        const allData = await chrome.storage.local.get(null);
        const keysToRemove = [];
        
        // Find temporary keys that should be cleaned up
        Object.keys(allData).forEach(key => {
            if (key.includes('_temp_') || key.includes('_tmp_')) {
                keysToRemove.push(key);
            }
        });
        
        if (keysToRemove.length > 0) {
            await chrome.storage.local.remove(keysToRemove);
            console.log('[GA4 Insights] Temporary data cleaned up:', keysToRemove.length, 'items');
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Temp data cleanup failed:', error);
    }
}

/**
 * Check storage usage and warn if approaching limits
 */
async function checkStorageUsage() {
    try {
        const usage = await chrome.storage.local.getBytesInUse();
        const limit = chrome.storage.local.QUOTA_BYTES || (5 * 1024 * 1024); // 5MB default
        const usagePercentage = (usage / limit) * 100;
        
        console.log('[GA4 Insights] Storage usage:', Math.round(usagePercentage) + '%', `(${usage}/${limit} bytes)`);
        
        if (usagePercentage > 80) {
            console.warn('[GA4 Insights] Storage usage high:', usagePercentage + '%');
            // Could trigger additional cleanup here
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Storage usage check failed:', error);
    }
}

/**
 * Show welcome notification for new users
 */
async function showWelcomeNotification() {
    try {
        const installData = await chrome.storage.local.get('ga4_first_install');
        
        if (installData.ga4_first_install) {
            // Check if user has already been welcomed
            const welcomed = await chrome.storage.local.get('ga4_welcomed');
            
            if (!welcomed.ga4_welcomed) {
                // Mark as welcomed
                await chrome.storage.local.set({ 'ga4_welcomed': true });
                
                console.log('[GA4 Insights] Welcome notification triggered');
                // Note: Could show a notification here if needed
            }
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Welcome notification failed:', error);
    }
}

/**
 * Perform data migration between versions
 */
async function performMigration(previousVersion) {
    try {
        console.log('[GA4 Insights] Performing migration from version:', previousVersion);
        
        // Example migration logic
        if (previousVersion && previousVersion < '0.1.0') {
            // Migrate old data format to new format
            console.log('[GA4 Insights] Migrating from pre-0.1.0 version');
        }
        
        console.log('[GA4 Insights] Migration completed');
        
    } catch (error) {
        console.error('[GA4 Insights] Migration failed:', error);
    }
}

/**
 * Clear old cache after updates
 */
async function clearOldCache() {
    try {
        const allData = await chrome.storage.local.get(null);
        const cacheKeys = Object.keys(allData).filter(key => key.startsWith('ga4_cache_'));
        
        if (cacheKeys.length > 0) {
            await chrome.storage.local.remove(cacheKeys);
            console.log('[GA4 Insights] Old cache cleared after update:', cacheKeys.length, 'entries');
        }
        
    } catch (error) {
        console.error('[GA4 Insights] Old cache cleanup failed:', error);
    }
}

// Handle extension messages (if needed for communication with popup)
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('[GA4 Insights] Background message received:', message);
    
    // Handle specific message types
    switch (message.type) {
        case 'FORCE_CACHE_CLEANUP':
            performCacheCleanup().then(() => {
                sendResponse({ success: true });
            }).catch(error => {
                sendResponse({ success: false, error: error.message });
            });
            return true; // Keep message channel open for async response
            
        case 'GET_STORAGE_STATS':
            checkStorageUsage().then(() => {
                sendResponse({ success: true });
            }).catch(error => {
                sendResponse({ success: false, error: error.message });
            });
            return true;
            
        default:
            sendResponse({ success: false, error: 'Unknown message type' });
    }
});

console.log('[GA4 Insights] Background service worker loaded');
