## 2. Herramientas y Formatos de Documentos (Instrucciones para IA)

* **PROMPT: Utilizar Control de Versiones**
    * **INSTRUCCIÓN:** Utiliza Git para todo el control de código fuente.
    * **INSTRUCCIÓN:** Configura un repositorio remoto en GitHub (o similar) para colaboración y backup.
* **PROMPT: Utilizar IDE**
    * **INSTRUCCIÓN:** El entorno de desarrollo principal será Visual Studio Code.
    * **INSTRUCCIÓN:** Instala y configura extensiones para HTML, CSS, JavaScript, ESLint, Prettier, y el lenguaje/framework backend seleccionado.
* **PROMPT: Especificaciones del Frontend**
    * **INSTRUCCIÓN:** Desarrolla el frontend utilizando HTML5, CSS3, y JavaScript (ES6+).
    * **INSTRUCCIÓN:** El archivo `manifest.json` debe seguir la especificación de Chrome Manifest V3.
    * **INSTRUCCIÓN:** Para gráficos, utiliza la biblioteca Chart.js.
* **PROMPT: Especificaciones del Backend**
    * **INSTRUCCIÓN:** Desarrolla el backend utilizando [Lenguaje/Framework seleccionado en Fase 4, ej. Node.js con Express.js].
* **PROMPT: Especificaciones de la Base de Datos**
    * **INSTRUCCIÓN:** Utiliza [Motor de BD seleccionado en Fase 4, ej. Firebase Firestore].
* **PROMPT: Interacción con APIs de Google**
    * **INSTRUCCIÓN:** Utiliza Google Identity Platform (OAuth 2.0) para la autenticación.
    * **INSTRUCCIÓN:** Interactúa con la Google Analytics Data API v1 para obtener informes.
    * **INSTRUCCIÓN:** Interactúa con la Google Analytics Admin API v1 para listar cuentas/propiedades/flujos.
* **PROMPT: Herramientas de Diseño UI/UX**
    * **INSTRUCCIÓN:** Genera los wireframes y mockups utilizando Figma (o una descripción textual detallada si Figma no es una opción directa para la IA).
    * **INSTRUCCIÓN:** Utiliza diagramas de flujo (generados como texto o con una herramienta compatible) para la arquitectura y flujos de usuario.
* **PROMPT: Formato de Documentos de Planificación**
    * **INSTRUCCIÓN:** Toda la documentación de planificación, requisitos y especificaciones debe generarse en formato Markdown (`.md`).